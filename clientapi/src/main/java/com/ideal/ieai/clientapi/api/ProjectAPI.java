package com.ideal.ieai.clientapi.api;

import com.ideal.ieai.clientapi.APIException;
import com.ideal.ieai.communication.Session;
import com.ideal.ieai.communication.marshall.Parameter;
import com.ideal.ieai.communication.marshall.Request;
import com.ideal.ieai.communication.marshall.Response;
import com.ideal.ieai.communication.marshall.transfer.interfaces.APIService;

/**
 * operation on project and adapor api
 * 
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company: Ideal Technologies, Inc.
 * </p>
 * 
 * <AUTHOR> modified by <PERSON>
 * @version 3.0
 */

public class ProjectAPI
{
    private ProjectAPI()
    {
    }

    /**
     * Create a new project.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_INVALID_PRJADP_NAME
     * <li> ERR_PRJADP_EXIST
     * <li> ERR_PACK_PRJADP_TO_PKG
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param PrjName project's name
     * @param Desc project's description
     * @param Comment project's comment
     * @param DesPwd the designer password
     * @param DevPwd the developer password
     * @return Response.returnData is not used
     * @throws APIException when error during create a new project
     */
    // public static final Response createProject(
    // Session sess,
    // String PrjName,
    // String Desc,
    // String Comment,
    // boolean IsProtected,
    // String DesPwd,
    // String DevPwd) throws
    // APIException
    // {
    //
    // String sessionId = sess.getSessId();
    //
    // //get all params
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.P_CREATE_PROJ_PROJ_NAME, PrjName);
    // Parameter param3 = new Parameter(APIService.P_CREATE_PROJ_DESC, Desc);
    // Parameter param4 = new Parameter(APIService.P_CREATE_PROJ_COMMENT, Comment);
    // Parameter param5 = new Parameter(APIService.P_CREATE_PROJ_IS_PROTECTED,
    // Boolean.valueOf(IsProtected));
    // Parameter param6 = new Parameter(APIService.P_CREATE_PROJ_DESIGNER_PWD, DesPwd);
    // Parameter param7 = new Parameter(APIService.P_CREATE_PROJ_DEVELOPER_PWD, DevPwd);
    //
    // Request req = new Request(sessionId, APIService.S_CREATE_PROJ);
    //
    // //add to request
    // req.addParameter(param1);
    // req.addParameter(param2);
    // req.addParameter(param3);
    // req.addParameter(param4);
    // req.addParameter(param5);
    // req.addParameter(param6);
    // req.addParameter(param7);
    // return APIHelper.processResponse(sess.call(req));
    //
    // }
    /**
     * Create a new adaptor.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_INVALID_PRJADP_NAME
     * <li> ERR_PRJADP_EXIST
     * <li> ERR_PACK_PRJADP_TO_PKG
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param AdpName name of an adaptor
     * @param Desc description of adaptor
     * @param Comment comment of adaptor
     * @return Response.returnData is not used
     * @throws APIException exception when error in response
     */
    // public static final Response createAdaptor(Session sess,
    // String AdpName,
    // String Desc,
    // String Comment) throws
    // APIException
    // {
    //
    // String sessionId = sess.getSessId();
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.P_CREATE_ADP_ADP_NAME, AdpName);
    // Parameter param3 = new Parameter(APIService.P_CREATE_ADP_DESC, Desc);
    // Parameter param4 = new Parameter(APIService.P_CREATE_ADP_COMMENT, Comment);
    //
    // //idit by xu ming :fix a bug 2003.12.15
    // Request req = new Request(sessionId, APIService.S_CREATE_ADP);
    // req.addParameter(param1);
    // req.addParameter(param2);
    // req.addParameter(param3);
    // req.addParameter(param4);
    //
    // return APIHelper.processResponse(sess.call(req));
    // }
    /**
     * Getting a project for editing, system will lock this project till this user updates it or
     * relase lock on it.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * </ul>
     * 
     * @param sess session
     * @return Response.returnData is a byte[] of the .pkg file
     * @throws APIException when error in response
     */
    // public static final Response editProject(Session sess,
    // String PrjName) throws
    // APIException
    // {
    //
    // String sessionId = sess.getSessId();
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.P_EDIT_PROJ_PROJ_NAME, PrjName);
    //
    // Request req = new Request(sessionId, APIService.S_EDIT_PROJ);
    // req.addParameter(param1);
    // req.addParameter(param2);
    //
    // return APIHelper.processResponse(sess.call(req));
    // }
    /**
     * Getting an adaptor for editing, system will lock this adaptor till this user updates it or
     * relase lock on it.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param AdpName adaptor's name
     * @return Response.returnData is a byte[] of the .pkg file
     * @throws APIException error in reponse
     */
    // public static final Response editAdaptor(Session sess,
    // String AdpName) throws
    // APIException
    // {
    //
    // String sessionId = sess.getSessId();
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.P_EDIT_ADP_ADP_NAME, AdpName);
    //
    // Request req = new Request(sessionId, APIService.S_EDIT_ADP);
    // req.addParameter(param1);
    // req.addParameter(param2);
    //
    // return APIHelper.processResponse(sess.call(req));
    //
    // }
    /**
     * Return all projects this user has some permissions with.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param Connector
     * @return Response returnData is a List of ProjectInfo objects
     * @throws APIException
     */
    public static final Response getRelatedProject ( Session sess ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);

        Request req = new Request(sessionId, APIService.S_GET_RELATED_PROJ);
        req.addParameter(param1);

        return APIHelper.processResponse(sess.call(req));
    }

    /**
     * Return all adaptors this user has some permissions with.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param Connector
     * @return Response returnData is a List of AdaptorInfo objects.
     * @throws APIException
     */
    public static final Response getRelatedAdaptor ( Session sess ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);

        Request req = new Request(sessionId, APIService.S_GET_RELATED_ADP);
        req.addParameter(param1);

        return APIHelper.processResponse(sess.call(req));
    }

    /**
     * Return all projects.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @return Response returnData is a List of ProjectInfo objects
     * @throws APIException error in response
     */
    public static final Response getUAProjectList ( Session sess ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);

        Request req = new Request(sessionId, APIService.S_GET_UA_PROJ_LIST);
        req.addParameter(param1);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Return all adaptors.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @return Response returnData is a List of AdaptorInfo objects.
     * @throws APIException error in response
     */
    public static final Response getUAAdaptorList ( Session sess ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);

        Request req = new Request(sessionId, APIService.S_GET_UA_ADP_LIST);
        req.addParameter(param1);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Delete a project.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param PrjName project name
     * @return Response.returnData not used
     * @throws APIException error in response
     */
    public static final Response deleteProject ( Session sess, String PrjName ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_DEL_PROJ_PROJ_NAME, PrjName);

        Request req = new Request(sessionId, APIService.S_DEL_PROJ);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    public static final Response getPartInfo ( Session sess, String PrjName, String flwName )
            throws APIException
    {
        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_GET_PART_PROJ_NAME, PrjName);
        Parameter param3 = new Parameter(APIService.P_GET_PART_FLOW_NAME, flwName);

        Request req = new Request(sessionId, APIService.S_GET_PART);
        req.addParameter(param1);
        req.addParameter(param2);
        req.addParameter(param3);

        return APIHelper.processResponse(sess.call(req));
    }

    /**
     * Delete an adaptor.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param AdpName adaptor 's name
     * @return Response.returnData is a byte[] of the .pkg file
     * @throws APIException errro in response
     */
    public static final Response deleteAdaptor ( Session sess, String AdpName ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_DEL_ADP_ADP_NAME, AdpName);

        Request req = new Request(sessionId, APIService.S_DEL_ADP);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Download a project.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param PrjName project name
     * @return Response.returnData is a byte[] of the .pkg file
     * @throws APIException error in response
     */
    public static final Response downloadProject ( Session sess, String PrjName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_DOWNLOAD_PROJ_PROJ_NAME, PrjName);

        Request req = new Request(sessionId, APIService.S_DOWNLOAD_PROJ);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Download an adaptor.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param session
     * @param AdpName adaptor's name
     * @return Response.returnData is a byte[] of the .pkg file
     * @throws APIException error in response
     */
    public static final Response downloadAdaptor ( Session sess, String AdpName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_DOWNLOAD_ADP_ADP_NAME, AdpName);

        Request req = new Request(sessionId, APIService.S_DOWNLOAD_ADP);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));
    }

    /**
     * Freeze a project, system will freeze this project till someone unfreezes it.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_PRJADP_FROZEN
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param PrjName project's name
     * @return Response.returnData is not used
     * @throws APIException error in response
     */
    public static final Response freezeProject ( Session sess, String PrjName ) throws APIException
    {
        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_FREEZE_PROJ_PROJ_NAME, PrjName);

        Request req = new Request(sessionId, APIService.S_FREEZE_PROJ);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Freeze an adaptor, system will freeze this adaptor till someone unfreezes it.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_PRJADP_FROZEN
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param AdpName adaptor's name
     * @return Response.returnData is not used
     * @throws APIException error in response
     */
    public static final Response freezeAdaptor ( Session sess, String AdpName ) throws APIException
    {
        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_FREEZE_ADP_ADP_NAME, AdpName);

        Request req = new Request(sessionId, APIService.S_FREEZE_ADP);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));
    }

    /**
     * Unfreeze a project.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param PrjName project's name
     * @return Response.returnData is not used
     * @throws APIException when error in response
     */
    public static final Response unfreezeProject ( Session sess, String PrjName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_UNFREEZE_PROJ_PROJ_NAME, PrjName);

        Request req = new Request(sessionId, APIService.S_UNFREEZE_PROJ);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Release the lock on a project.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param session the session
     * @param prjName project name
     * @return response from server
     * @throws APIException when error in response
     */
    // public static final Response releaseEditLockOnProject(Session
    // sess,
    // String PrjName) throws APIException
    // {
    //
    // String sessionId = sess.getSessId();
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.
    // P_RELEASE_EDIT_LOCK_ON_PROJ_PROJ_NAME, PrjName);
    //
    // Request req = new Request(sessionId, APIService.S_RELEASE_EDIT_LOCK_ON_PROJ);
    // req.addParameter(param1);
    // req.addParameter(param2);
    //
    // return APIHelper.processResponse(sess.call(req));
    // }
    /**
     * Release the lock on a project.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param AdaptorName
     * @return response from server
     * @throws APIException when error in response
     */
    // public static final Response releaseEditLockOnAdaptor(Session sess,
    // String AdaptorName) throws APIException
    // {
    //
    // String sessionId = sess.getSessId();
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.
    // P_RELEASE_EDIT_LOCK_ON_ADP_ADP_NAME, AdaptorName);
    //
    // Request req = new Request(sessionId, APIService.S_RELASE_EDIT_LOCK_ON_ADP);
    // req.addParameter(param1);
    // req.addParameter(param2);
    //
    // return APIHelper.processResponse(sess.call(req));
    // }
    /**
     * Unfreeze an adaptor.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess the session
     * @param AdpName adaptor 's name
     * @return Response.returnData is not used
     * @throws APIException when error in response
     */
    public static final Response unfreezeAdaptor ( Session sess, String AdpName )
            throws APIException
    {
        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_UNFREEZE_ADP_ADP_NAME, AdpName);

        Request req = new Request(sessionId, APIService.S_UNFREEZE_ADP);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Update a project, system will unlock this project.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_INVALID_PRJADP_NAME
     * <li> ERR_DB_ERROR
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_NOT_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * </ul>
     * 
     * @param sess session
     * @param PrjName project's name
     * @return Response.returnData is not used
     * @throws APIException when error in response
     */
    public static final Response updateProject ( Session sess, String PrjName, String Comment,
            byte[] pkg ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_UPDATE_PROJ_PROJ_NAME, PrjName);
        // Parameter param3 = new Parameter(APIService.P_UPDATE_PROJ_MAJ_VER,
        // Boolean.valueOf(MajVersion));
        Parameter param4 = new Parameter(APIService.P_UPDATE_PROJ_COMMENT, Comment);
        Parameter param5 = new Parameter(APIService.P_UPDATE_PROJ_PKG, pkg);

        Request req = new Request(sessionId, APIService.S_UPDATE_PROJ);
        req.addParameter(param1);
        req.addParameter(param2);
        // req.addParameter(param3);
        req.addParameter(param4);
        req.addParameter(param5);

        return APIHelper.processResponse(sess.call(req));

    }

    public static final Response importProjectXml ( Session sess, String userName, short projectType, String xml )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_UPDATE_PROJ_PROJ_USERNAME, userName);
        Parameter param3 = new Parameter(APIService.P_UPDATE_PROJ_PROJ_TYPE, projectType);
        Parameter param4 = new Parameter(APIService.P_UPDATE_PROJ_PROJ_XML, xml);

        Request req = new Request(sessionId, APIService.S_UPDATE_PROJ_XML);
        req.addParameter(param1);
        req.addParameter(param2);
        req.addParameter(param3);
        req.addParameter(param4);

        return APIHelper.processResponse(sess.call(req));

    }

    // 添加检测上传的工程名是否存在的方法 update by tao_ding on 2009-03-23
    public static final Response existProject ( Session sess, String PrjName ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_DETECT_EXIST_PROJ_PROJ_NAME, PrjName);

        Request req = new Request(sessionId, APIService.S_DETECT_PROJ);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }
    
    /**
     * 获取目标工程最新的版本的uuid
     * @param sess
     * @param PrjName
     * @return
     * @throws APIException
     */
    public static final Response getLastPrjUuidByPrjName ( Session sess, String PrjName ) throws APIException
    {
    	
    	String sessionId = sess.getSessId();
    	Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    	Parameter param2 = new Parameter(APIService.P_UPDATE_PROJ_PROJ_NAME, PrjName);
    	
    	Request req = new Request(sessionId, APIService.S_UPLOAD_GETUUID);
    	req.addParameter(param1);
    	req.addParameter(param2);
    	
    	return APIHelper.processResponse(sess.call(req));
    	
    }
    
    /**
     * 根据uuid获取对应版本的工程
     * @param sess
     * @param PrjName
     * @return
     * @throws APIException
     */
    public static final Response downPrjByUuid ( Session sess, String uuid ) throws APIException
    {
    	
    	String sessionId = sess.getSessId();
    	Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    	Parameter param2 = new Parameter(APIService.P_DOWNLOAD_PROJ_UUID, uuid);
    	
    	Request req = new Request(sessionId, APIService.S_DOWNLOAD_PROJ_UUID);
    	req.addParameter(param1);
    	req.addParameter(param2);
    	
    	return APIHelper.processResponse(sess.call(req));
    	
    }

    
    
    
    /**
     * Upload a project. <br>
     * If project not exist, system will create it (user need to have PERM_CREATE_PROJECT)<br>
     * If project exists, system will unlock and update it (user need to have PERM_UPDATE_PROJECT)
     * <br>
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_INVALID_PRJADP_NAME
     * <li> ERR_DB_ERROR
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_NOT_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * </ul>
     * 
     * @param sess session
     * @param PrjName project's name
     * @return Response.returnData is not used
     * @throws APIException when error in response
     */

    // public static final Response uploadProject(Session sess,
    // String PrjName,
    // boolean MajVersion,
    // String Comment,
    // byte[] pkg) throws APIException
    // {
    //
    // String sessionId = sess.getSessId();
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.P_UPLOAD_PROJ_PROJ_NAME, PrjName);
    //
    // Parameter param4 = new Parameter(APIService.P_UPLOAD_PROJ_CMT, Comment);
    // Parameter param3 = new Parameter(APIService.P_UPLOAD_PROJ_ISPROTECTED,
    // Boolean.valueOf(MajVersion));
    // Parameter param5 = new Parameter(APIService.P_UPLOAD_PROJ_PKG, pkg);
    //
    // Request req = new Request(sessionId, APIService.S_UPLOAD_PROJ);
    // req.addParameter(param1);
    // req.addParameter(param2);
    // req.addParameter(param3);
    // req.addParameter(param4);
    // req.addParameter(param5);
    //
    // return APIHelper.processResponse(sess.call(req));
    //
    // }
    /**
     * Upload an adaptor. <br>
     * If adaptor not exist, system will create it (user need to have PERM_CREATE_ADAPTOR)<br>
     * If adaptor exists, system will unlock and update it (user need to have PERM_UPDATE_ADAPTOR)
     * <br>
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_INVALID_PRJADP_NAME
     * <li> ERR_DB_ERROR
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_NOT_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * </ul>
     * 
     * @param sess the session
     * @param AdpName adaptor 's name
     * @param MajVersion major version
     * @param Comment comment on adaptor
     * @param pkg package
     * @return Response.returnData is not used
     * @throws APIException when error in response
     */
    // public static final Response uploadAdaptor(Session sess,
    // String AdpName,
    // boolean MajVersion,
    // // String Desc,
    // String Comment,
    // byte[] pkg
    // ) throws APIException
    // {
    //
    // String sessionId = sess.getSessId();
    // Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
    // Parameter param2 = new Parameter(APIService.P_UPLOAD_ADP_ADP_NAME, AdpName);
    // Parameter param4 = new Parameter(APIService.P_UPLOAD_ADP_CMT, Comment);
    // Parameter param3 = new Parameter(APIService.P_UPLOAD_ADP_ISPROTECTED,
    // Boolean.valueOf(MajVersion));
    // Parameter param5 = new Parameter(APIService.P_UPLOAD_ADP_PKG, pkg);
    //
    // Request req = new Request(sessionId, APIService.S_UPLOAD_ADP);
    // req.addParameter(param1);
    // req.addParameter(param2);
    // req.addParameter(param3);
    // req.addParameter(param4);
    // req.addParameter(param5);
    //
    // return APIHelper.processResponse(sess.call(req));
    // }
    /**
     * Update an adaptor, system will unlock this adaptor.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_INVALID_PRJADP_NAME
     * <li> ERR_DB_ERROR
     * <li> ERR_PRJADP_LOCKED
     * <li> ERR_PRJADP_NOT_LOCKED
     * <li> ERR_PRJADP_FROZEN
     * </ul>
     * 
     * @param sess the session
     * @param AdpName adaptor's name
     * @param MajVersion adaptpors's major version
     * @param Comment comment on adaptor
     * @param pkg pkgs of adaptor
     * @return Response.returnData is not used
     * @throws APIException when error in response
     */
    public static final Response updateAdaptor ( Session sess, String AdpName, String Comment,
            byte[] pkg ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_UPDATE_ADP_ADP_NAME, AdpName);

        Parameter param4 = new Parameter(APIService.P_UPDATE_ADP_COMMENT, Comment);
        // Parameter param3 = new Parameter(APIService.P_UPDATE_ADP_MAJ_VER,
        // Boolean.valueOf(MajVersion));
        Parameter param5 = new Parameter(APIService.P_UPDATE_ADP_PKG, pkg);

        Request req = new Request(sessionId, APIService.S_UPDATE_ADP);
        req.addParameter(param1);
        req.addParameter(param2);
        // req.addParameter(param3);
        req.addParameter(param4);
        req.addParameter(param5);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Return a project's version information history.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess the session
     * @param PrjName project's name
     * @return Response.returnData is a List of VersionHistory objects
     * @throws APIException when error in response
     */
    public static final Response getProjectVerHistory ( Session sess, String PrjName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_GET_PROJ_VER_HISTORY_PROJ_NAME, PrjName);

        Request req = new Request(sessionId, APIService.S_GET_PROJ_VER_HISTORY);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Return a project's log information history.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess the session
     * @param PrjName project's name
     * @return Response.returnData is a List of LogHistory object
     * @throws APIException when error in it
     */
    public static final Response getProjectLogHistory ( Session sess, String PrjName )
            throws APIException
    {
        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_GET_PROJ_LOG_HISTORY_PROJ_NAME, PrjName);

        Request req = new Request(sessionId, APIService.S_GET_PROJ_LOG_HISTORY);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Return an adaptor's version information history.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param AdpName adaptor's name
     * @return Response.returnData is a List of VersionHistory objects
     * @throws APIException when error in response
     */
    public static final Response getAdaptorVerHistory ( Session sess, String AdpName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_GET_ADP_VER_HISTORY_ADP_NAME, AdpName);

        Request req = new Request(sessionId, APIService.S_GET_ADP_VER_HISTORY);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * Return an adaptor's log information history.
     * 
     * <br>
     * Possible Errors:
     * <ul>
     * <li> ERR_PRJADP_NOT_EXIST
     * <li> ERR_DB_ERROR
     * </ul>
     * 
     * @param sess session
     * @param AdpName adaptor's name
     * @return Response.returnData is a List of LogHistory objects
     * @throws APIException error in response
     */
    public static final Response getAdaptorLogHistory ( Session sess, String AdpName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_GET_ADP_LOG_HISTORY_ADP_NAME, AdpName);

        Request req = new Request(sessionId, APIService.S_GET_ADP_LOG_HISTORY);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));

    }

    /**
     * <li>Description:获取Excel上传的工程列表</li>
     * 
     * <AUTHOR> 2015年7月23日
     * @param sess
     * @return
     * @throws APIException return Response
     */
    public static final Response getExcelProject ( Session sess ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);

        Request req = new Request(sessionId, APIService.S_GET_RELATED_PROJ_STUDIO);
        req.addParameter(param1);

        return APIHelper.processResponse(sess.call(req));
    }

    /**
     * <li>Description:获取Excel上传的工作流</li>
     * 
     * <AUTHOR> 2015年7月23日
     * @param sess
     * @param prjs
     * @return
     * @throws APIException return Response
     */
    public static final Response getExcelFlows ( Session sess, String prjs ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_PRJ_NAME, prjs);

        Request req = new Request(sessionId, APIService.S_GET_RELATED_FLOW_STUDIO);
        req.addParameter(param1);
        req.addParameter(param2);

        return APIHelper.processResponse(sess.call(req));
    }
    
    /**
     * 
     * @Title: getProjectActRelyName   
     * @Description: 获取活动依赖关系   
     * @param sess
     * @param ActName
     * @return
     * @throws APIException      
     * @author: licheng_zhao 
     * @date:   2019年1月7日 下午4:05:15
     */
    final static public Response getProjectActRelyName ( Session sess,String ActName ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.ACT_NAME, ActName);

        Request req = new Request(sessionId, APIService.S_GET_RELY_ACT_NAME);
        req.addParameter(param1);
        req.addParameter(param2);
        return APIHelper.processResponse(sess.call(req));
    }
    
    
    /**
     * 
     * @Title: getProjectActPri   
     * @Description: 获取活动优先级  
     * @param sess
     * @param ActName
     * @return
     * @throws APIException      
     * @author: licheng_zhao 
     * @date:   2019年1月7日 下午4:05:36
     */
    final static public Response getProjectActPri ( Session sess,String ActName ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.ACT_NAME, ActName);

        Request req = new Request(sessionId, APIService.S_GET_ACT_PRI);
        req.addParameter(param1);
        req.addParameter(param2);
        return APIHelper.processResponse(sess.call(req));
    }
    
    /**
     * 
     * @Title: getProjectActName   
     * @Description: 获取活动名称   
     * @param sess
     * @param sysName
     * @param ActName
     * @return
     * @throws APIException      
     * @author: licheng_zhao 
     * @date:   2019年1月7日 下午4:05:55
     */
    final static public Response getProjectActName ( Session sess,String sysName,String ActName ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.ACT_NAME, ActName);
        Parameter param3 = new Parameter(APIService.SYS_NAME, sysName);
        
        Request req = new Request(sessionId, APIService.S_GET_ACT_NAME);
        req.addParameter(param1);
        req.addParameter(param2);
        req.addParameter(param3);
        return APIHelper.processResponse(sess.call(req));
    }

    /**
     * @Title: getProjectActPriByPrj
     * @Description: 根据工程名工作流名活动名获取活动优先级
     * @param sess
     * @param prjName
     * @param flowName
     * @param actName
     * @return
     * @throws APIException
     * @author: txl
     * @date:   2019年7月4日 下午7:03:32
     */
    public static final Response getProjectActPriByPrj ( Session sess, String prjName, String flowName, String actName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.PRJ_NAME, prjName);
        Parameter param3 = new Parameter(APIService.FLOW_NAME, flowName);
        Parameter param4 = new Parameter(APIService.ACT_NAME, actName);

        Request req = new Request(sessionId, APIService.S_GET_ACT_PRI_PRJ);
        req.addParameter(param1);
        req.addParameter(param2);
        req.addParameter(param3);
        req.addParameter(param4);
        return APIHelper.processResponse(sess.call(req));
    }


    /**
     *
     * @Title: checkUuidForUploadPrj
     * @Description: 添加检测工程uuid是否相同
     * @param sess
     * @param sysName
     * @param ActName
     * @return
     * @throws APIException
     * @author: licheng_zhao
     * @date:   2019年1月7日 下午4:05:55
     */
    public static final Response checkUuidForUploadPrj ( Session sess, String prjName, String uuid ) throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.P_PRJ_NAME, prjName);
        Parameter param3 = new Parameter(APIService.P_PRJ_NAME, uuid);

        Request req = new Request(sessionId, APIService.S_UPLOAD_CHECKUUID);
        req.addParameter(param1);
        req.addParameter(param2);
        req.addParameter(param3);
        return APIHelper.processResponse(sess.call(req));
    }
    /**
     * @Title: getRelyActNameByPrj
     * @Description: 取一个活动的所有依赖
     * @param sess
     * @param prjName
     * @param flowName
     * @param ActName
     * @return
     * @throws APIException
     * @author: xinglintian
     * @date:   2019年7月11日 下午1:29:51
     */
    public static final Response getRelyActNameByPrj ( Session sess, String prjName, String flowName, String ActName )
            throws APIException
    {

        String sessionId = sess.getSessId();
        Parameter param1 = new Parameter(APIService.SESSION_ID, sessionId);
        Parameter param2 = new Parameter(APIService.PRJ_NAME, prjName);
        Parameter param3 = new Parameter(APIService.FLOW_NAME, flowName);
        Parameter param4 = new Parameter(APIService.ACT_NAME, ActName);

        Request req = new Request(sessionId, APIService.S_GET_RELY_ACT_NAME_BYPTJ);
        req.addParameter(param1);
        req.addParameter(param2);
        req.addParameter(param3);
        req.addParameter(param4);
        return APIHelper.processResponse(sess.call(req));
    }
}
