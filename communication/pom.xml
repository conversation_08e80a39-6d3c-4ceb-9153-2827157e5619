<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ideal.ieai</groupId>
  <artifactId>communication</artifactId>
  <version>0.1-SNAPSHOT</version>
  <parent>
		<groupId>com.ideal</groupId>
		<artifactId>entegorboot-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath />
	</parent>
  
	<dependencies>
  		<dependency>
            <groupId>com.ideal.ieai</groupId>
            <artifactId>idealutils</artifactId>
            <version>0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.ideal.ieai</groupId>
            <artifactId>core</artifactId>
            <version>0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>..</directory>
                <includes>
                    <include>version.txt</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
		<plugins>
        <plugin>
           <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
            </configuration>
      	</plugin>
    	</plugins>
	</build>
</project>