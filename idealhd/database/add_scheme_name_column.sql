-- 为drill_results表添加scheme_name字段
-- 执行时间：请在部署前执行此脚本

-- 检查字段是否已存在，如果不存在则添加
DO $$
BEGIN
    -- 检查scheme_name字段是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'drill_results' 
        AND column_name = 'scheme_name'
    ) THEN
        -- 添加scheme_name字段
        ALTER TABLE drill_results 
        ADD COLUMN scheme_name VARCHAR(100) NOT NULL DEFAULT '未命名方案';
        
        -- 添加注释
        COMMENT ON COLUMN drill_results.scheme_name IS '演练方案名称';
        
        -- 创建索引以提高查询性能
        CREATE INDEX idx_drill_results_scheme_name ON drill_results(scheme_name);
        
        RAISE NOTICE '成功添加scheme_name字段到drill_results表';
    ELSE
        RAISE NOTICE 'scheme_name字段已存在，跳过添加';
    END IF;
END $$;

-- 如果是MySQL数据库，请使用以下脚本：
/*
-- 检查并添加scheme_name字段（MySQL版本）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE drill_results ADD COLUMN scheme_name VARCHAR(100) NOT NULL DEFAULT ''未命名方案'' COMMENT ''演练方案名称'' AFTER id',
        'SELECT ''scheme_name字段已存在'' as message'
    )
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'drill_results'
    AND column_name = 'scheme_name'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建索引（如果字段是新添加的）
CREATE INDEX IF NOT EXISTS idx_drill_results_scheme_name ON drill_results(scheme_name);
*/

-- 验证字段添加成功
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns
WHERE table_name = 'drill_results'
AND column_name = 'scheme_name';
