<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>idealhd</artifactId>
	<version>0.1-SNAPSHOT</version>
	<packaging>jar</packaging>
	<parent>
		<groupId>com.ideal</groupId>
		<artifactId>entegorboot-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath />
	</parent>
	<!--  
	打包说明：
	
	jar包：
	1. <packaging>jar</packaging>
	2. 根据当前内容容器 选择打开 “Tomcat jar ” 段引用或“宝兰德jar” 段引用 
	3. profile 为 dev
	4. \install\entegor\template
	5. 其中\install\entegor\template\start.cmd 为启动脚本，修改其中workspace_loc路径即可
	
	war包：
	1. 确保关闭“Tomcat jar ” 段引用 与 “宝兰德jar” 段引用引用
	2. profile 为 war
	3. war包生成位置为target 下 ROOT.war
	4.解压war包，后放在容器中间件，webapp中覆盖容器中(webservice 原因，必须放在根路径下)
	5. tomcat 配置环境变量（其他容器原理相同）
	catalina.bat中添加如下代码
	set workspace_loc=D:\bld
	set "JAVA_OPTS=%JAVA_OPTS% -DIEAI_HOME=%workspace_loc%\ieai_home -DWS_WEBAPPS=%workspace_loc%\server -DSM_HOME=%workspace_loc%\servermanager\sm -DUT_HOME=%workspace_loc%\usertask\usertasks -DHD_HOME=%workspace_loc%\idealhd\src\main\webapp -DSUS_HOME=%workspace_loc%\ieaisusauto\susauto -DLOG_HOME=%workspace_loc%\systemselect\sel -DCM_HOME=%workspace_loc%\ieaicmauto\cmauto -DMX_HOME=%workspace_loc%\imxgraph\webEditor -Dlog4j2.contextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -DLOG4J_CONFIGURATION_FILE=%workspace_loc%\ieai_home\config\serverLog4j2.xml -Xms2048m -Xmx4096m -XX:PermSize=256M  -XX:MaxPermSize=512M"
	
-->
	<properties>
		<project.build.sourceEncoding>GBK</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<failOnMissingWebXml>false</failOnMissingWebXml>
	</properties>

	<dependencies>
		<dependency>
	        <groupId>org.springframework.boot</groupId>
	        <artifactId>spring-boot-starter</artifactId>
	        <exclusions>
	            <exclusion>
	                <groupId>org.springframework.boot</groupId>
	                <artifactId>spring-boot-starter-logging</artifactId>
	            </exclusion>
	        </exclusions>
	    </dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>asm</artifactId>
					<groupId>org.ow2.asm</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jakarta.activation-api</artifactId>
					<groupId>jakarta.activation</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.tomcat.embed</groupId>
					<artifactId>tomcat-embed-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.tomcat.embed</groupId>
					<artifactId>tomcat-embed-el</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.tomcat.embed</groupId>
					<artifactId>tomcat-embed-jasper</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.tomcat.embed</groupId>
					<artifactId>tomcat-embed-websocket</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<groupId>ideal</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				 <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
			</exclusions>
		</dependency>
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <!--
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
		</dependency>
		-->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
		</dependency>
		<dependency>
			<groupId>taglibs</groupId>
			<artifactId>standard</artifactId>
		</dependency>
		<dependency>
			<groupId>p6spy</groupId>
			<artifactId>p6spy</artifactId>
			<version>3.9.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2</artifactId>
			<version>2.0.0</version>
			<type>pom</type>
		</dependency>
		<!--
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        -->
		<!--
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.9.1</version>
		</dependency>
		-->
		<!-- https://mvnrepository.com/artifact/org.springframework/spring-mock -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-mock</artifactId>
		</dependency>
		<!--
		<dependency>
			<groupId>com.github.penggle</groupId>
			<artifactId>kaptcha</artifactId>
			<version>2.3.2</version>
			<exclusions>
				<exclusion>
					<artifactId>javax.servlet-api</artifactId>
					<groupId>javax.servlet</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		-->
		<dependency>
			<groupId>org.apache.axis</groupId>
			<artifactId>axis</artifactId>
		</dependency>
		
		<!--
		<dependency>
			<groupId>axis</groupId>
			<artifactId>axis-jaxrpc</artifactId>
			<version>1.4</version>
		</dependency>
		-->
		<dependency>
		    <groupId>org.apache.axis</groupId>
		    <artifactId>axis-jaxrpc</artifactId>
		</dependency>
		<dependency>
			<groupId>wsdl4j</groupId>
			<artifactId>wsdl4j</artifactId>
		</dependency>
		
		<dependency>
	        <groupId>javax.jms</groupId>
	        <artifactId>javax.jms-api</artifactId>
    	</dependency>
		
		
		<!--  for springboot add begin-->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
			<optional>true</optional> 
		</dependency>
		
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>javax.servlet.jsp-api</artifactId>
			<scope>provided</scope>
			<optional>true</optional> 
		</dependency>
		<dependency>
  			<groupId>idealgd</groupId>
			<artifactId>security-base</artifactId>
		</dependency>
		<!--  for springboot add end-->
		
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>idealutils</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>communication</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
			<!-- for springboot add begin-->
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
			<!-- for springboot add end-->
			
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>core</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>commonadaptor</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
			<!-- for springboot add begin-->
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
			<!-- for springboot add end-->
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>server</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
			<!-- for springboot add begin-->
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>jstl</artifactId>
				</exclusion>
			</exclusions>
			<!-- for springboot add end-->
		</dependency>

		<dependency>
			<groupId>ideal</groupId>
			<artifactId>minxing-java-sdk</artifactId>
		</dependency>
		<!-- META-INF/spring.components -->
		<!-- for springboot remove 屏蔽META-INF/spring.components -->
		

		<dependency>
			<groupId>ideal</groupId>
			<artifactId>opsapi</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>ESBClientAPI</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>cssparser</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/fr.opensagres.xdocreport/org.apache.poi.xwpf.converter.core -->
		<dependency>
			<groupId>fr.opensagres.xdocreport</groupId>
			<artifactId>org.apache.poi.xwpf.converter.core</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/fr.opensagres.xdocreport/org.apache.poi.xwpf.converter.xhtml -->
		<dependency>
			<groupId>fr.opensagres.xdocreport</groupId>
			<artifactId>org.apache.poi.xwpf.converter.xhtml</artifactId>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/com.opencsv/opencsv -->
		<dependency>
   			<groupId>com.opencsv</groupId>
   			<artifactId>opencsv</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/antlr/antlr -->
		<dependency>
			<groupId>antlr</groupId>
			<artifactId>antlr</artifactId>
		</dependency>

		<dependency>
			<groupId>ideal</groupId>
			<artifactId>batik-all</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jodconverter</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>ycas-client-core</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>saml-api-sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>junrar</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>java-unrar</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>interface</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		
		<!-- for springboot  add begin -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
	
		</dependency>
		<!-- for springboot  add end -->
		
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.github.penggle</groupId>
			<artifactId>kaptcha</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>javax.servlet-api</artifactId>
					<groupId>javax.servlet</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.jhlabs</groupId>
			<artifactId>filters</artifactId>
		</dependency>
		<dependency>
			<groupId>cebbank</groupId>
			<artifactId>caspclient</artifactId>
		</dependency>
		<dependency>
			<groupId>CIB</groupId>
			<artifactId>cap_cas</artifactId>
		</dependency>
		<dependency>
			<groupId>CIB</groupId>
			<artifactId>cas_client</artifactId>
		</dependency>
		<dependency>
			<groupId>CIB</groupId>
			<artifactId>cas_clientcore</artifactId>
		</dependency>
		<dependency>
		  <groupId>idealgd</groupId>
		  <artifactId>auth_api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ideal</groupId>
			<artifactId>ieai-tmt-middle</artifactId>
			<version>0.0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>

		<!-- 微服务生成token -->
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
			<version>3.22.0</version>
		</dependency>
		<!-- jwt -->
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.4.1</version>
		</dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.7.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<includeScope>compile</includeScope>
							<outputDirectory>${currentVer}/lib</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>*.yml</exclude>
						<exclude>*.properties</exclude>
						<exclude>*.xml</exclude>
						<exclude>*.docx</exclude>
						<exclude>*.txt</exclude>
						<exclude>liquibase/**</exclude>
						<exclude>mapper/**</exclude>
						<exclude>META-INF/**</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.3.5.RELEASE</version>
				<configuration>
					<mainClass>com.ideal.entegorboot.EntergoBooterApplication</mainClass>
					<layout>ZIP</layout>
					<includes>
						<include>
							<groupId>non-exists</groupId>
							<artifactId>non-exists</artifactId>
						</include>
					</includes>
					<fork>true</fork>
					<outputDirectory>${currentVer}/lib</outputDirectory>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
