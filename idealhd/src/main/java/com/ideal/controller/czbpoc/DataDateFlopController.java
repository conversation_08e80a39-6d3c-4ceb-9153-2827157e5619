package com.ideal.controller.czbpoc;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.repository.czbpoc.DataDateFlopManager;
import com.ideal.service.czbpoc.DataDateFlopService;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 数据日期翻牌控制器
 */
@Controller
public class DataDateFlopController {

    static Logger              logger  = Logger.getLogger(DataDateFlopController.class);
    public static final String SUCCESS = "success";
    public static final String MESSAGE = "message";

    @RequestMapping("dataDateFlop.do")
    public String dataDateFlop (HttpServletRequest request) throws ServerException
    {
        return "czbpoc/datadateflop";
    }
    @RequestMapping("saveDataDateFlop1111.do")
    @ResponseBody
    public Map<String, String> saveDataDateFlop1111 (int dataDate,int hour,int minute,int effective)
    {
        Map resp = new HashMap();
        resp.put("success", "true");
        resp.put("message", "保存成功");
        DataDateFlopManager aom = new DataDateFlopManager();
        try
        {
            Date nowdate= new Date();
            Long date = nowdate.getTime();
            String flopTime = hour+":"+minute;
            List list = aom.updateDataDateFlop(dataDate, date, flopTime,effective);
            if (list != null && !list.isEmpty())
            {
                Map map = (Map) list.get(0);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date dt = new Date(Long.parseLong(String.valueOf(map.get("updateTime"))));
                String sDateTime = sdf.format(dt);
                resp.put("updateTime", sDateTime);
                resp.put("dataDate", String.valueOf(map.get("dataDate")));
                resp.put("hour", map.get("hour"));
                resp.put("minute", map.get("minute"));
                resp.put("effective", String.valueOf(map.get("effective")));
            }
        } catch (Exception e)
        {
            resp.put("success", "true");
            resp.put("message", "保存失败");
            logger.error("saveDataDateFlop is error ", e);
        }
        return resp;
    }


    @RequestMapping("getDataDateFlopList.do")
    @ResponseBody
    public Map getDataDateFlopList ( Integer start, Integer limit, String queryString )
    {
        DataDateFlopService service = new DataDateFlopService();
        Map map = new HashMap();
        try
        {
            map = service.getDataDateFlopList(start, limit, queryString, Constants.IEAI_IEAI);
        } catch (Exception e)
        {
            logger.error("查询失败", e);
        }
        return map;
    }


    @RequestMapping("saveDataDateFlop.do")
    @ResponseBody
    public Map saveDataDateFlop ( String jsonData )
    {
        DataDateFlopService service = new DataDateFlopService();
        Map map = new HashMap();
        String message = "";
        try
        {
            message = service.saveDataDate( jsonData,Constants.IEAI_IEAI);
            if("保存成功".equals(message)) {
                map.put(SUCCESS, true);
                map.put(MESSAGE, message);
            }else {
                map.put(SUCCESS, false);
                map.put(MESSAGE, message);
            }
        } catch (Exception e)
        {
            logger.error("保存失败", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "保存失败");
        }
        return map;
    }

    @RequestMapping("deleteDataDate.do")
    @ResponseBody
    public Map deleteDataDate ( String deleteIds )
    {
        DataDateFlopService service = new DataDateFlopService();
        Map map = new HashMap();
        try
        {
            service.deleteDataDate( deleteIds,Constants.IEAI_IEAI);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "删除成功");
        } catch (Exception e)
        {
            logger.error("删除失败", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "删除失败");
        }
        return map;
    }


    @RequestMapping("getIeaiProject.do")
    @ResponseBody
    public Map getIeaiProject (  )
    {
        DataDateFlopService service = new DataDateFlopService();
        Map map = new HashMap();
        try
        {
            List  list = service.getIeaiProject( Constants.IEAI_IEAI);
            map.put("dataList", list);
        } catch (Exception e)
        {
            logger.error("查询失败", e);
            map.put("dataList", Collections.emptyList());
        }
        return map;
    }

    /**
     * 批量更新所有记录的数据日期和业务日期
     * @param dataDate 数据日期（8位数字）
     * @return 操作结果
     */
    @RequestMapping("flopDataDate.do")
    @ResponseBody
    public Map flopDataDate (
            @NotNull @RequestParam("dataDate") String dataDate)
    {
        Map map = new HashMap();

        // 验证数据日期格式（8位数字）
        if (!dataDate.matches("^\\d{8}$")) {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "Data date must be an 8-digit number");
            return map;
        }

        DataDateFlopService service = new DataDateFlopService();
        try
        {
            int updatedCount = service.updateAllDataDates(dataDate, Constants.IEAI_IEAI);
            if (updatedCount > 0) {
                map.put(SUCCESS, true);
                map.put(MESSAGE, String.format("Successfully updated %d records", updatedCount));
            } else {
                map.put(SUCCESS, false);
                map.put(MESSAGE, "No records found to update");
            }
        } catch (Exception e)
        {
            logger.error("更新数据日期失败", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "Failed to update data dates");
        }
        return map;
    }
}