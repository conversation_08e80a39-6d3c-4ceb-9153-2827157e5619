package com.ideal.controller.jobscheduling.actmonitor;

import cn.hutool.core.util.ObjectUtil;
import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.actmonitor.ActBean;
import com.ideal.ieai.server.jobscheduling.repository.actmonitor.FilterActBean;
import com.ideal.ieai.server.jobscheduling.repository.flowquery.FlowQueryManager;
import com.ideal.ieai.server.jobscheduling.repository.onsMessage.OnsMessageManager;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.activity.ScriptCallConstants;
import com.ideal.ieai.server.repository.activity.ScriptCallRuntimeManager;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBean;
import com.ideal.ieai.server.repository.hd.processSwitch.ProcessSwitchData;
import com.ideal.ieai.server.repository.topo.erroroper.ErrorOperationManager;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import com.ideal.service.dm.uthome.UtHomeService;
import com.ideal.service.jobscheduling.actmonitor.ActMonitorService;
import com.ideal.service.jobscheduling.flowquery.FlowQueryService;
import com.ideal.service.platform.businessSystem.BusinessSystemService;
import com.ideal.service.shellcmdoutout.ShellCmdOutPutService;
import com.ideal.util.Security;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * <ul>
 * <li>Title: ActMonitorController.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2016年9月20日
 */
@Controller
public class ActMonitorController
{
    private static final Logger log      = Logger.getLogger(ActMonitorController.class);
    public static final String  TASKID   = "taskId";
    public static final String  FLOWID   = "flowId";
    public static final String  ACTID    = "actId";
    private static final String DATALIST = "dataList";
    public static final String  SYSTYPE  = "sysType";
    public static final String  SUCCESS  = "success";
    public static final String  MESSAGE  = "message";

    @RequestMapping("actMonitorIndex.do")
    public String ftpinfoindex (HttpServletRequest request,String filterPrjName)
    {
        request.setAttribute("filterPrjName", filterPrjName);
        return "jobScheduling/actmonitor/actMonitor";
    }

    @RequestMapping("actSDMonitorIndex.do")
    public String actSDMonitorIndex ()
    {
        return "jobScheduling/actmonitor/actSDMonitor";
    }

    @RequestMapping("actmonitor/getPrjName.do")
    @ResponseBody
    public Map<String, Object> getPrjName ( HttpServletRequest request, ActBean actBean ) throws RepositoryException
    {
        String errorMsg = "";
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            actBean.setDbType(Constants.IEAI_IEAI);
            actBean.setUserId(sessionData.getUserInnerCode());
            actBean.setUserName(sessionData.getUserName());

            map = ActMonitorService.getInstance().getPrjName(actBean);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, errorMsg);
            log.error("getPrjName is error ", e);
        }
        return map;
    }

    /**
     *
     * @Title: getPrjname
     * @Description: 业务监控业务系统查询
     * @param request
     * @return
     * @throws Exception
     * @author: Administrator
     * @date:   2018年5月30日 下午7:48:46
     */
    @RequestMapping("actmonitor/getGroName.do")
    @ResponseBody
    public Map<String, Object> getGroName ( HttpServletRequest request, ActBean actBean )
    {
        List<Map> groNames = new ArrayList();
        Map<String, Object> map = new HashMap<String, Object>();

        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            actBean.setDbType(Constants.IEAI_IEAI);
            actBean.setUserId(sessionData.getUserInnerCode());
            actBean.setUserName(sessionData.getUserName());
            List<ActBean> list = new ArrayList();
            list = ActMonitorService.getInstance().getGroName();
            if (!list.isEmpty())
            {
                List<String> collect = list.stream().map(ActBean::getGro).distinct().collect(Collectors.toList());

                for (String groName : collect)
                {
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("groName", groName);
                    groNames.add(data);
                }
                map.put(DATALIST, groNames);
                map.put(SUCCESS, true);
            } else
            {
                map.put(DATALIST, groNames);
                map.put(SUCCESS, true);
            }
        } catch (Exception e)
        {
            map.put(DATALIST, new ArrayList());
            map.put(SUCCESS, false);
            log.error("getGroName is error ", e);
        }
        return map;
    }

    @RequestMapping("utRecordOptionForJob.do")
    @ResponseBody
    public Map<String, Object> utRecordOption ( HttpServletRequest request )
    {
        String opId = request.getParameter("opId");
        long taskId = Long.parseLong(request.getParameter(TASKID));
        long flowId = Long.parseLong(request.getParameter(FLOWID) == null ? "0" : request.getParameter(FLOWID));
        long actId = Long.parseLong(request.getParameter(ACTID) == null ? "0" : request.getParameter(ACTID));
        String opDesc = request.getParameter("opDesc");
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));
        Map<String, Object> map = new HashMap<String, Object>();

        UtHomeService service = new UtHomeService();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            map = service.existFailover(flowId, actId, sysType);
            if (!(Boolean) map.get("flag"))
            {
                service.utRecordOption(taskId, sessionData.getSessionId(), opId, opDesc, sysType);
                map.put(MESSAGE, "执行成功");
                map.put(SUCCESS, true);
            }
        } catch (Exception e)
        {
            map.put(MESSAGE, "执行失败");
            map.put(SUCCESS, false);
            log.error("utRecordOption is error ", e);
        }
        return map;
    }

    @RequestMapping("utRecordOptionForOdsJob.do")
    @ResponseBody
    public Map<String, Object> utRecordOptionForOdsJob ( HttpServletRequest request )
    {
        String opId = request.getParameter("opId");
        long taskId = Long.parseLong(request.getParameter(TASKID));
        long flowId = Long.parseLong(request.getParameter(FLOWID) == null ? "0" : request.getParameter(FLOWID));
        long actId = Long.parseLong(request.getParameter(ACTID) == null ? "0" : request.getParameter(ACTID));
        String opDesc = request.getParameter("opDesc");
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));
        Map<String, Object> map = new HashMap<String, Object>();

        UtHomeService service = new UtHomeService();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            map = service.existFailover(flowId, actId, sysType);
            if (!(Boolean) map.get("flag"))
            {
                service.utRecordOption(taskId, sessionData.getSessionId(), opId, opDesc, sysType);
                map.put(MESSAGE, "执行成功");
                map.put(SUCCESS, true);
            }
        } catch (Exception e)
        {
            map.put(MESSAGE, "执行失败");
            map.put(SUCCESS, false);
            log.error("utRecordOption is error ", e);
        }
        return map;
    }

    @RequestMapping("utRecordForJob.do")
    @ResponseBody
    public Object utRecord ( HttpServletRequest request )
    {
        long taskId = 0;
        if (StringUtils.isNotBlank(request.getParameter(TASKID)))
        {
            taskId = Long.valueOf(request.getParameter(TASKID));
        }
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));

        Map list = null;

        UtHomeService service = new UtHomeService();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            list = service.getTaskDetail(taskId, sessionData.getSessionId(), sysType);

        } catch (Exception e)
        {
            log.error("utRecord is error ", e);
        }
        return list;

    }

    @RequestMapping("utPropsForJob.do")
    @ResponseBody
    public Object utProps ( HttpServletRequest request )
    {
        long taskId = 0;
        if (StringUtils.isNotBlank(request.getParameter(TASKID)))
        {
            taskId = Long.valueOf(request.getParameter(TASKID));
        }
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));

        Map list = null;

        UtHomeService service = new UtHomeService();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            list = service.getTaskProps(taskId, sessionData.getSessionId(), sysType);

        } catch (Exception e)
        {
            log.error("utProps is error ", e);
        }
        return list;

    }

    @SuppressWarnings("unused")
    @RequestMapping("actMonitorUtInfo.do")
    public String utInfo ( HttpServletRequest request )
    {

        try
        {
            int sysType = Integer.parseInt(request.getParameter(SYSTYPE));
            long flowId = Long.parseLong(request.getParameter(FLOWID) == null ? "0" : request.getParameter(FLOWID));
            long actId = Long.parseLong(request.getParameter(ACTID) == null ? "0" : request.getParameter(ACTID));
            String pageType = request.getParameter("pageType");
            String pageName = request.getParameter("pageName");
            // 开启操作说明的开关
            boolean isOperDesc = PersonalityEnv.isOperationDescriptionSwitchValue();
            request.setAttribute("isOperDesc", isOperDesc);
            request.setAttribute("pageType", pageType);
            request.setAttribute(SYSTYPE, sysType);
            request.setAttribute(FLOWID, flowId);
            request.setAttribute(ACTID, actId);
            request.setAttribute("pageName", pageName);

        } catch (Exception e)
        {
            log.error("utInfo is error ", e);
        }
        return "jobScheduling/actmonitor/actMonitorUtInfo";
    }

    @RequestMapping("actList.do")
    @ResponseBody
    public Object actList(HttpServletRequest request, Long limit, Long page, String insName, String stateValue, String treeFlowName, String treePrjName, String treeSysName,
                          String groName, String prjName, String state)
    {
        SessionData sessionData = SessionData.getSessionData(request);
        Map<String, Object> resp = new HashMap<String, Object>();
        String sysName = request.getParameter("sysName");
        List<ActBean> actData = new ArrayList<>();//带延迟器和定时cron
        List<ActBean> actDataTwo = new ArrayList<>();//不带延迟器和定时cron
        try
        {
            if (null != sessionData.getUserInnerCode() && !"".equals(sessionData.getUserInnerCode()))
            {
                FilterActBean filter = new FilterActBean();
                filter.setStart((page - 1) * limit);
                filter.setPagesize(limit);
                if (StringUtils.isNotEmpty(groName))
                {
                    List<String> lastSysNameList = ActMonitorService.getInstance().getLastSysNameList(groName);
                    String prjNames = "'" + StringUtils.join(lastSysNameList, "','") + "'";
                    String sysNames = StringUtils.join(lastSysNameList, ",");
                    resp = ActMonitorService.getInstance().listActsByGro(Long.valueOf(sessionData.getUserInnerCode()),
                        filter, prjNames, prjName, sysNames);
                } else if (StringUtils.isNotEmpty(sysName)
                        && ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                {
                    resp = ActMonitorService.getInstance().listActsBySys(Long.valueOf(sessionData.getUserInnerCode()),
                            filter, prjName, sysName, "");
                } else if (StringUtils.isNotEmpty(prjName) || StringUtils.isNotEmpty(state))
                {
                    resp = ActMonitorService.getInstance().listActsByPrj(Long.valueOf(sessionData.getUserInnerCode()),
                            filter, prjName, state);
                } else if (StringUtils.isNotEmpty(treeSysName)) {
                    //树形图查询，按系统名查询
                    resp = ActMonitorService.getInstance().listActsBySys(Long.valueOf(sessionData.getUserInnerCode()),
                            filter, prjName, "", treeSysName);
                } else if (StringUtils.isNotEmpty(treeFlowName) && StringUtils.isNotEmpty(treePrjName)) {
                    //树形图查询，按工程名加流程名查询
                    resp = ActMonitorService.getInstance().listActsByPrjFlow(Long.valueOf(sessionData.getUserInnerCode()),
                            filter, treePrjName, state, treeFlowName);
                } else if (StringUtils.isNotEmpty(treePrjName)) {
                    //树形图查询，按工程名查询
                    resp = ActMonitorService.getInstance().listActsByPrj(Long.valueOf(sessionData.getUserInnerCode()),
                            filter, treePrjName, state);
                } else
                {
                    resp = ActMonitorService.getInstance().listActs(Long.valueOf(sessionData.getUserInnerCode()),
                        filter);
                }

                resp.put(SUCCESS, true);
            }
            List<ActBean> resultList = (List<ActBean>) resp.get("datalist");
//            if (null != treeFlowId){
//                List<Long> callFlowId = ActMonitorService.getInstance().getCallFlowId(treeFlowId);
//                callFlowId.add(treeFlowId);
//                // 筛选resultList中flowID在callFlowIdList中的记录
//                List<ActBean> filteredList = new ArrayList<>();
//                for (ActBean bean : resultList) {
//                    if (callFlowId.contains(bean.getFlowID())) {
//                        filteredList.add(bean);
//                    }
//                }
//                resp.put("datalist", filteredList);
//            } else if (StringUtils.isNotBlank(treePrjName)) {
//
//            }

            if (resp != null && resp.containsKey("datalist") && !"all".equals(stateValue)) {
                List<ActBean> resultList1 = (List<ActBean>) resp.get("datalist");
                for (ActBean bean : resultList1) {
                    Long flowID = bean.getFlowID();
                    boolean flag = ActMonitorService.getInstance().getFilterActList(flowID);
                    if (flag) {
                        actData.add(bean);
                    } else {
                        actDataTwo.add(bean);
                    }
                }
                if ("schedule".equals(stateValue)) {
                    resp.put("datalist", actData);
                }else if ("task".equals(stateValue)){
                    resp.put("datalist", actDataTwo);
                }
            }

        } catch (RepositoryException e)
        {
            log.error("actList is error ", e);
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    @RequestMapping("actSDList.do")
    @ResponseBody
    public Object actSDList ( HttpServletRequest request, Long limit, Long page, String insName, String stateValue,
            String prjName )
    {
        SessionData sessionData = SessionData.getSessionData(request);
        Map<String, Object> firResp = new HashMap<String, Object>();
        Map<String, Object> allResp = new HashMap<String, Object>();
        JSONObject jsonObject = null;
        try
        {
            if (null != sessionData.getUserInnerCode() && !"".equals(sessionData.getUserInnerCode()))
            {
                FilterActBean filter = new FilterActBean();
                filter.setStart(0L);
                filter.setPagesize(10000L);

                firResp = ActMonitorService.getInstance().listSDActs(Long.valueOf(sessionData.getUserInnerCode()),
                    filter, prjName, 0);

                allResp = ActMonitorService.getInstance().listActs(Long.valueOf(sessionData.getUserInnerCode()),
                    filter);

                jsonObject = dealRespMap(firResp, allResp, prjName);

            }

        } catch (RepositoryException e)
        {
            log.error("actList is error ", e);
            firResp.put(SUCCESS, false);
            firResp.put(MESSAGE, e.getMessage());
        }

        return jsonObject;

    }

    private JSONObject dealRespMap ( Map<String, Object> resp, Map<String, Object> allResp, String prjName )
            throws NumberFormatException, RepositoryException
    {

        JSONObject jb = new JSONObject();
        jb.put("text", ".");
        if (resp != null)
        {

            List<ActBean> resultList = (List<ActBean>) resp.get("datalist");
            resultList = resultList.stream().sorted(Comparator.comparing(ActBean::getFlowID)).collect(Collectors.toList());
            log.info("result List size is:" + resultList.size());
            JSONArray jsonarray = JSONArray.fromObject(resultList);
            List<Long> flowIdList = new ArrayList<>();
            l1:
            for (int i = 0; i < jsonarray.size(); i++)
            {
                List<Long> thiFlowIdList = null;
                List<Long> secFlowIdList = null;
                JSONObject js = (JSONObject) jsonarray.get(i);
                String firPrjName = js.getString("prjName");
                long flowId = js.getLong("flowID");
                for (Long id : flowIdList) {
                    if (id == flowId) {
                        jsonarray.remove(i);
                        continue l1;
                    }
                }
                String actName = js.getString("actName");
                String callFlowActNames = "";
                secFlowIdList = FlowQueryService.getInstance().getAllCallflowIdByMainId(flowId, -1,
                    "'" + actName + "'");
                if (thiFlowIdList == null || thiFlowIdList.isEmpty())
                {
                    List<String> callFlowNames = FlowQueryService.getInstance().getAllCallflowByMainId(flowId);
                    if (callFlowNames != null && callFlowNames.size() > 0)
                    {
                        String cName = StringUtils.join(callFlowNames, "','");
                        callFlowActNames = "'" + cName + "'";
                        secFlowIdList = FlowQueryService.getInstance().getAllCallflowIdByMainId(flowId, -1,
                            callFlowActNames);
                    }
                }

                List<Long> fourthFlowIdList = null;
                // List<Long> secFlowIdList =
                // FlowQueryService.getInstance().getAllCallflowIdByMainId(flowId,callFlowActId);

                List<ActBean> secResultList = new ArrayList<ActBean>();

                if (secFlowIdList == null || secFlowIdList.isEmpty())
                {

                    js.put("leaf", true);
                } else
                {
                    for (int k = 0; k < secFlowIdList.size(); k++)
                    {

                        long secFlowId = secFlowIdList.get(k);
                        flowIdList.add(secFlowId);
                        List list = (List<ActBean>) allResp.get("datalist");
                        if (list != null && !list.isEmpty())
                        {

                            for (int n = 0; n < list.size(); n++)
                            {

                                ActBean bean = (ActBean) list.get(n);

                                if (bean.getFlowID() == secFlowId)
                                {
                                    secResultList.add(bean);
                                }
                            }

                        }
                    }
                    if (secResultList != null && !secResultList.isEmpty())
                    {

                        JSONArray secjsonarray = JSONArray.fromObject(secResultList);
                        if (secjsonarray != null)
                        {
                            for (int m = 0; m < secjsonarray.size(); m++)
                            {
                                List<ActBean> thiResultList = new ArrayList<ActBean>();
                                JSONObject secjs = (JSONObject) secjsonarray.get(m);
                                long sflowId = secjs.getLong("flowID");

                                String secPrjName = secjs.getString("prjName");
                                String seccallFlowActName = secjs.getString("actName");
                                // List <String> secCallFlowNames =
                                // FlowQueryService.getInstance().getAllCallflowByMainId(flowId);
                                thiFlowIdList = FlowQueryService.getInstance().getAllCallflowIdByMainId(sflowId, -1,
                                    "'" + seccallFlowActName + "'");
                                if (thiFlowIdList == null || thiFlowIdList.isEmpty())
                                {
                                    List<String> secCallFlowNames = FlowQueryService.getInstance()
                                            .getAllCallflowByMainId(sflowId);
                                    String secAllCallFlowName = "";
                                    if (secCallFlowNames != null && secCallFlowNames.size() > 0)
                                    {
                                        String cName1 = StringUtils.join(secCallFlowNames, "','");
                                        secAllCallFlowName = "'" + cName1 + "'";
                                        thiFlowIdList = FlowQueryService.getInstance().getAllCallflowIdByMainId(sflowId,
                                            -1, secAllCallFlowName);
                                    }
                                }

                                if (thiFlowIdList == null || thiFlowIdList.isEmpty())
                                {

                                    secjs.put("leaf", true);
                                } else
                                {
                                    for (int k = 0; k < thiFlowIdList.size(); k++)
                                    {

                                        long chFlowId = thiFlowIdList.get(k);
                                        flowIdList.add(chFlowId);
                                        List list = (List<ActBean>) allResp.get("datalist");
                                        if (list != null && !list.isEmpty())
                                        {

                                            for (int n = 0; n < list.size(); n++)
                                            {

                                                ActBean bean = (ActBean) list.get(n);

                                                if (bean.getFlowID() == chFlowId)
                                                {
                                                    bean.setLeaf(true);
                                                    thiResultList.add(bean);
                                                }
                                            }

                                        }

                                    }

                                    if (thiResultList.isEmpty())
                                    {

                                        log.info("thiResultList is null or is empty");
                                        secjs.put("leaf", true);
                                    } else
                                    {
                                        JSONArray thijsonarray = JSONArray.fromObject(thiResultList);
                                        secjs.put("leaf", false);
                                        secjs.put("children", thijsonarray);

                                        for (int jj = 0; jj < thijsonarray.size(); jj++)
                                        {
                                            List<ActBean> foruthResultList = new ArrayList<ActBean>();
                                            JSONObject thijs = (JSONObject) thijsonarray.get(jj);
                                            long thiflowId = thijs.getLong("flowID");
                                            flowIdList.add(thiflowId);
                                            String thiPrjName = thijs.getString("prjName");
                                            String thicallFlowActName = thijs.getString("actName");
                                            fourthFlowIdList = FlowQueryService.getInstance().getAllCallflowIdByMainId(
                                                thiflowId, -1, "'" + thicallFlowActName + "'");

                                            if (fourthFlowIdList == null || fourthFlowIdList.isEmpty())
                                            {

                                                fourthFlowIdList = FlowQueryService.getInstance()
                                                        .getAllCallflowIdByMainId(thiflowId);
                                            }

                                            // List <String> secCallFlowNames =
                                            // FlowQueryService.getInstance().getAllCallflowByMainId(flowId);
                                            if (fourthFlowIdList == null || fourthFlowIdList.isEmpty())
                                            {
                                                thijs.put("leaf", true);
                                                log.info("fourthFlowIdList is null or is empty");
                                            } else
                                            {

                                                for (int k = 0; k < fourthFlowIdList.size(); k++)
                                                {

                                                    long fouthFlowId = fourthFlowIdList.get(k);
                                                    flowIdList.add(fouthFlowId);
                                                    List list = (List<ActBean>) allResp.get("datalist");
                                                    if (list != null && !list.isEmpty())
                                                    {

                                                        for (int n = 0; n < list.size(); n++)
                                                        {

                                                            ActBean bean = (ActBean) list.get(n);

                                                            if (bean.getFlowID() == fouthFlowId)
                                                            {
                                                                bean.setLeaf(true);
                                                                foruthResultList.add(bean);
                                                            }
                                                        }

                                                    }

                                                }
                                                JSONArray fourthjsonarray = JSONArray.fromObject(foruthResultList);
                                                thijs.put("children", fourthjsonarray);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (!secjsonarray.isEmpty())
                        {

                            js.put("children", secjsonarray);
                        } else
                        {
                            if (!firPrjName.equals(prjName))
                            {
                                jsonarray.remove(i);
                                i--;
                                continue;
                            }
                        }
                    } else
                    {

                        js.put("leaf", true);
                    }
                }

            }

            jb.put("children", jsonarray);
            jb.put("count", jsonarray.size());
        }

        return jb;
    }

    /**   
     * @Title: updateFinishSkip   
     * @Description: 更改脚本调用活动的运行状态为略过状态  
     * @param request
     * @param flowId
     * @param actName
     * @param runtimeActiid
     * @return
     * @throws ServerException      
     * @author: lyq 
     * @date:   2019年4月22日 下午9:37:35   
     */
    @RequestMapping("updateFinishSkip.do")
    @ResponseBody
    public Map<String, Object> updateFinishSkip ( HttpServletRequest request ) throws ServerException
    {

        Map<String, Object> map = new HashMap<String, Object>();
        long runtimeActiid = Long.parseLong(request.getParameter("runtimeActiid"));
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));

        try
        {
            ScriptCallRuntimeManager.getInstance().updateStatesByRuntimeActiid(runtimeActiid, sysType,
                ScriptCallConstants.SCRIPT_CALL_SKIPPING);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "操作成功");
        } catch (DBException e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, e.getMessage());
        }

        return map;

    }

    /**
     * 
     * <li>Description:</li>
     * 
     * <AUTHOR> 2016年9月22日
     * @param request
     * @param flowId
     * @param errorTaskId
     * @param execModel 1重试 2略过
     * @param sts
     * @param actName
     * @param actId
     * @param systemType
     * @return return Map
     */
    @RequestMapping("jobSchedulingRepeat.do")
    @ResponseBody
    public Map<String, Object> jobSchedulingRepeat ( HttpServletRequest request, long flowId, long errorTaskId,
            String execModel, String sts, String actName, String actId, String explain )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        // 与石哥沟通：
        // 重试或者略过 根据flowId判断 流程状态是否为异常终止，如果是异常终止直接返回"异常终止不能重试或略过" ===start
        // gang_wang
        if ("1".equals(execModel) || "2".equals(execModel))
        {
            if (flowId > 0)
            {

                try
                {
                    int status = ErrorOperationManager.getInstance().getFlowState(String.valueOf(flowId));
                    if (status == Constants.STATE_UNEXPECTED_END)
                    {
                        map.put(MESSAGE, "异常终止活动不能重试或略过!");
                        return map;
                    }
                } catch (RepositoryException e)
                {
                    log.error("ActMonitorController.jobSchedulingRepeat is error !", e);
                }
            }
        }
        // end
        String message = "";
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));
        int flag = Integer.parseInt(execModel);
        ShellCmdOutPutService service = new ShellCmdOutPutService();
        long iActId = 0L;
        if (ObjectUtil.isNotNull(request.getParameter("iActId")))
        {
            iActId = Long.parseLong(request.getParameter("iActId"));
        }
        try
        {
            List list = jsonTranslate(flowId, errorTaskId, actId, actName);
            map = service.exceptionOperForIEAI(list, request, String.valueOf(flowId), iActId, execModel, sts, explain,
                sysType);
            if ((Boolean) map.get("flag"))
            {
                map.put(SUCCESS, false);
            } else
            {
                map.put(SUCCESS, true);
                /** add by xinglili 20220910 西安-调度平台与行内报警平台对接 */
                Boolean warningXiAn = ServerEnv.getInstance()
                        .getBooleanConfig(ServerEnv.IEAI_WARNING_MONITOR_XIAN_SWITCH, false);
                /** add by yuxh 20200108 中银富登报警 */
                boolean zyfdWarnSwitch = PersonalityEnv.isZyfdWarnSwitchValue();
                if (Environment.getInstance().isYCACTMONITOR()||zyfdWarnSwitch)
                {
                    if (flag == 1 || flag == 2)
                    {// 重试、略过清除报警信息
                        /* deleteWaringNum( String.valueOf(flowId)); */
                        String id = WorkflowManager.getInstance().getAlarmIflowid(flowId);
                        if (!"".equals(id) || id != null)
                        {
                            service.dealNodeWarnDelDes(id, request);
                        } else
                        {
                            log.info("告警已经被清理！未查到告警信息");
                        }
                    }
                } else if (warningXiAn)
                {

                    if (flag == 1 || flag == 2)
                    {// 重试、略过清除报警信息
                        /* deleteWaringNum( String.valueOf(flowId)); */
                        String id = WorkflowManager.getInstance().getAlarmIflowid(flowId);
                        if (!"".equals(id) || id != null)
                        {
                            service.dealNodeWarnDelDesXiAn(id, request, execModel);
                        } else
                        {
                            log.info("告警已经被清理！未查到告警信息");
                        }
                    }

                }
                // if(warningXiAn){
                // if(flag==1||flag==2){//重试、略过清除报警信息
                // /* deleteWaringNum( String.valueOf(flowId));*/
                // String id= WorkflowManager.getInstance().getAlarmIflowid(flowId);
                // if(!"".equals(id)||id!=null){
                // service.dealNodeWarnDelDesXiAn( id,request,execModel);
                // }else {
                // log.info("告警已经被清理！未查到告警信息");
                // }
                // }
                // }
                switch (flag)
                {
                    case 1:
                        message = "重试";
                        break;
                    case 2:
                        message = "略过";
                        break;
                    case 0:
                        message = "继续执行";
                        break;
                    case 4:
                        message = "强制成功操作";
                        break;
                    default:
                        break;
                }
                map.put(MESSAGE, message + "成功");
            }
        } catch (Exception e)
        {
            map.put(MESSAGE, message + "失败");
            map.put(SUCCESS, false);
            log.error("jobSchedulingRepeat is error ", e);
        }
        return map;
    }

    private int deleteWaringNum ( String flowId )
    {
        int isAlarm = 0;
        try
        {
            isAlarm = WorkflowManager.getInstance().deleteWaringNum(flowId);
        } catch (ServerException e1)
        {
            isAlarm = 0;
            log.error("删除报警异常:" + e1);
        }
        return isAlarm;
    }

    /**   
     * @Title: jobSchedulingRepeatOds   
     * @Description: 分离ods的首页异常处理权限   
     * @param request
     * @param flowId
     * @param errorTaskId
     * @param execModel
     * @param sts
     * @param actName
     * @param actId
     * @param explain
     * @return      
     * @author: txl 
     * @date:   2020-8-5 16:19:52   
     */
    @RequestMapping("jobSchedulingRepeatOds.do")
    @ResponseBody
    public Map<String, Object> jobSchedulingRepeatOds ( HttpServletRequest request, long flowId, long errorTaskId,
            String execModel, String sts, String actName, String actId, String explain )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        // 与石哥沟通：
        // 重试或者略过 根据flowId判断 流程状态是否为异常终止，如果是异常终止直接返回"异常终止不能重试或略过" ===start
        // gang_wang
        if ("1".equals(execModel) || "2".equals(execModel))
        {
            if (flowId > 0)
            {
                try
                {
                    int status = ErrorOperationManager.getInstance().getFlowState(String.valueOf(flowId));
                    if (status == Constants.STATE_UNEXPECTED_END)
                    {
                        map.put(MESSAGE, "异常终止活动不能重试或略过!");
                        return map;
                    }
                } catch (RepositoryException e)
                {
                    log.error("ActMonitorController.jobSchedulingRepeat is error !", e);
                }
            }
        }
        // end
        String message = "";
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));
        int flag = Integer.parseInt(execModel);
        ShellCmdOutPutService service = new ShellCmdOutPutService();
        long iActId = Long.parseLong(request.getParameter("iActId"));
        try
        {
            List list = jsonTranslate(flowId, errorTaskId, actId, actName);
            map = service.exceptionOperForIEAI(list, request, String.valueOf(flowId), iActId, execModel, sts, explain,
                sysType);
            if ((Boolean) map.get("flag"))
            {
                map.put(SUCCESS, false);
            } else
            {
                map.put(SUCCESS, true);
                switch (flag)
                {
                    case 1:
                        message = "重试";
                        break;
                    case 2:
                        message = "略过";
                        break;
                    case 0:
                        message = "继续执行";
                        break;
                    case 4:
                        message = "终止工作流";
                        break;
                    default:
                        break;
                }
                map.put(MESSAGE, message + "成功");
            }
        } catch (Exception e)
        {
            map.put(MESSAGE, message + "失败");
            map.put(SUCCESS, false);
            log.error("jobSchedulingRepeat is error ", e);
        }
        return map;
    }

    /*
     * <li>Description:拼装list 为异常处理使用</li>
     * 
     * <AUTHOR> 2016年1月13日
     * 
     * @param flowId
     * 
     * @param errorTaskId
     * 
     * @param actId
     * 
     * @param actName
     * 
     * @return
     * 
     * @throws Exception return List
     */
    public List<ProcessSwitchData> jsonTranslate ( long flowId, long errorTaskId, String actId, String actName )
    {
        List<ProcessSwitchData> dataList = new ArrayList<ProcessSwitchData>();
        ProcessSwitchData bean = null;
        bean = new ProcessSwitchData();

        bean.setFlowId(Long.valueOf(flowId));
        bean.setErrorTaskId(errorTaskId);
        if (!"".equals(actId) && null != actId)
        {
            bean.setActId(actId);
        }
        if (!"".equals(actName) && null != actName)
        {
            bean.setActName(actName);
        }
        dataList.add(bean);
        return dataList;
    }

    /**
     * 
     * <li>Description:锦州获取所属系统</li> 
     * <AUTHOR>
     * 2019年3月26日 
     * @return
     * return Map
     */
    @RequestMapping("jzActMonitorQuerySystem.do")
    @ResponseBody
    public Map<String, Object> querySystemCombox ( HttpServletRequest request, ActBean actBean )
    {
        SessionData sessionData = SessionData.getSessionData(request);
        actBean.setDbType(Constants.IEAI_IEAI);
        actBean.setUserId(sessionData.getUserInnerCode());
        actBean.setUserName(sessionData.getUserName());
        return ActMonitorService.getInstance().querySystemCombox(actBean);
    }

    @RequestMapping("utPropsForJobIid.do")
    @ResponseBody
    public Map<String, Object> utPropsForJobIid ( HttpServletRequest request )
    {
        String iid = request.getParameter("iid");

        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));
        Map<String, Object> map = new HashMap<String, Object>();

        UtHomeService service = new UtHomeService();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);

            map = service.getTaskPropsiid(Long.parseLong(iid), sessionData.getSessionId(), sysType);
            map.put(MESSAGE, "执行成功");
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(MESSAGE, "执行失败");
            map.put(SUCCESS, false);
            log.error("utRecordOption is error ", e);
        }
        return map;
    }

    @RequestMapping("utRecordOptionForJobBH.do")
    @ResponseBody
    public Map<String, Object> utRecordOptionForBH ( HttpServletRequest request )
    {
        String checkUser = request.getParameter("checkUser");
        String passWord = Security.encrypt(request.getParameter("passWord").toCharArray());

        String opId = request.getParameter("opId");
        long taskId = Long.parseLong(request.getParameter(TASKID));
        long flowId = Long.parseLong(request.getParameter(FLOWID) == null ? "0" : request.getParameter(FLOWID));
        long actId = Long.parseLong(request.getParameter(ACTID) == null ? "0" : request.getParameter(ACTID));
        String opDesc = request.getParameter("opDesc");
        int sysType = Integer.parseInt(request.getParameter(SYSTYPE));
        Map<String, Object> map = new HashMap<String, Object>();

        UtHomeService service = new UtHomeService();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            String loginName = sessionData.getLoginName();

            if (loginName.equals(checkUser)){
                map.put(SUCCESS, false);
                map.put(MESSAGE, "登录用户和审核人相同,无法申请！");
                return map;
            }
            int[] userInfo = FlowQueryManager.getInstance().getDoubleCheckUserInfo(checkUser, passWord);
            if (userInfo[0] == 0) {
                map.put(SUCCESS, false);
                map.put(MESSAGE, "审核用户不存在或密码错误！");
            } else if (userInfo[0] == -1) {
                map.put(SUCCESS, false);
                map.put(MESSAGE, "审核用户已被锁定！");
            }else {
                map = service.existFailover(flowId, actId, sysType);
                if (!(Boolean) map.get("flag"))
                {
                    service.utRecordOption(taskId, sessionData.getSessionId(), opId, opDesc, sysType);
                    map.put(MESSAGE, "执行成功");
                    map.put(SUCCESS, true);
                    OnsMessageManager.getInstance().deleteUserTaskWarnStatus(flowId);
                }
            }

        } catch (Exception e)
        {
            map.put(MESSAGE, "执行失败");
            map.put(SUCCESS, false);
            log.error("utRecordOption is error ", e);
        }
        return map;
    }

    @RequestMapping("getPrjFlowTree.do")
    @ResponseBody
    public List<Map<String, Object>> getPrjFlowTree(HttpServletRequest request, ActBean actBean, String prjFolwName, String state) throws RepositoryException {
        SessionData sessionData = SessionData.getSessionData(request);
        List<Map<String, Object>> treeData = new ArrayList<>();
        Map<String, Object> resp = new HashMap<>();
        List<ActBean> actData = new ArrayList<>();
        try {
            if (null != sessionData.getUserInnerCode() && !"".equals(sessionData.getUserInnerCode())) {
                FilterActBean filter = new FilterActBean();
                filter.setStart(0L);
                filter.setPagesize(10000L);

                // 直接获取工程节点
                String prjName = actBean.getPrjName();
                if (null != prjName || !"".equals(state)) {
                    resp = ActMonitorService.getInstance().listActsByPrj(Long.valueOf(sessionData.getUserInnerCode()), filter, prjName, state);
                } else {
                    resp = ActMonitorService.getInstance().listActs(Long.valueOf(sessionData.getUserInnerCode()), filter);
                }

                if (resp != null && resp.containsKey("datalist")) {
                    List<ActBean> resultList = (List<ActBean>) resp.get("datalist");
                    for (ActBean bean : resultList) {
                        Long prjid = BusinessSystemService.getInstance().getPrjid(bean.getPrjName());
                        BusinessSystemBean proInfo = BusinessSystemService.getInstance().getProInfo(prjid, Constants.IEAI_IEAI);
                       if(proInfo.getExcelPrj()==1){
                        Long flowID = bean.getFlowID();
                        Long mainFlowId = ActMonitorService.getInstance().getMainFlowId(flowID);
                        if (null != mainFlowId && mainFlowId > 0) {
                            // 主流程跳过
                        } else {
                            actData.add(bean);
                        }
                       }else {
                           actData.add(bean);
                       }

                    }
                    // 按系统名分组，如果系统名为空则使用工程名分组
                    Map<String, List<ActBean>> projects = actData.stream()
                            .collect(Collectors.groupingBy(bean -> {
                                String sysName = bean.getSysName();
                                return (sysName != null && !sysName.trim().isEmpty()) ? sysName : bean.getPrjName();
                            }));

                    // 如果有过滤条件，先过滤系统/工程
                    if (prjFolwName != null && !prjFolwName.isEmpty()) {
                        // 小写处理，用于不区分大小写匹配
                        String searchKey = prjFolwName.toLowerCase();

                        // 过滤系统名或工程名包含关键字的系统/工程
                        projects = projects.entrySet().stream()
                                .filter(entry ->
                                        // 系统名或工程名匹配
                                        entry.getKey().toLowerCase().contains(searchKey) ||
                                                // 或工作流名匹配
                                                entry.getValue().stream()
                                                        .anyMatch(flow -> flow.getFlowName().toLowerCase().contains(searchKey))
                                )
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    }

                    // 构建系统/工程节点（作为根节点）
                    for (Map.Entry<String, List<ActBean>> entry : projects.entrySet()) {
                        String groupName = entry.getKey(); // 可能是系统名或工程名
                        List<ActBean> flows = entry.getValue();

                        // 如果有过滤条件，过滤当前系统/工程下的工作流
                        if (prjFolwName != null && !prjFolwName.isEmpty()) {
                            String searchKey = prjFolwName.toLowerCase();
                            flows = flows.stream()
                                    .filter(flow ->
                                            // 工作流名匹配
                                            flow.getFlowName().toLowerCase().contains(searchKey) ||
                                                    // 或系统名/工程名匹配（系统名/工程名已经匹配过，这里是冗余检查）
                                                    groupName.toLowerCase().contains(searchKey)
                                    )
                                    .collect(Collectors.toList());
                        }

                        // 如果过滤后没有工作流，跳过该系统/工程
                        if (flows.isEmpty()) {
                            continue;
                        }

                        // 获取第一个工作流的工程名用于查询工程信息
                        String firstPrjName = flows.get(0).getPrjName();
                        Long prjid = BusinessSystemService.getInstance().getPrjid(firstPrjName);
                        BusinessSystemBean proInfo = BusinessSystemService.getInstance().getProInfo(prjid, Constants.IEAI_IEAI);

                        Map<String, Object> projectNode = new HashMap<>();
                        projectNode.put("id", "sys_" + groupName.hashCode());
                        projectNode.put("text", groupName);
                        projectNode.put("prjName", firstPrjName); // 保持原有的prjName字段
                        projectNode.put("sysName", groupName); // 新增sysName字段
                        if (null != proInfo.getSysType()){
                            projectNode.put("iconCls","important-system-icon");
                        }
                        projectNode.put("leaf", false);
                        projectNode.put("expanded", false);

                        // 构建工作流子节点，在当前系统内按工作流名去重
                        List<Map<String, Object>> flowNodes = flows.stream()
                                .collect(Collectors.toMap(
                                    flow -> flow.getFlowName(), // 使用工作流名作为去重键
                                    flow -> {
                                        Map<String, Object> flowNode = new HashMap<>();
                                        flowNode.put("id", flow.getFlowID());
                                        flowNode.put("text", flow.getFlowName());
                                        flowNode.put("prjName", flow.getPrjName()); // 使用实际的工作流工程名
                                        flowNode.put("sysName", flow.getSysName()); // 添加系统名
                                        flowNode.put("flowName", flow.getFlowName());
                                        flowNode.put("leaf", true);
                                        return flowNode;
                                    },
                                    (existing, replacement) -> existing // 如果有重复的工作流名，保留第一个
                                ))
                                .values()
                                .stream()
                                .collect(Collectors.toList());

                        projectNode.put("children", flowNodes);
                        treeData.add(projectNode);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取树状数据失败", e);
        }
        return treeData;
    }

}
