package com.ideal.controller.jobscheduling.taskupload;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.ClientSession;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.cluster.manager.ClusterManager;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ClientSessionHelper;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.DbUtilUpLoadExcel;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.UploadVersionInfo;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.service.jobscheduling.taskupload.TaskUploadService;
import com.ideal.service.topo.topoimport.TopoImportService;
import com.ideal.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

/**   
 * @ClassName:  TaskUploadController   
 * @Description:作业依赖关系导入 
 * @author: yue_sun 
 * @date:   2018年2月26日 上午8:56:52   
 * <AUTHOR> 2018-02-08迁移    
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
@Controller
public class TaskUploadController
{

    private static final Logger _log = Logger.getLogger(TaskUploadController.class);
    public static final String  UTF8 = "UTF-8";
    public static final String  STR1 = "text/html";
    public static final String  STR2 = "{success:false,message:'";

    @RequestMapping("taskUpload.do")
    public String test (HttpServletRequest request, HttpServletResponse response)
    {
        //获取快速上传功能在线开关
        String isFast = TaskUploadService.getInstance().getFastOnlieConfig(Constants.IEAI_IEAI_BASIC);
        request.setAttribute("isFast", isFast);
        return "jobScheduling/taskUpload/taskUpload";
    }

    /**
     * @Title: taskUpload   
     * @Description: 上传文件  
     * @param file
     * @param request
     * @param response      
     * @author: hui_xie 
     * @date:   2017-8-9 下午04:04:06
     */
    @RequestMapping("taskuploadExcel.do")
    public void taskUpload ( @RequestParam("uploadFile") CommonsMultipartFile file, HttpServletRequest request,
            HttpServletResponse response , @RequestParam("incrementalImport")String incrementalImport, @RequestParam("paramIsRequired") String paramIsRequired, UploadVersionInfo uploadVersionInfo)
    {
        File fileNew = null;
        FileOutputStream outer = null;
        String rsStr = "{success:true}";
        String messages = "";
        DbUtilUpLoadExcel dbUtilUpLoadExcel = DbUtilUpLoadExcel.getInstance();
        try
        {
            _log.info(file.getOriginalFilename() + "【Excel导入】开始！");
            ClientSession session = ClusterManager.getInstance().getSession(
                SessionData.getSessionData(request).getSessionId());
            String username = session.getUserLoginName();
            UserInfo user = ClientSessionHelper.getUser(SessionData.getSessionData(request).getSessionId());
            String ffName = file.getOriginalFilename();
            long size = file.getSize();
            getStr2(size, response);
            // 上传文件临时目录
            fileNew = new File(Environment.getInstance().getIEAIHome() + File.separator + UUID.uuid());
            if (!fileNew.exists())
            {
                fileNew.mkdir();
            }
            // 上传文件
            outer = new FileOutputStream(fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename());
            byte[] buffer = file.getBytes();
            outer.write(buffer);
            outer.close();
            // 单独excel文件上传
            if (ffName.toLowerCase().endsWith(".xls") || ffName.toLowerCase().endsWith(".xlsx") || ffName.toLowerCase().endsWith(".et"))
            {
                //添加新条件incrementalImport表示是否为增量上传
                messages = dbUtilUpLoadExcel.taskUpload(fileNew, file,file.getOriginalFilename(), user, username,incrementalImport, paramIsRequired, uploadVersionInfo);
                SessionData sd = SessionData.getSessionData(request);
                String loginName = sd.getUserInnerCode();
                _log.info("USER:" + loginName + " batch import callback topopublish !");
                TopoImportService.getInstance().topopublish(loginName);

            }
            // 通过压缩包上传
            else if (ffName.toLowerCase().endsWith(".rar") || ffName.toLowerCase().endsWith(".zip")
                    || ffName.toLowerCase().endsWith(".7z"))
            {
                // 解压缩后返回结果集，含：1是否成功标志2失败信息3压缩包中文件名清单
                Map map;
                // rar格式压缩包上传
                if (ffName.toLowerCase().endsWith(".rar"))
                {
                    map = TaskUploadService.getInstance().unRarFile(
                        fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(),
                        fileNew.getAbsolutePath());
                }
                // 7z格式压缩包上传
                else if (ffName.toLowerCase().endsWith(".7z"))
                {
                    map = TaskUploadService.getInstance().un7zFile(
                        fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(),
                        fileNew.getAbsolutePath());
                }
                // zip格式压缩包上传
                else
                {
                    map = TaskUploadService.getInstance().unZipFiles(
                        fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(),
                        fileNew.getAbsolutePath());
                }

                String result = (String) map.get("result");
                // 解压缩失败直接返回
                if (!"1".equals(result))
                {
                    messages = (String) map.get("message");
                    rsStr = STR2 + messages + "'}";
                    response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                    response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                    outsClose(rsStr, response);
                } else
                {
                    StringBuilder messageBuilder = new StringBuilder("");
                    List<String> list = (List) map.get("resultList");
                    EngineRepositotyJdbc.getInstance().saveUploadExcelRar(ffName, username, list, Constants.IEAI_IEAI);
                    for (String filename : list)
                    {
                        // 循环解析压缩中的文件
                        messages = dbUtilUpLoadExcel.taskUpload(fileNew, file, filename, user, username,incrementalImport,paramIsRequired, uploadVersionInfo);
                        taskUploadFun(messages, messageBuilder, response, filename, ffName, username);
                    }

                }
            } else
            {
                messages = "请上传合法格式的文件！";
                rsStr = STR2 + messages + "'}";
                response.setCharacterEncoding("utf-8");
                response.setContentType(STR1);
                response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                outsClose(rsStr, response);
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            rsStr = STR2 + "上传失败 " + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        } finally
        {
            finallyClose(outer, fileNew);
        }

        _log.info(file.getOriginalFilename() + "【Excel导入】结束！");
        rsStr = getStr1(messages, response);
        outsClose(rsStr, response);

    }
    /**
     * @Title: taskUpload
     * @Description: 上传文件
     * @param file
     * @param request
     * @param response
     * @author: hui_xie
     * @date:   2017-8-9 下午04:04:06
     */

    @RequestMapping(value = "/checkTaskUploadExcel.do", method = RequestMethod.POST)
    public void checkTaskUpload ( @RequestParam("file") CommonsMultipartFile file, HttpServletRequest request,
                             HttpServletResponse response )
    {
        File fileNew = null;
        FileOutputStream outer = null;
        String rsStr = "{success:true}";
        String messages = "";
        DbUtilUpLoadExcel dbUtilUpLoadExcel = DbUtilUpLoadExcel.getInstance();
        try
        {
            _log.info(file.getOriginalFilename() + "【CheckExcel】开始！");
            ClientSession session = ClusterManager.getInstance().getSession(
                    SessionData.getSessionData(request).getSessionId());
            String username = session.getUserLoginName();
            UserInfo user = ClientSessionHelper.getUser(SessionData.getSessionData(request).getSessionId());
            String ffName = file.getOriginalFilename();
            long size = file.getSize();
            getStr2(size, response);
            // 上传文件临时目录
            fileNew = new File(Environment.getInstance().getIEAIHome() + File.separator + UUID.uuid());
            if (!fileNew.exists())
            {
                fileNew.mkdir();
            }
            // 上传文件
            outer = new FileOutputStream(fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename());
            byte[] buffer = file.getBytes();
            outer.write(buffer);
            outer.close();
            // 单独excel文件上传
            if (ffName.toLowerCase().endsWith(".xls") || ffName.toLowerCase().endsWith(".xlsx"))
            {
                //添加新条件incrementalImport表示是否为增量上传
                messages = dbUtilUpLoadExcel.checkTaskUpload(fileNew,file.getOriginalFilename(), user);

            } else
            {
                messages = "请上传合法格式的文件！";
                rsStr = STR2 + messages + "'}";
                response.setCharacterEncoding("utf-8");
                response.setContentType(STR1);
                response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                outsClose(rsStr, response);
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            rsStr = STR2 + "校验失败 " + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        } finally
        {
            finallyClose(outer, fileNew);
        }

        _log.info(file.getOriginalFilename() + "【Excel校验】结束！");
        rsStr = getStr1(messages, response);
        outsClose(rsStr, response);

    }

    /**
     * @Title: taskUpload
     * @Description: 上传文件
     * @param file
     * @param request
     * @param response
     * @author: hui_xie
     * @date:   2017-8-9 下午04:04:06
     */
    @RequestMapping("taskUpdateExcel.do")
    public void taskUpload1 ( @RequestParam("uploadFile") CommonsMultipartFile file, HttpServletRequest request,
                             HttpServletResponse response , @RequestParam("incrementalImport")String incrementalImport ,@RequestParam("dataUpList")String dataUpList, UploadVersionInfo uploadVersionInfo)
    {
        File fileNew = null;
        FileOutputStream outer = null;
        String rsStr = "{success:true}";
        String messages = "";
        DbUtilUpLoadExcel dbUtilUpLoadExcel = DbUtilUpLoadExcel.getInstance();
        try
        {
            _log.info(file.getOriginalFilename() + "【Excel导入】开始！");
            ClientSession session = ClusterManager.getInstance().getSession(
                    SessionData.getSessionData(request).getSessionId());
            String username = session.getUserLoginName();
            UserInfo user = ClientSessionHelper.getUser(SessionData.getSessionData(request).getSessionId());
            String ffName = file.getOriginalFilename();
            long size = file.getSize();
            getStr2(size, response);
            // 上传文件临时目录
            fileNew = new File(Environment.getInstance().getIEAIHome() + File.separator + UUID.uuid());
            if (!fileNew.exists())
            {
                fileNew.mkdir();
            }
            // 上传文件
            outer = new FileOutputStream(fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename());
            byte[] buffer = file.getBytes();
            outer.write(buffer);
            outer.close();
            // 单独excel文件上传
            if (ffName.toLowerCase().endsWith(".xls") || ffName.toLowerCase().endsWith(".xlsx"))
            {
                //添加新条件incrementalImport表示是否为增量上传
                messages = dbUtilUpLoadExcel.taskUpload(fileNew, file,file.getOriginalFilename(), user, username,incrementalImport,dataUpList,0L,uploadVersionInfo);

            }
            // 通过压缩包上传
            else if (ffName.toLowerCase().endsWith(".rar") || ffName.toLowerCase().endsWith(".zip")
                    || ffName.toLowerCase().endsWith(".7z"))
            {
                // 解压缩后返回结果集，含：1是否成功标志2失败信息3压缩包中文件名清单
                Map map;
                // rar格式压缩包上传
                if (ffName.toLowerCase().endsWith(".rar"))
                {
                    map = TaskUploadService.getInstance().unRarFile(
                            fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(),
                            fileNew.getAbsolutePath());
                }
                // 7z格式压缩包上传
                else if (ffName.toLowerCase().endsWith(".7z"))
                {
                    map = TaskUploadService.getInstance().un7zFile(
                            fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(),
                            fileNew.getAbsolutePath());
                }
                // zip格式压缩包上传
                else
                {
                    map = TaskUploadService.getInstance().unZipFiles(
                            fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(),
                            fileNew.getAbsolutePath());
                }

                String result = (String) map.get("result");
                // 解压缩失败直接返回
                if (!"1".equals(result))
                {
                    messages = (String) map.get("message");
                    rsStr = STR2 + messages + "'}";
                    response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                    response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                    outsClose(rsStr, response);
                } else
                {
                    StringBuilder messageBuilder = new StringBuilder("");
                    List<String> list = (List) map.get("resultList");
                    EngineRepositotyJdbc.getInstance().saveUploadExcelRar(ffName, username, list, Constants.IEAI_IEAI);
                    for (String filename : list)
                    {
                        // 循环解析压缩中的文件
                        messages = dbUtilUpLoadExcel.taskUpload(fileNew, file, filename, user, username,incrementalImport,null,0L);
                        taskUploadFun(messages, messageBuilder, response, filename, ffName, username);
                    }

                }
            } else
            {
                messages = "请上传合法格式的文件！";
                rsStr = STR2 + messages + "'}";
                response.setCharacterEncoding("utf-8");
                response.setContentType(STR1);
                response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                outsClose(rsStr, response);
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            rsStr = STR2 + "上传失败 " + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        } finally
        {
            finallyClose(outer, fileNew);
        }

        _log.info(file.getOriginalFilename() + "【Excel导入】结束！");
        rsStr = getStr1(messages, response);
        outsClose(rsStr, response);

    }


    private void taskUploadFun ( String messages, StringBuilder messageBuilder, HttpServletResponse response,
            String filename, String ffName, String userName )
            throws RepositoryException
    {
        if (!"导入成功".equals(messages))
        {
            messageBuilder = messageBuilder.append("压缩包中文件\"").append(filename).append("\"导入失败，").append(messages);
            String rsStr = STR2 + messageBuilder.toString() + "'}";
            response.setCharacterEncoding(UTF8);
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        } else
        {
            EngineRepositotyJdbc.getInstance().updateUploadExcelRar(ffName, filename, userName, 1, Constants.IEAI_IEAI);
        }
    }
    
    private void finallyClose ( FileOutputStream outer, File fileNew )
    {
        try
        {
            if (null != outer)
            {
                // 关闭输出流
                outer.close();
            }
            if (null != fileNew)
            {
                // 删除临时文件
                TaskUploadService.getInstance().deleteDir(fileNew);
            }
        } catch (Exception e2)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e2);
        }
    }
    
    private String getStr1 ( String messages, HttpServletResponse response )
    {
        String rsStr = "";
        if ("导入成功".equals(messages))
        {
            rsStr = "{success:true,message:'" + messages + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
        } else
        {
            if (StringUtils.isBlank(messages))
            {
                messages = "上传失败";
            }
            rsStr = STR2 + messages + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
        }
        return rsStr;
    }
    
    private void getStr2 (long size, HttpServletResponse response )
    {
        if (size >= 52428800)
        {
            String rsStr = STR2 + "上传模板文件不能大于50M" + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        }
    }
    
    private void outsClose ( String rsStr, HttpServletResponse response )
    {
        PrintWriter outs = null;
        try
        {
            outs = response.getWriter();
            outs.write(rsStr);
            outs.close();
        } catch (IOException e1)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
        }
    }

}
