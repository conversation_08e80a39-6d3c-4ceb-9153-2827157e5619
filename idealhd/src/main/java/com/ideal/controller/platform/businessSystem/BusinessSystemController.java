package com.ideal.controller.platform.businessSystem;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringEscapeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.utils.*;
import com.ideal.dubbo.models.AgentModel;
import com.ideal.ieai.clientapi.api.ConnectionAPI;
import com.ideal.ieai.clientapi.api.ProjectAPI;
import com.ideal.ieai.commons.ClientSession;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ProjectInfo;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.communication.Session;
import com.ideal.ieai.communication.marshall.Response;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.eswitch.repository.gfxmdb.SysModel;
import com.ideal.ieai.server.eswitch.thread.XMDBSyncBSToHcThread;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.batchstart.BatchStartBean;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.DbUtilUpLoadExcel;
import com.ideal.ieai.server.jobscheduling.thread.TopoLogicalThread;
import com.ideal.ieai.server.jobscheduling.util.prjmodel.Project;
import com.ideal.ieai.server.paas.order.monitor.OrderMonitorManage;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.dock.docking.Docking;
import com.ideal.ieai.server.repository.hd.cicd.syncsystem.SyncSystemService;
import com.ideal.ieai.server.repository.hd.ic.hccomputeroperate.HcComputerOperateManager;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBean;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBeanForQuery;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemManager;
import com.ideal.ieai.server.repository.hd.userGroup.UserGroupInfo;
import com.ideal.ieai.server.repository.permission.IPermissionManager;
import com.ideal.ieai.server.repository.permission.PermissionManager;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.repository.user.IUserManager;
import com.ideal.ieai.server.repository.user.UserManager;
import com.ideal.service.instanceConfig.InstanceConfigService;
import com.ideal.service.jobscheduling.projects.ProjectsService;
import com.ideal.service.platform.businessSystem.BusinessSystemService;
import com.ideal.service.topo.topoimport.TopoImportService;
import com.ideal.service.userGroup.UserGroupService;
import com.ideal.util.CollectionUtil;
import com.ideal.util.Security;
import com.ideal.util.UUID;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.tools.zip.ZipOutputStream;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.zip.DataFormatException;

/**
 * 
 * 名称: BusinessSystemController.java<br>
 * 描述: 业务系统Controller<br>
 * 类型: JAVA<br>
 * 最近修改时间:2015年10月13日<br>
 * 
 * <AUTHOR>
 */
@Controller
public class BusinessSystemController
{
    private static Logger       log            = Logger.getLogger(BusinessSystemController.class);
    private static final String SUCCESS        = "success";
    private static final String MESSAGE        = "message";
    private static final String OPERASYSTYPE   = "opersystype";
    private static final String SYSNAME        = "sysName";
    private static final String URLTYPE        = "urlType";
    private static final String SYSCONFIGUSER  = "sysConfigUser";
    private static final String SYSCONFIGGUSER = "sysConfigGUser";
    private static final String CZ_PROMOTION_IP = ServerEnv.getInstance().getSysConfig(Environment.CZ_PROMOTION_IP, "");

    Map<Long,String> proInfoMap = BusinessSystemManager.getInstance().getProjectMap();

    IPermissionManager          _permMgr       = PermissionManager.getInstance();

    /**
     * User manager
     */
    IUserManager                _userMgr       = UserManager.getInstance();

    /**
     * getFilterActList
     * @Title: initBusinessSystem
     * @Description: 业务系统首页
     * @return
     * @return String 返回类型
     * @throws
     * @变更记录 2015年10月13日 yunpeng_zhang
     */
    @RequestMapping("initBusinessSystem.do")
    public String initBusinessSystem ( HttpServletRequest request )
    {
        SessionData sessionData = SessionData.getSessionData(request);
        request.setAttribute("loginName", sessionData.getLoginName());
        String urlType = StringEscapeUtils.escapeHtml4( request.getParameter(URLTYPE) == null ? "" : request.getParameter(URLTYPE));
        request.setAttribute(URLTYPE, urlType);
        ConfigReader cr = ConfigReader.getInstance();
        try
        {
            cr.init();
        } catch (IOException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " " + e);
        }
        boolean s = cr.getBooleanProperties(Environment.OPM_BUSS_USER_BIND_SWITCH, true);
        request.setAttribute("user_sys", s ? false : true);
        return "businessSystem/businessSystemMain";
    }

    /**
     * 
     * @Title: initBusinessSystemComputer
     * @Description: 所属设备init方法
     * @return
     * @return String 返回类型
     * @throws
     * @变更记录 2015年10月16日 yunpeng_zhang
     */
    @RequestMapping("initBusinessSystemComputer.do")
    public String initBusinessSystemComputer ()
    {
        // 浦发银行 平台管理 业务系统维护 设备窗口特殊展示开关
        boolean busisystemSwitch = Environment.getInstance().getBusisystemMaintainEquiSwitch();
        if (busisystemSwitch)
        {
            return "businessSystem/businessSystemComputerForSPDB";
        } else
        {
            return "businessSystem/businessSystemComputer";
        }
    }

    /**
     * 
     * @Title: initBusinessSystemComputerNoSelected
     * @Description: 待添加设备init方法
     * @return
     * @return String 返回类型
     * @throws
     * @变更记录 2015年10月16日 yunpeng_zhang
     */
    @RequestMapping("initBusinessSystemComputerNoSelected.do")
    public String initBusinessSystemComputerNoSelected ()
    {
        // 浦发银行 平台管理 业务系统维护 设备窗口特殊展示开关
        boolean busisystemSwitch = Environment.getInstance().getBusisystemMaintainEquiSwitch();
        if (busisystemSwitch)
        {
            return "businessSystem/businessSystemComputerNoSelectedForSPDB";
        } else
        {
            return "businessSystem/businessSystemComputerNoSelected";
        }
    }

    /**
     * 
     * @Title: businessSystemNameList
     * @Description: 获取所有业务系统名
     * @param request
     * @return
     * @return Object 返回类型
     * @throws
     * @变更记录 2015年10月13日 yunpeng_zhang
     */
    @RequestMapping("businessSystemNameList.do")
    @ResponseBody
    public Object businessSystemNameList ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        SessionData sessionData = SessionData.getSessionData(request);
        List<BusinessSystemBean> list = new ArrayList<BusinessSystemBean>();
        try
        {
            if (null != sessionData.getUserInnerCode() && !"".equals(sessionData.getUserInnerCode()))
            {
                list = BusinessSystemService.getInstance().getBusinessSystemNameList(
                    Long.valueOf(sessionData.getUserInnerCode()), Constants.IEAI_HEALTH_INSPECTION);
            }

        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return list;
    }

    /**
     * 
     * @Title: sysLvList
     * @Description: 获取业务系统级别
     * @return
     * @return Object 返回类型
     * @throws
     * @变更记录 2015年10月23日 yunpeng_zhang
     */
    @RequestMapping("sysLvList.do")
    @ResponseBody
    public Object sysLvList ()
    {
        List<Map> list = new ArrayList<Map>();
        try
        {
            list = BusinessSystemService.getInstance().getSysLvList();

        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return list;
    }

    /**
     * @Description:徽商银行点击同步按钮对业务系统进行同步
     * @param response
     * @return
     * @throws RepositoryException
     */
    @RequestMapping("dockSystem.do")
    @ResponseBody
    public Map dock ( HttpServletResponse response ) throws RepositoryException
    {
        Map map = new HashMap();
        try
        {
            map = Docking.getInstance().dockSystem("","");
        } catch (Exception e)
        {
            log.info("同步全部系统失败"+e.getMessage());
        }
        return map;

    }


    /**
     * 
     * @Title: businessSystemList
     * @Description: 获取业务系统列表
     * @param request
     * @param flag
     * @param businessSystemBeanForQuery
     * @return
     * @return Map 返回类型
     * @throws
     * @变更记录 2015年10月13日 yunpeng_zhang
     */
    @RequestMapping("businessSystemList.do")
    @ResponseBody
    public Map businessSystemList ( HttpServletRequest request, BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        Map res = null;
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            businessSystemBeanForQuery.setUserId(sessionData.getUserInnerCode());
            businessSystemBeanForQuery.setUserName(sessionData.getUserName());
            businessSystemBeanForQuery.setLoginName(sessionData.getLoginName());
            if (null != sessionData.getUserInnerCode() && !"".equals(sessionData.getUserInnerCode()))
            {
                String sort = request.getParameter("sort");// 要排序的列名及顺序
                if (sort != null)
                {
                    List<Map<String, Object>> sortList = ParseJson.JSON2List(sort);
                    for (Map<String, Object> sortListObj : sortList)
                    {
                        businessSystemBeanForQuery.setProperty(sortListObj.get("property").toString());
                        businessSystemBeanForQuery.setDirection(sortListObj.get("direction").toString());
                    }
                }
                res = BusinessSystemService.getInstance().getBusinessSystemList(businessSystemBeanForQuery,
                    Constants.IEAI_HEALTH_INSPECTION);
            }

        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } catch (JSONException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        res.put("proInfoMap",proInfoMap);
        return res;
    }
    /**
     * 
     * @Title: businessSystemSimpleList
     * @Description: 获取业务系统简单列表
     * @return
     * @return Map 返回类型 (系统名，系统编码)
     * 
     */
    @RequestMapping("businessSystemSimpleList.do")
    @ResponseBody
    public Map<String, Object> businessSystemSimpleList ( HttpServletRequest request, BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
         Map<String, Object> map = new HashMap<String, Object>();
         try
         {
             String systemCode=  request.getParameter("systemCode");
             map = BusinessSystemService.getInstance().getBusinessSystemSimpleList(systemCode);
         } catch (Exception e)
         {
             log.error("BusinessSystemController businessSystemSimpleList is error",e);;
         }
            return map;
    }

    /**   
     * @Title: businessSystemComputerListForSPDB   
     * @Description: 浦发银行 平台管理 业务系统维护 查询设备
     * @param request
     * @param filter
     * @return      
     * @author: yuhe_zhang
     * @date:   2019年1月4日 下午2:34:54   
     */
    @RequestMapping("businessSystemComputerListForSPDB.do")
    @ResponseBody
    public Map<String, Object> businessSystemComputerListForSPDB ( HttpServletRequest request, AgentModel filter,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {

        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));
        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getComputerListForSPDB(businessSystemBeanForQuery, filter, type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return res;
    }//

    @RequestMapping("getHostNameForBusSysSPDB.do")
    @ResponseBody
    public Map<String, Object> getHostNameForBusSysSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {

        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));
        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getHostNameForBusSysSPDB(businessSystemBeanForQuery, type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @RequestMapping("getIpForBusSysSPDB.do")
    @ResponseBody
    public Map<String, Object> getIpForBusSysSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {

        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));
        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getIpForBusSysSPDB(businessSystemBeanForQuery, type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @RequestMapping("getSysAdminForBusSysSPDB.do")
    @ResponseBody
    public Map<String, Object> getSysAdminForBusSysSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {

        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));
        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getSysAdminForBusSysSPDB(businessSystemBeanForQuery, type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @RequestMapping("getSystemNameForBusSysSPDB.do")
    @ResponseBody
    public Map<String, Object> getSystemNameForBusSysSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {

        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));
        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getSystemNameForBusSysSPDB(businessSystemBeanForQuery, type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    /**
     * 
     * @Title: businessSystemComputerList
     * @Description: 获取业务系统所属设备列表)
     * @param request
     * @param businessSystemBeanForQuery
     * @return
     * @return Map 返回类型
     * @throws
     * @变更记录 2015年10月19日 yunpeng_zhang
     */
    @RequestMapping("businessSystemComputerList.do")
    @ResponseBody
    public Map businessSystemComputerList ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        String stype = request.getParameter(OPERASYSTYPE);
        Map<String, String> sortMap = ParseJson.getOrderBy(request);// 获取排序字段
        businessSystemBeanForQuery.setSortMap(sortMap);
        int type = Integer.parseInt(stype);
        // 如果两个输入框都有值，则按ip段查询；否则只有一个输入框有值，则按ip模糊查询
        if (null != businessSystemBeanForQuery.getIpBetween() && !"".equals(businessSystemBeanForQuery.getIpBetween())
                && null != businessSystemBeanForQuery.getIpEnd() && !"".equals(businessSystemBeanForQuery.getIpEnd()))
        {
            BigDecimal ipBetween = IPTools.getAlias(businessSystemBeanForQuery.getIpBetween());
            if (ipBetween.longValue() > 0)
            {
                businessSystemBeanForQuery.setIpBetweenLongValue(ipBetween.longValue());
            }
            BigDecimal ipEnd = IPTools.getAlias(businessSystemBeanForQuery.getIpEnd());
            if (ipEnd.longValue() > 0)
            {
                businessSystemBeanForQuery.setIpEndLongValue(ipEnd.longValue());
            }
        }
        Map res = null;
        try
        {
            if (businessSystemBeanForQuery.getSysIdForQuery() > 0)
            {
                res = BusinessSystemService.getInstance().getComputerList(businessSystemBeanForQuery, type);
            }

        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return res;
    }

    /**   
     * @Title: businessSystemComputerListNoSelectedForSPDB   
     * @Description:  浦发银行 平台管理 业务系统维护 查询待添加设备设备名称
     * @param request
     * @param getHostNameForBusSysNoSelectedSPDB
     * @return      
     * @author:  yuhe_zhang
     * @date:   2019年1月5日 下午8:27:55   
     */
    @RequestMapping("getHostNameForBusSysNoSelectedSPDB.do")
    @ResponseBody
    public Map<String, Object> getHostNameForBusSysNoSelectedSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));

        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getHostNameForBusSysNoSelectedSPDB(businessSystemBeanForQuery,
                type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    /**   
     * @Title: getIpForBusSysNoSelectedSPDB   
     * @Description: 浦发银行 平台管理 业务系统维护 查询待添加设备IP
     * @param request
     * @param businessSystemBeanForQuery
     * @return      
     * @author: yuhe_zhang
     * @date:   2019年1月14日 下午5:53:48   
     */
    @RequestMapping("getIpForBusSysNoSelectedSPDB.do")
    @ResponseBody
    public Map<String, Object> getIpForBusSysNoSelectedSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));

        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getIpForBusSysNoSelectedSPDB(businessSystemBeanForQuery, type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    /**   
     * @Title: getSysAdminForBusSysNoSelectedSPDB   
     * @Description: 浦发银行 平台管理 业务系统维护 查询待添加设备 系统管理员
     * @param request
     * @param businessSystemBeanForQuery
     * @return      
     * @author: yuhe_zhang
     * @date:   2019年1月14日 下午5:53:48   
     */
    @RequestMapping("getSysAdminForBusSysNoSelectedSPDB.do")
    @ResponseBody
    public Map<String, Object> getSysAdminForBusSysNoSelectedSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));

        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getSysAdminForBusSysNoSelectedSPDB(businessSystemBeanForQuery,
                type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    /**   
     * @Title: getSystemNameForBusSysNoSelectedSPDB   
     * @Description: 浦发银行 平台管理 业务系统维护 查询待添加设备 信息系统名称
     * @param request
     * @param businessSystemBeanForQuery
     * @return      
     * @author: yuhe_zhang
     * @date:   2019年1月14日 下午5:53:48   
     */
    @RequestMapping("getSystemNameForBusSysNoSelectedSPDB.do")
    @ResponseBody
    public Map<String, Object> getSystemNameForBusSysNoSelectedSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));

        Map<String, Object> res = null;
        try
        {
            res = BusinessSystemService.getInstance().getSystemNameForBusSysNoSelectedSPDB(businessSystemBeanForQuery,
                type);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @RequestMapping("businessSystemComputerListNoSelectedForSPDB.do")
    @ResponseBody
    public Map<String, Object> businessSystemComputerListNoSelectedForSPDB ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery, AgentModel filter )
    {
        int type = Integer.parseInt(request.getParameter(OPERASYSTYPE));

        Map<String, Object> res = null;
        try
        {

            res = BusinessSystemService.getInstance().getComputerListNoSelectedForSPDB(businessSystemBeanForQuery,
                filter, type);

        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return res;
    }

    /**
     * 
     * @Title: businessSystemComputerListNoSelected
     * @Description: 获取业务系统待添加设备列表
     * @param request
     * @param businessSystemBeanForQuery
     * @return
     * @return Map 返回类型
     * @throws
     * @变更记录 2015年10月19日 yunpeng_zhang
     */
    @RequestMapping("businessSystemComputerListNoSelected.do")
    @ResponseBody
    public Map businessSystemComputerListNoSelected ( HttpServletRequest request,
            BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        String stype = request.getParameter(OPERASYSTYPE);
        int type = Integer.parseInt(stype);
        String batchComputerName = request.getParameter("batchComputerName");
        Map<String, String> sortMap = ParseJson.getOrderBy(request);// 获取排序字段
        businessSystemBeanForQuery.setSortMap(sortMap);
        // 如果两个输入框都有值，则按ip段查询；否则只有一个输入框有值，则按ip模糊查询
        if (null != businessSystemBeanForQuery.getIpBetween() && !"".equals(businessSystemBeanForQuery.getIpBetween())
                && null != businessSystemBeanForQuery.getIpEnd() && !"".equals(businessSystemBeanForQuery.getIpEnd()))
        {
            BigDecimal ipBetween = IPTools.getAlias(businessSystemBeanForQuery.getIpBetween());
            if (ipBetween.longValue() > 0)
            {
                businessSystemBeanForQuery.setIpBetweenLongValue(ipBetween.longValue());
            }
            BigDecimal ipEnd = IPTools.getAlias(businessSystemBeanForQuery.getIpEnd());
            if (ipEnd.longValue() > 0)
            {
                businessSystemBeanForQuery.setIpEndLongValue(ipEnd.longValue());
            }
        }
        String dataCenter = request.getParameter("dataCenter");
        Map res = null;
        try
        {
            if (businessSystemBeanForQuery.getSysIdForQuery() > 0)
            {
                res = BusinessSystemService.getInstance().getComputerListNoSelected(businessSystemBeanForQuery, type,dataCenter,batchComputerName);
            }
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return res;
    }

    /** 一体化运维4.7.9 业务系统维护相关功能 on 2018-03-12 by yue_sun start  **/
    /**   
     * @Title: freezeBusinessSystem   
     * @Description: (冻结与解冻业务系统)   
     * @param request
     * @param freezeIds
     * @return      
     * @author: yue_sun 
     * @date:   2018年3月9日 上午9:51:22   
     */
    @RequestMapping("freezeBusinessSystem.do")
    @ResponseBody
    public Object freezeBusinessSystem ( HttpServletRequest request, String[] prjNames, boolean isFreeze )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            UserInfo userInfo = new UserInfo();
            userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
            userInfo.setFullName(sessionData.getUserName());
            BusinessSystemService.getInstance().freezeProject(prjNames, isFreeze, userInfo, Constants.IEAI_IEAI);
            resp.put(SUCCESS, true);
            if (isFreeze)
            {
                resp.put(MESSAGE, "冻结工程成功！");
            } else
            {
                resp.put(MESSAGE, "解冻工程成功！");
            }

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            if (isFreeze)
            {
                resp.put(MESSAGE, "冻结工程失败！");
            } else
            {
                resp.put(MESSAGE, "解冻工程失败！");
            }

        }
        return resp;
    }

    /**
     * 
     * @Title: deleteBusinessSystem 
     * @Description: 删除业务系统
     * @param request
     * @param deleteIds
     * @return
     * @return Object    返回类型 
     * @throws 
     * @变更记录 2015年11月3日  yunpeng_zhang
     */
    @RequestMapping("deleteBusinessSystem.do")
    @ResponseBody
    public Object deleteBusinessSystem ( HttpServletRequest request, Long[] deleteIds )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        boolean returnValue = false;
        try
        {

            // 检查定时任务是否用到该业务系统
            Map<String, Object> respc = BusinessSystemService.getInstance().chekcBusinessSystemUsedForTT(deleteIds,
                Constants.IEAI_TIMINGTASK);
            // 验证巡检是否用到该业务系统

            Map<String, Object> respcHc = HcComputerOperateManager.getInstance().checkSysBindHcComputer(deleteIds,
                Constants.IEAI_HEALTH_INSPECTION);

            if ((Boolean) respc.get(SUCCESS))
            {
                if (!(Boolean) respcHc.get(SUCCESS))
                {
                    resp = respcHc;
                    return resp;
                }
                List<Map<String,String>> proMap = com.ideal.ieai.server.jobscheduling.repository.project.ProjectManager.getInstance().getProjectListByIds(deleteIds);

                SessionData sessionData = SessionData.getSessionData(request);

                if(Environment.getInstance().getBooleanConfig(Environment.FJNX_SYNC_SYSTEM_SWITCH, false)) {
                    String deleteSysCodess = request.getParameter("deleteSysCodess");
                    deleteIds = BusinessSystemService.getInstance().queryBusinessSystemIds(deleteSysCodess,
                            Constants.IEAI_SUS);
                }
                returnValue = BusinessSystemService.getInstance().deleteBusinessSystem(deleteIds,
                            sessionData.getLoginName());

                if(Environment.getInstance().getBooleanConfig(Environment.FJNX_SYNC_SYSTEM_SWITCH, false)) {
                    SyncSystemService service = new SyncSystemService();
                    for(long iid : deleteIds) {
                        service.deleteProjectSyncMore(iid);
                    }
                }

                if (returnValue)
                {
                    boolean passSwitch = Environment.getInstance().getBooleanConfig(Environment.PASS_DM_SWITCH, false);
                    if (CollectionUtil.isNotEmpty(proMap) && passSwitch)
                    {
                        OrderMonitorManage orderMonitorManage = new OrderMonitorManage();
                        for (Map<String,String> map : proMap)
                        {
                            Float num = Float.parseFloat(map.get("proType"));
                            orderMonitorManage.deleteTemplate(map.get("proName"), num.longValue());
                        }
                    }

                    boolean excelSwitch = Environment.getInstance().getBooleanConfig(Environment.DELETE_IEAI_EXCEL_SWITCH, false);
                    if(CollectionUtil.isNotEmpty(proMap) && excelSwitch)
                    {
                        boolean isDm = false;
                        boolean isMain = false;
                        boolean isChild = false;
                        for(Map<String,String> map : proMap)
                        {
                            //判断是否是日启动工程


                            isDm = BusinessSystemManager.getInstance().isDayStartMainFlow(map.get("proName"));
                            if(isDm)
                            {
                                BusinessSystemManager.getInstance().deleteDayStartPro(map.get("proName"));
                                log.info("=========用户" + sessionData.getLoginName() + "删除ods日启动工程:" + map.get("proName") + "成功！");
                            }

                            //判断是否是主工程
                            isMain = BusinessSystemManager.getInstance().isMainPro(map.get("proName"));
                            if(isMain)
                            {
                                BusinessSystemManager.getInstance().deleteMainPro(map.get("proName"));
                                log.info("=========用户" + sessionData.getLoginName() + "删除ods主工程:" + map.get("proName") + "成功！");
                            }

                            //判断是否是子系统工程
                            isChild = BusinessSystemManager.getInstance().isChildPro(map.get("proName"));
                            if(isChild)
                            {
                                BusinessSystemManager.getInstance().deleteChildPro(map.get("proName"));
                                log.info("=========用户" + sessionData.getLoginName() + "删除ods子系统工程:" + map.get("proName") + "成功！");
                            }

                        }
                    }
                    
//                  OrderMonitorManage orderMonitorManage = new OrderMonitorManage();
                  String zbNames= "";
                  String ids="";
                  for (Map<String,String> map : proMap)
                  {
                      Float num = Float.parseFloat(map.get("proType"));
                      if(num.longValue()==Constants.IEAI_EMERGENCY_SWITCH) {
                          zbNames=zbNames+(StringUtils.isEmpty(zbNames)?"'":",'")+map.get("proName")+"'";
                          ids=ids+(StringUtils.isEmpty(ids)?"":",")+map.get("iid");
//                          orderMonitorManage.deleteTemplateForZB(map.get("proName"), Long.parseLong(map.get("proType")));
                      }
                  }
                  if((!StringUtils.isEmpty(zbNames))&&(!StringUtils.isEmpty(ids))) {
                      InstanceConfigService.getInstance().deleteInstanceForHd(zbNames.split(","), ids);
                  }
                    resp.put(SUCCESS, true);
                    resp.put(MESSAGE, "执行成功！");
                } else
                {
                    resp.put(SUCCESS, false);
                    resp.put(MESSAGE, "执行失败！");
                }
            } else
            {
                resp = respc;
            }

        } catch (Exception e)
        {
            log.error("deleteBusinessSystem is error",e);
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }

        return resp;
    }

    /**
     * 
     * @Title: initProjectDetail   
     * @Description: 工程详细信息初始化
     * @param: @param request
     * @param: @return      
     * @return: String      
     * @throws   
     * @author: yue_sun 
     * @date:   2017年7月25日 下午5:00:01
     */
    @RequestMapping("initProjectDetailForIEAI.do")
    public String initProjectDetail ( HttpServletRequest request )
    {
        return "jobScheduling/projects/projectDetailInfoForIEAI";
    }

    /***
     * 
     * <li>Description:导出作业调度下工程的PKG</li> 
     * <AUTHOR>
     * 2018年3月12日 
     * @param request
     * @param response
     * @param prjId
     * @param prjName
     * @throws RepositoryException
     * @throws DataFormatException
     * @throws UnsupportedEncodingException
     * return void
     */
    @RequestMapping("downloadPKGForIEAI.do")
    public void exportIEAIPKG ( HttpServletRequest request, HttpServletResponse response, String prjId, String prjName )
            throws RepositoryException, DataFormatException, UnsupportedEncodingException
    {
        SessionData sessionData = SessionData.getSessionData(request);

        Project prj = new Project();
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());

        List prjList = ProjectManager.getInstance().getProjectVerHistory(Long.valueOf(prjId), prjName, userInfo);
        Iterator iter = prjList.iterator();
        while (iter.hasNext())
        {
            ProjectInfo info = (ProjectInfo) iter.next();
            if (String.valueOf(info.getId()) == prjId || String.valueOf(info.getId()).equals(prjId))
            {
                prj = new Project(info);
            }
        }

        if (null == prjName)
        {
            log.warn("invalid prjName");
        }
        String prjVer = request.getParameter(Constants.RES_PRJ_VER);
        if (null == prjVer)
        {
            log.warn("invalid prjVer");
            prjVer = "0.0";
        }

        StringBuilder buff = new StringBuilder(prjName + "_");
        if (prjVer.indexOf('.') == -1)
        {
            log.warn("Invalid project version.");
        }
        String majVer = prjVer.substring(0, prjVer.indexOf('.'));
        String minVer = prjVer.substring(prjVer.indexOf('.') + 1, prjVer.length());
        buff.append(majVer);
        buff.append("_");
        buff.append(minVer);
        buff.append(".pkg");
        String fileVer = buff.toString();

        // Gets history informations of the project.
        response.setContentType("application/pkg;");
        response.setHeader("Content-Disposition",
            "attachment;filename=" + new String(fileVer.getBytes("gb2312"), "ISO8859-1"));
        try
        {
            // it is a ProjectInfo object stored in prjInfos.
            byte[] pkg = ProjectManager.getInstance().downloadProject(Long.valueOf(prjId), prjName, userInfo);
            ServletOutputStream stream = response.getOutputStream();
            if (pkg != null)
            {
                stream.write(pkg);
            }
            stream.close();
        } catch (IOException ex)
        {
            log.error(ex.toString());
        }
    }

    /** 一体化运维4.7.9 业务系统维护相关功能 on 2018-03-12 by yue_sun end  **/

    /**
     * 
     * @Title: saveBusinessSystem
     * @Description: 保存业务系统
     * @param request
     * @param incdata
     * @return
     * @return Object 返回类型
     * @throws
     * @变更记录 2015年10月30日 yunpeng_zhang
     */
    @RequestMapping("saveBusinessSystem.do")
    @ResponseBody
    public Object saveBusinessSystem ( HttpServletRequest request, String incdata )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        List<Map<String, Object>> businessSystems;
        List<Map<String, Object>> businessSystemsCheckList = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> timetaskCheckList = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> systemNumberCheckList = new ArrayList<Map<String, Object>>();
        try
        {
            businessSystems = ParseJson.JSON2List(incdata);
            for (Map<String, Object> checkmap : businessSystems)
            {
                if (-1 == (Long) Long.parseLong(checkmap.get("systemId").toString()))
                {
                    for (Map<String, Object> map : businessSystemsCheckList)
                    {
                        if (checkmap.get(SYSNAME).toString().equals(map.get(SYSNAME).toString())
                                && String.valueOf(checkmap.get("prjType")).equals(String.valueOf(map.get("prjType"))))
                        {
                            resp.put(SUCCESS, false);
                            resp.put(MESSAGE, "业务系统名称【" + checkmap.get(SYSNAME).toString() + "】重复！");
                            return resp;
                        }
                    }
                    businessSystemsCheckList.add(checkmap);
                }
                if (String.valueOf(checkmap.get("prjType")).equals("5"))
                {
                    timetaskCheckList.add(checkmap);
                }
                if (StringUtils.isNotEmpty(String.valueOf(checkmap.get("systemNumber")))){
                    systemNumberCheckList.add(checkmap);
                }
            }

            if(ServerEnv.getInstance().getBooleanConfig(Environment.SYSTEMNAME_CHECK_BYHS_SWITCH, false)){
                List<String> returnList = BusinessSystemService.getInstance()
                        .checkBusinessSystemName(businessSystemsCheckList);
                if (!returnList.isEmpty())
                {
                    resp.put(SUCCESS, false);
                    resp.put(MESSAGE, "业务系统名称【" + returnList.get(0).toString() + "】重复！");
                    return resp;
                }
            }else{
                List<String> returnList = BusinessSystemService.getInstance()
                        .checkBusinessSystemNameAndProtype(businessSystemsCheckList);
                if (!returnList.isEmpty())
                {
                    resp.put(SUCCESS, false);
                    resp.put(MESSAGE, "业务系统名称【" + returnList.get(0).toString() + "】重复！");
                    return resp;
                }
            }

            //系统编码不可重复校验开始
            List<String> systemNumberList = BusinessSystemService.getInstance().checkSystemCodeNew(systemNumberCheckList);
            if (!systemNumberList.isEmpty())
            {
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "系统编码【" + systemNumberList.get(0).toString() + "】重复！");
                return resp;
            }

            // 新增定时任务名称不可重复
            if (null != businessSystems && !businessSystems.isEmpty() && !timetaskCheckList.isEmpty())
            {
                List<String> timeTaskReturnList = BusinessSystemService.getInstance()
                        .checkTimeTaskSystemNameAndProtype(timetaskCheckList);
                if (!timeTaskReturnList.isEmpty())
                {
                    resp.put(SUCCESS, false);
                    resp.put(MESSAGE, "业务系统名称【" + timeTaskReturnList.get(0).toString() + "】重复！");
                    return resp;
                }
            }

            SessionData sessionData = SessionData.getSessionData(request);
            long userId = Long.parseLong(sessionData.getUserInnerCode());
            Map<String,Object> respMap = new HashMap<String,Object>();

            if(Environment.getInstance().getBooleanConfig(Environment.SYSTEM_SAVE_NEW_SWITCH, false)) {
                BusinessSystemService.getInstance().saveBusinessSystem(businessSystems, sessionData.getUserName(), userId);
                respMap.put(Constants.SUS_SUCCESS, true);
            }else {
                respMap = BusinessSystemService.getInstance().saveBusinessSystem(businessSystems,
                        sessionData.getUserName(), userId, Constants.IEAI_HEALTH_INSPECTION);
            }

            boolean pfIpmpSystemSwitch = ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_IPMP_SYSTEM_BAND_SWITCH, false);
            if (Boolean.parseBoolean(String.valueOf(respMap.get(Constants.SUS_SUCCESS))))
            {
                resp.put(SUCCESS, true);
                resp.put(MESSAGE, "执行成功！"); 
                if(pfIpmpSystemSwitch){
                    String mes = String.valueOf(respMap.get(Constants.SUS_MESSAGE));
                    if(!StringUtils.isBlank(mes)){
                        resp.put(MESSAGE, "保存失败！"+mes+"系统在IPMP平台不存在"); 
                    }
                }
            } else
            {
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "执行失败！");
                if(pfIpmpSystemSwitch){
                    String mes = String.valueOf(respMap.get(Constants.SUS_MESSAGE));
                    if(!StringUtils.isBlank(mes)){
                        resp.put(MESSAGE, "保存失败！"+mes+"系统在IPMP平台不存在"); 
                    }
                }
            }

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }

        return resp;
    }

    @RequestMapping("getProInfo.do")
    @ResponseBody
    public Object getProInfo ( HttpServletRequest request, long prjid )
    {
        BusinessSystemBean returnValue = null;
        try
        {
            returnValue = BusinessSystemService.getInstance().getProInfo(prjid, Constants.IEAI_HEALTH_INSPECTION);

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }

        return returnValue;
    }

    @RequestMapping("saveProInfo.do")
    @ResponseBody
    public Object saveProInfo ( HttpServletRequest request, String sysName, long projectid, String sysParam,
            String sysDesc, int priority, String sysType, String handStart)
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        BusinessSystemBean bean = null;
        proInfoMap.put(projectid,sysType);
        try
        {

            bean = new BusinessSystemBean();
            bean.setSystemId(projectid);
            bean.setMailSendStatus(false);
            bean.setSysParam(sysParam);
            bean.setSysDesc(sysDesc);
            bean.setPriority(priority);
            bean.setSysType(sysType);
            bean.setSysName(sysName);
//            bean.setHandStart(handStart);
            boolean returnValue = BusinessSystemService.getInstance().saveProInfo(bean,
                Constants.IEAI_HEALTH_INSPECTION);
            if (returnValue)
            {
                resp.put(SUCCESS, true);
                resp.put("proInfoMap",proInfoMap);
                resp.put(MESSAGE, "系统属性信息保存成功");

                if (Environment.getInstance().getDgProjectParamSwitch() && handStart != null && handStart != "") {
                    TopoLogicalThread topoLogicalThread = new TopoLogicalThread(bean, TopoLogicalThread.UPTYPE);
                    topoLogicalThread.start();
                }
            } else
            {
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "系统属性信息保存失败！");
            }

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }

        return resp;
    }

    /**
     * 
     * @Title: saveBusinessSystemRelation
     * @Description: 保存业务系统与设备的关联)
     * @param request
     * @param incdata
     * @param systemId
     * @return
     * @return Object 返回类型
     * @throws
     * @变更记录 2015年10月31日 yunpeng_zhang
     */
    @RequestMapping("saveBusinessSystemRelation.do")
    @ResponseBody
    public Object saveBusinessSystemRelation ( HttpServletRequest request, String incdata, long systemId )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        List<Map<String, Object>> businessSystems;
        try
        {

            businessSystems = ParseJson.JSON2List(incdata);
            SessionData sessionData = SessionData.getSessionData(request);

            String opersystype = request.getParameter(OPERASYSTYPE);
            Integer bustype = Integer.parseInt(opersystype);

            boolean returnValue = BusinessSystemService.getInstance().saveBusinessSystemRelation_wr(businessSystems,
                systemId, sessionData.getLoginName(), bustype);

            if (returnValue)
            {
                resp.put(SUCCESS, true);
                resp.put(MESSAGE, "操作执行成功！");
            } else
            {
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "执行操作失败！");
            }

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }

        return resp;
    }

    /**
     * 
     * @Title: deleteBusinessSystemRelation
     * @Description: 删除业务系统与设备关联
     * @param request
     * @param incdata
     * @param systemId
     * @return
     * @return Object 返回类型
     * @throws
     * @变更记录 2015年10月30日 yunpeng_zhang
     */
    @RequestMapping("deleteBusinessSystemRelation.do")
    @ResponseBody
    public Object deleteBusinessSystemRelation ( HttpServletRequest request, Long[] deleteIds, long systemId )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            
            //获取业务系统类型
            long groupIdForSysid = BusinessSystemService.getInstance().getGroupIdForSysid(systemId);
            
            if(groupIdForSysid== Constants.IEAI_HEALTH_INSPECTION) {
                //移除前验证是否存在已启动的巡检的设备被移除，如果存在提示用户不能进行移除
                String checkResult = BusinessSystemService.getInstance().checkHcStartForRemoveRelation(deleteIds, systemId);
                if(!"".equals(checkResult)) {
                    resp.put(SUCCESS, false);
                    resp.put(MESSAGE, "存在以下设备["+checkResult+"]当前巡检状态为启动状态！");
                    return resp;
                }
                
                //移除系统下设备时验证新巡检是否启动
                boolean  suppercheck = ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false);
                if(suppercheck) {
                    String checkNewHCResult = BusinessSystemService.getInstance().checkNewHcStartForRemoveRelation(deleteIds, systemId);
                    if(!"".equals(checkNewHCResult)) {
                        resp.put(SUCCESS, false);
                        resp.put(MESSAGE, " 存在以下设备["+checkNewHCResult+"]在新巡检中是启动状态！");
                        return resp;
                    }
                }
            }else if(groupIdForSysid== Constants.IEAI_TIMINGTASK){
                // V4.7.23 校验定时任务下业务系统绑定的ip是否有正在运行的定时任务记录,如存在记录,则不进行删除操作
                resp = BusinessSystemService.getInstance().validBusinessSystemRelationWithTimer(deleteIds, systemId);
                if (!(Boolean) resp.get(SUCCESS))
                {
                    return resp;
                }
            }
            boolean returnValue = BusinessSystemService.getInstance().deleteBusinessSystemRelation(deleteIds,
                systemId, sessionData.getLoginName());
            if (returnValue)
            {
                resp.put(SUCCESS, true);
                resp.put(MESSAGE, "操作执行成功！");
            } else
            {
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "执行操作失败！");
            }
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }

        return resp;
    }
    
    /**
     * 
     * <li>Description:此方法未启用</li> 
     * <AUTHOR>
     * 2021年6月5日 
     * @param request
     * @param deleteIds
     * @param systemId
     * @return
     * return Object
     */
    @RequestMapping("deleteBusinessSystemRelationValidHc.do")
    @ResponseBody
    public Object deleteBusinessSystemRelationValidHc ( HttpServletRequest request, Long[] deleteIds, long systemId ) {
        Map<String, Object> resp = new HashMap<String, Object>();
        //移除前验证是否存在已启动的巡检的设备被移除，如果存在提示用户不能进行移除
        String checkResult = "";
        try
        {
            checkResult = BusinessSystemService.getInstance().checkHcStartForRemoveRelation(deleteIds, systemId);
            if(!"".equals(checkResult)) {
                resp.put(SUCCESS, true);
                resp.put("isHave", true);
                resp.put(MESSAGE, "存在以下设备["+checkResult+"]当前巡检状态为启动状态！");
                return resp;
            }
        } catch (RepositoryException e)
        {
            log.error("验证是否存在启动巡检状态设备失败！", e);
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, "验证失败");
        }
        resp.put(SUCCESS, true);
        resp.put("isHave", false);
        resp.put(MESSAGE, "验证通过");
        return resp;
    }

    /**
     * <li>Description:业务系统维护绑定用户组及用户</li> 
     * <AUTHOR>
     * 2019年3月12日 
     * @param request
     * @return
     * return String
     */
    @RequestMapping("sysConfigUserGroup.do")
    public String sysConfigUserGroup ( HttpServletRequest request, String sysIdForQuery )
    {
        BusinessSystemService service = new BusinessSystemService();
        List sysConfigUser = null;
        List sysConfigGUser = null;
        try
        {
            sysConfigUser = service.getSysConfigUser(sysIdForQuery, Constants.IEAI_IEAI_BASIC);
            sysConfigGUser = service.getSysConfigGUser(sysIdForQuery, Constants.IEAI_IEAI_BASIC);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " " + e);
        }
        request.setAttribute(SYSCONFIGUSER, sysConfigUser);
        request.setAttribute(SYSCONFIGGUSER, sysConfigGUser);
        return "/businessSystem/businessSystemConfigUserGroup";
    }

    /**
     * <li>Description:业务系统维护绑定用户组及用户</li> 
     * <AUTHOR>
     * 2019年3月12日 
     * @param request
     * @return
     * return String
     */
    @RequestMapping("sysConfigUser.do")
    public String sysConfigUser ( HttpServletRequest request, String sysIdForQuery )
    {
        BusinessSystemService service = new BusinessSystemService();
        List sysConfigUser = null;
        List sysConfigGUser = null;
        try
        {
            sysConfigUser = service.getSysConfigUser(sysIdForQuery, Constants.IEAI_IEAI_BASIC);
            sysConfigGUser = service.getSysConfigGUser(sysIdForQuery, Constants.IEAI_IEAI_BASIC);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " " + e);
        }
        request.setAttribute(SYSCONFIGUSER, sysConfigUser);
        request.setAttribute(SYSCONFIGGUSER, sysConfigGUser);
        return "/businessSystem/businessSystemConfigUser";
    }

    /**
     * <li>Description:业务系统绑定用户组及用户保存操作</li> 
     * <AUTHOR>
     * 2019年3月12日 
     * @param jsonData
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("saveBusinessOfUserGroup.do")
    @ResponseBody
    public Object saveUserGroupInfo ( String jsonData, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        UserGroupService service = new UserGroupService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            List<UserGroupInfo> groupInfoList = json2Info(jsonData);
            resp = service.saveUserGroupInfo(groupInfoList);
            log.info("用户名：" + userName + ",操作:用户信息保存。");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    private List<UserGroupInfo> json2Info ( String jsonData )
    {
        List<UserGroupInfo> list = new ArrayList<UserGroupInfo>();
        JSONArray jsonArr;
        JSONObject json2 = null;
        try
        {
            if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData)
            {
                jsonArr = new JSONArray(jsonData);
                for (int i = 0; i < jsonArr.length(); i++)
                {
                    json2 = jsonArr.getJSONObject(i);
                    UserGroupInfo bean = new UserGroupInfo();
                    bean.setIid(json2.getLong("iid"));
                    bean.setIposition(json2.getLong("duty"));
                    list.add(bean);
                }
            }
        } catch (JSONException jsonexception)
        {
            log.error(jsonexception);
        }
        return list;
    }

    /**
     * 保存业务与系统与用户关系
     * @Title: saveUserToBussSys   
     * @Description: 保存业务与系统与用户关系
     * @param jsonData
     * @param request
     * @return      
     * @author: Administrator 
     * @date:   2019年3月13日 上午9:05:07
     */
    @RequestMapping("saveUserToBussSys.do")
    @ResponseBody
    public Object saveUserToBussSys ( String[] userids, String sysId, String tabflag, String operFlag,
            String[] olduserids, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        BusinessSystemService service = new BusinessSystemService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            log.info("用户名：" + userName + ",操作:saveUserToBussSys");
            if ("1".equals(operFlag) && (null == userids || userids.length == 0))
            {
                service.saveUserToBussSysForGroupNotChk(olduserids, sysId);
            } else
            {
                service.saveUserToBussSys(userids, sysId, tabflag);
            }
            List sysConfigUser = service.getSysConfigUser(sysId, Constants.IEAI_IEAI_BASIC);
            List sysConfigGUser = service.getSysConfigGUser(sysId, Constants.IEAI_IEAI_BASIC);
            resp.put(SYSCONFIGUSER, sysConfigUser);
            resp.put(SYSCONFIGGUSER, sysConfigGUser);
            resp.put(SUCCESS, true);
            resp.put(MESSAGE, "保存成功!");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * @Title: exportPkgS   
     * @Description: 锦州版本批量导出   
     * @param request
     * @param response
     * @param jsonData      
     * @author: yunpeng_zhang 
     * @date:   2019年4月28日 下午4:29:42
     */
    @RequestMapping("exportPkgS.do")
    public void exportPkgS ( HttpServletRequest request, HttpServletResponse response, String jsonData )
    {

        SessionData sessionData = SessionData.getSessionData(request);
        File file = null;
        FileOutputStream fous = null;
        OutputStream toClient = null;
        try
        {

            List<File> files = new ArrayList<File>();
            List<Map<String, String>> jsonList = JSON.parseObject(jsonData, List.class);
            for (int i = 0; i < jsonList.size(); i++)
            {
                Map<String, String> mapT = jsonList.get(i);
                String prjId = mapT.get("prjId");
                String prjVer = mapT.get("prjVer");
                String prjName = mapT.get("prjName");
                files.add(this.exportPkgFile(sessionData, prjId, prjVer, prjName));
            }
            // 多文件导出zip压缩包
            if (files.size() > 0)
            {
                /**
                 * 创建一个临时压缩文件， 我们会把文件流全部注入到这个文件中 这里的文件你可以自定义是.rar还是.zip
                 */
                file = new File(Environment.getInstance().getIEAIHome() + File.separator + "template" + File.separator
                        + "temp" + File.separator + UUID.uuid() + ".zip");
                if (!file.exists())
                {
                    file.createNewFile();
                }

                // 创建文件输出流
                fous = new FileOutputStream(file);
                ZipOutputStream zipOut = new ZipOutputStream(fous);
                ZipUtil.zipFile(files, zipOut);
                fous.flush();
                fous.close();
                // 以流的形式下载文件。
                InputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
                byte[] buffer = new byte[fis.available()];
                fis.read(buffer);
                fis.close();
                // 清空response
                response.reset();

                toClient = new BufferedOutputStream(response.getOutputStream());
                response.reset();
                response.setContentType("application/octet-stream");
                response.setHeader("Content-disposition", "attachment; filename="
                        + new String(("工程导出" + PoiUtil.getRandomFileName() + ".zip").getBytes("gb2312"), "ISO8859-1")); // 解决火狐中文乱码

                response.setCharacterEncoding("GBK");
                // 解决文件名称中带有[1]等字样
                response.setHeader("Pragma", "no-cache");
                response.setHeader("Cache-Control", "no-cache");
                response.setDateHeader("Expires", 0);

                toClient.write(buffer);
                toClient.flush();
                toClient.close();
            }

        } catch (Exception e)
        {
            e.printStackTrace();
        } finally
        {
            try
            {
                if (fous != null)
                {
                    fous.close();
                }
                if (toClient != null)
                {
                    toClient.close();
                }
                if (file != null)
                {
                    File f = new File(file.getPath());
                    f.delete();
                }

            } catch (Exception e)
            {
                e.printStackTrace();
            }
        }

    }

    /**
     * 
     * <li>Description:获取单个pkg文件</li> 
     * <AUTHOR>
     * 2019年4月11日 
     * @param sessionData
     * @param prjId
     * @param prjVer
     * @param prjName
     * @return
     * return File
     */

    public File exportPkgFile ( SessionData sessionData, String prjId, String prjVer, String prjName )
    {
        OutputStream out = null;
        java.io.File xlsFile = null;
        /*
         * response.setContentType("application/vnd.ms-excel; GBK");
         * response.setHeader("Content-disposition", "attachment;filename=" +
         * PoiUtil.getRandomFileName() + ".xls"); response.setCharacterEncoding("UTF-8");
         */
        try
        {
            Map returnMap;
            try
            {
                returnMap = this.exportIEAIPKG_JZ(sessionData, prjId, prjVer, prjName);
                String excelName = (String) returnMap.get("pkgname");
                byte[] contentInBytes = (byte[]) returnMap.get("byteSz");
                xlsFile = new java.io.File(excelName);
                out = new FileOutputStream(xlsFile);
                out.write(contentInBytes);
                out.flush();
                out.close();
            } catch (DataFormatException e)
            {
                e.printStackTrace();
            }

        } catch (RepositoryException e1)
        {
            e1.printStackTrace();
        } catch (IOException e)
        {
            e.printStackTrace();
        } finally
        {
            try
            {
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e)
            {
                e.printStackTrace();
            }
        }
        return xlsFile;
    }

    /**
     * 
     * <li>获取pkg byte型数组和pkg名</li> 
     * <AUTHOR>
     * 2019年4月11日 
     * @param sessionData
     * @param prjId
     * @param prjVer
     * @param prjName
     * @return
     * @throws RepositoryException
     * @throws DataFormatException
     * @throws UnsupportedEncodingException
     * return Map
     */
    public Map exportIEAIPKG_JZ ( SessionData sessionData, String prjId, String prjVer, String prjName )
            throws RepositoryException, DataFormatException, UnsupportedEncodingException
    {

        Project prj = new Project();
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());

        List prjList = ProjectManager.getInstance().getProjectVerHistory(Long.valueOf(prjId), prjName, userInfo);
        Iterator iter = prjList.iterator();
        while (iter.hasNext())
        {
            ProjectInfo info = (ProjectInfo) iter.next();
            if (String.valueOf(info.getId()) == prjId || String.valueOf(info.getId()).equals(prjId))
            {
                prj = new Project(info);
            }
        }

        if (null == prjName)
        {
            log.warn("invalid prjName");
        }
        // String prjVer = request.getParameter(Constants.RES_PRJ_VER);
        if (null == prjVer)
        {
            log.warn("invalid prjVer");
            prjVer = "0.0";
        }

        StringBuilder buff = new StringBuilder(prjName + "_");
        if (prjVer.indexOf('.') == -1)
        {
            log.warn("Invalid project version.");
        }
        String majVer = prjVer.substring(0, prjVer.indexOf('.'));
        String minVer = prjVer.substring(prjVer.indexOf('.') + 1, prjVer.length());
        buff.append(majVer);
        buff.append("_");
        buff.append(minVer);
        buff.append(".pkg");
        Map returnMap = new HashMap();
        returnMap.put("pkgname", buff.toString());
        try
        {
            // it is a ProjectInfo object stored in prjInfos.
            byte[] pkg = ProjectManager.getInstance().downloadProject(Long.valueOf(prjId), prjName, userInfo);
            returnMap.put("byteSz", pkg);
        } catch (IOException ex)
        {
            log.error(ex.toString());
        }
        return returnMap;
    }
    
    /**
     * 
     * <li>Description:健康巡检-业务系统维护对接XMDB实现业务系统自动同步</li> 
     * <AUTHOR>
     * 2021年6月17日 
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("manuallyforxmdb.do")
    @ResponseBody
    public Object synchronousDataManuallyforXMDB ( HttpServletRequest request )
    {

        Map<String, Object> resp = new HashMap<>();
        resp.put(SUCCESS, true);
        resp.put(MESSAGE, "操作成功!");
        try
        {
            SessionData session = SessionData.getSessionData(request);

            XMDBSyncBSToHcThread thread = new XMDBSyncBSToHcThread(session.getUserName(),
                    Long.parseLong(session.getUserInnerCode()));//作为service类调用其函数成员
            List<SysModel> xmdb_sys = thread.getSystemList();//从xmdb获取数据
            
            if(xmdb_sys != null && !xmdb_sys.isEmpty()) {
                thread.saveSysList(xmdb_sys, Constants.IEAI_HEALTH_INSPECTION);//保存数据到IEAI_GF_HC_XMDB_SYS
                List<Map<String, Object>> bsList = thread.getXMDBDate(Constants.IEAI_HEALTH_INSPECTION);//联合ieai_project
                if (bsList != null && !bsList.isEmpty())
                {
                    //装配并保存
                    thread.assemblydataAndSave(bsList);
                }
            }
        } catch (Exception e)
        {
            resp.put(SUCCESS, true);
            resp.put(MESSAGE, "操作失败!");
            log.error("操作失败", e);
        }
        return resp;
    }


    @RequestMapping("checkIpExist.do")
    @ResponseBody
    public Map checkIpExist ( HttpServletRequest request ) throws RepositoryException
    {
        String errorMsg = "";
        String uncheckip = "";
        Map map = new HashMap();
        String stype = request.getParameter(OPERASYSTYPE);
        int type = Integer.parseInt(stype);
        try
        {
            // 接收参数
            String batchComputerName = request.getParameter("batchComputerName");
            String systemId = request.getParameter("systemId");
            uncheckip = BusinessSystemService.getInstance().getAgentListBySysId(batchComputerName,Long.parseLong(systemId), type);
            map.put(MESSAGE, uncheckip);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, errorMsg);
            log.error("TTManageController.getComputerListNoSelectedForTT Exception error:" + e);
        }
        return map;
    }

    @RequestMapping("filterIp.do")
    @ResponseBody
    public Map filterIp ( HttpServletRequest request ) throws RepositoryException
    {
        String errorMsg = "";
        String filterip = "";
        Map map = new HashMap();
        int type = Constants.IEAI_TIMINGTASK;
        try
        {
            // 接收参数
            String allip = request.getParameter("allip");
            String uncheckip = request.getParameter("uncheckip");

            filterip = BusinessSystemService.getInstance().filterAllIp(allip,uncheckip);
            map.put(MESSAGE, filterip);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, errorMsg);
            log.error("BusinessSystemController.filterIp Exception error:" + e);
        }
        return map;
    }
    @RequestMapping("rollbackPKGIEAI.do")
    @ResponseBody
    public Map<String, Object> saveDelayStart(HttpServletRequest request, BatchStartBean model,String prjId, String prjName , HttpServletResponse response )
            throws RepositoryException, DataFormatException, UnsupportedEncodingException{
        SessionData sessionData = SessionData.getSessionData(request);
        String loginName = sessionData.getLoginName();
        model.setLoginUser(loginName);
        model.setPassWord(Security.encrypt(model.getPassWord().toCharArray()));


        Project prj = new Project();
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());

        List prjList = ProjectManager.getInstance().getProjectVerHistory(Long.valueOf(prjId), prjName, userInfo);
        Iterator iter = prjList.iterator();
        while (iter.hasNext())
        {
            ProjectInfo info = (ProjectInfo) iter.next();
            if (String.valueOf(info.getId()) == prjId || String.valueOf(info.getId()).equals(prjId))
            {
                prj = new Project(info);
            }
        }

        if (null == prjName)
        {
            log.warn("invalid prjName");
        }
        String prjVer = request.getParameter(Constants.RES_PRJ_VER);
        if (null == prjVer)
        {
            log.warn("invalid prjVer");
            prjVer = "0.0";
        }

        StringBuilder buff = new StringBuilder(prjName + "_");
        if (prjVer.indexOf('.') == -1)
        {
            log.warn("Invalid project version.");
        }
        String majVer = prjVer.substring(0, prjVer.indexOf('.'));
        String minVer = prjVer.substring(prjVer.indexOf('.') + 1, prjVer.length());
        buff.append(majVer);
        buff.append("_");
        buff.append(minVer);
        buff.append(".pkg");
        String fileVer = buff.toString();

        // Gets history informations of the project.
        response.setContentType("application/pkg;");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + new String(fileVer.getBytes("gb2312"), "ISO8859-1"));
        Map<String, Object> map = new HashMap();
        try
        {
            String _sessionId = sessionData.getSessionId();
            // it is a ProjectInfo object stored in prjInfos.

            byte[] pkg = ProjectManager.getInstance().downloadProject(Long.valueOf(prjId), prjName, userInfo);
            map=   BusinessSystemService.getInstance().rollbackPKG(model,pkg,_sessionId,prjName);
        } catch (IOException ex)
        {
            log.error(ex.toString());
        }
        return map;
    }


    @RequestMapping("promotionBusinessSystem.do")
    @ResponseBody
    public Map<String, Object> promotionBusinessSystem(HttpServletRequest request, HttpServletResponse response, String[] systemIds,  String[] excelPrjs) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        SessionData sessionData = SessionData.getSessionData(request);
        String loginName = sessionData.getLoginName();
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());

        DbUtilUpLoadExcel dbUtilUpLoadExcel = DbUtilUpLoadExcel.getInstance();
        String rsStr = "{success:true}";
        String messages = "";
        File fileNew = null;
        if ("".equals(CZ_PROMOTION_IP)){
            map.put("success", false);
            map.put("msg", "请配置晋级地址");
            return map;
        }
        for (int i = 0; i < systemIds.length; i++) {
            String systemId = systemIds[i];
            Integer excelPrj = Integer.parseInt(excelPrjs[i]);
            String prjName = ProjectManager.getInstance().getPrjNameById(systemId);
            if (excelPrj > 0) {
                try {
                    // 构建调用参数
                    String apiUrl = "http://" + CZ_PROMOTION_IP + ":8888/promotionExcelSystem.do";

                    // 准备请求参数
                    Map<String, String> params = new HashMap<>();
                    params.put("prjName", prjName);
                    params.put("loginName", loginName);
                    params.put("userInnerCode", sessionData.getUserInnerCode());
                    params.put("userName", sessionData.getUserName());

                    // 创建 HTTP 请求头
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

                    // 构建请求实体
                    MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
                    requestBody.setAll(params);
                    HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

                    // 使用 RestTemplate 调用接口
                    // 创建带超时的RestTemplate
                    RestTemplate restTemplate = createRestTemplateWithTimeout(300000, 300000);
                    ResponseEntity<String> responseEntity = restTemplate.exchange(apiUrl, HttpMethod.POST, requestEntity, String.class);
                    String responseBody = responseEntity.getBody();
                    if (responseBody == null) {
                        map.put("success", false);
                        map.put("msg", "API返回空响应");
                        return map;
                    }

                    // 检查内容类型
                    if (responseBody.trim().startsWith("{") || responseBody.trim().startsWith("[")) {
                        // JSON格式处理
                        ObjectMapper mapper = new ObjectMapper();
                        Map<String, Object> apiResponse = mapper.readValue(responseBody, Map.class);

                        if (apiResponse != null) {
                           if (Boolean.TRUE.equals(apiResponse.get("isOk"))){
                               map.put("success", true);
                               map.put("msg", "晋级成功");
                           }else {
                               map.put("success", false);
                               map.put("msg", apiResponse.get("msg"));
                           }

                        } else {
                            String errorMsg = apiResponse != null ?
                                    String.valueOf(apiResponse.get("msg")) : "API返回错误";
                            map.put("success", false);
                            map.put("msg", errorMsg);
                        }
                    } else {
                        // HTML格式处理
                        log.error("API返回HTML响应: " + responseBody);
                        map.put("success", false);
                        map.put("msg", "API返回了HTML响应，可能是错误页面");
                    }
                } catch (Exception e) {
                    map.put("success", false);
                    map.put("msg", "调用晋级失败: " + e.getMessage());
                    log.error("调用晋级失败", e);
                    return map;
                }
            } else {
                byte[] pkg = ProjectManager.getInstance().downloadProject(Long.valueOf(systemId), prjName, userInfo);
                ClientSession clientSession = new ClientSession();
                clientSession.setClient("Server");
                clientSession.setUserLoginName(loginName);
                Session session = ConnectionAPI.login(CZ_PROMOTION_IP, 6666, clientSession, "admin", 0, null, null, false);

                Response responseStudio = new Response();
                responseStudio = ProjectAPI.updateProject(session, prjName, "远程发布", pkg);

                String uuid = (String) responseStudio.getRetData();
                if (uuid == null) {
                    log.info("remote relaese prj'name : " + prjName);
                    map.put("success", false);
                    map.put("msg", "晋级失败！");
                    return map;
                } else {
                    map.put("success", true);
                    map.put("msg", "晋级成功");
                }
            }
        }
        return map;
    }
    @RequestMapping("promotionBusinessSystemLocal.do")
    @ResponseBody
    public Map<String, Object> promotionBusinessSystem(HttpServletRequest request, HttpServletResponse response, String[] systemIds,  String[] excelPrjs,String local) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        SessionData sessionData = SessionData.getSessionData(request);
        String loginName = sessionData.getLoginName();
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());

        DbUtilUpLoadExcel dbUtilUpLoadExcel = DbUtilUpLoadExcel.getInstance();
        String rsStr = "{success:true}";
        String messages = "";
        File fileNew = null;
        if ("".equals(local)){
            map.put("success", false);
            map.put("msg", "请配置晋级地址");
            return map;
        }
        for (int i = 0; i < systemIds.length; i++) {
            String systemId = systemIds[i];
            Integer excelPrj = Integer.parseInt(excelPrjs[i]);
            String prjName = ProjectManager.getInstance().getPrjNameById(systemId);
            if (excelPrj > 0) {
                try {
                    // 构建调用参数
                    String apiUrl = "http://" + local + ":8888/promotionExcelSystem.do";

                    // 准备请求参数
                    Map<String, String> params = new HashMap<>();
                    params.put("prjName", prjName);
                    params.put("loginName", loginName);
                    params.put("userInnerCode", sessionData.getUserInnerCode());
                    params.put("userName", sessionData.getUserName());

                    // 创建 HTTP 请求头
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

                    // 构建请求实体
                    MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
                    requestBody.setAll(params);
                    HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

                    // 使用 RestTemplate 调用接口
                    // 创建带超时的RestTemplate
                    RestTemplate restTemplate = createRestTemplateWithTimeout(300000, 300000);
                    ResponseEntity<String> responseEntity = restTemplate.exchange(apiUrl, HttpMethod.POST, requestEntity, String.class);
                    String responseBody = responseEntity.getBody();
                    if (responseBody == null) {
                        map.put("success", false);
                        map.put("message", "API返回空响应");
                        return map;
                    }

                    // 检查内容类型
                    if (responseBody.trim().startsWith("{") || responseBody.trim().startsWith("[")) {
                        // JSON格式处理
                        ObjectMapper mapper = new ObjectMapper();
                        Map<String, Object> apiResponse = mapper.readValue(responseBody, Map.class);

                        if (apiResponse != null) {
                           if (Boolean.TRUE.equals(apiResponse.get("isOk"))){
                               map.put("success", true);
                               map.put("message", "回退成功");
                           }else {
                               map.put("success", false);
                               map.put("message", apiResponse.get("msg"));
                           }

                        } else {
                            String errorMsg = apiResponse != null ?
                                    String.valueOf(apiResponse.get("msg")) : "API返回错误";
                            map.put("success", false);
                            map.put("message", errorMsg);
                        }
                    } else {
                        // HTML格式处理
                        log.error("API返回HTML响应: " + responseBody);
                        map.put("success", false);
                        map.put("message", "API返回了HTML响应，可能是错误页面");
                    }
                } catch (Exception e) {
                    map.put("success", false);
                    map.put("message", "调用回退失败: " + e.getMessage());
                    log.error("调用回退失败", e);
                    return map;
                }
            } else {
                byte[] pkg = ProjectManager.getInstance().downloadProject(Long.valueOf(systemId), prjName, userInfo);
                ClientSession clientSession = new ClientSession();
                clientSession.setClient("Server");
                clientSession.setUserLoginName(loginName);
                Session session = ConnectionAPI.login(local, 6666, clientSession, "admin", 0, null, null, false);

                Response responseStudio = new Response();
                responseStudio = ProjectAPI.updateProject(session, prjName, "远程发布", pkg);

                String uuid = (String) responseStudio.getRetData();
                if (uuid == null) {
                    log.info("remote relaese prj'name : " + prjName);
                    map.put("success", false);
                    map.put("msg", "晋级失败！");
                    return map;
                } else {
                    map.put("success", true);
                    map.put("msg", "晋级成功");
                }
            }
        }
        return map;
    }

    @RequestMapping("promotionExcelSystem.do")
    @ResponseBody
    public Map<String, Object> promotionExcelSystem(HttpServletRequest request, HttpServletResponse response, String prjName, String loginName, String userInnerCode, String userName) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        SessionData sessionData = SessionData.getSessionData(request);
        Long valueOf = Long.valueOf(userInnerCode);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(valueOf);
        userInfo.setFullName(userName);

        DbUtilUpLoadExcel dbUtilUpLoadExcel = DbUtilUpLoadExcel.getInstance();
        String rsStr = "{success:true}";
        String messages = "";
        File directory = null;
        try {
            // 1. 导出Excel文件
            String uuid = ProjectsService.getInstance().exportTaskExcel(prjName, response);
            if ("-1".equals(uuid)) {
                map.put("success", false);
                map.put("msg", "子系统不能晋级");
                return map;
            }
            String directoryPath = Environment.getInstance().getIEAIHome() + File.separator + "downTemp" + File.separator + uuid;
             directory = new File(directoryPath);

            // 2. 动态获取导出的Excel文件名（包含扩展名）
            File excelDir = new File(directoryPath);
            File[] excelFiles = excelDir.listFiles((dir, name) ->
                    name.toLowerCase().endsWith(".xls") || name.toLowerCase().endsWith(".xlsx"));

            if (excelFiles == null || excelFiles.length == 0) {
                map.put("success", false);
                map.put("msg", "未找到导出的Excel文件");
                return map;
            }

            File excelFile = excelFiles[0]; // 取第一个Excel文件
            String fileName = excelFile.getName();
            String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

            // 3. 动态设置MIME类型
            String mimeType;
            if ("xls".equals(fileExtension)) {
                mimeType = "application/vnd.ms-excel";
            } else if ("xlsx".equals(fileExtension)) {
                mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            } else {
                map.put("success", false);
                map.put("msg", "不支持的文件格式: " + fileExtension);
                return map;
            }

            // 4. 创建FileItem（使用实际文件名和MIME类型）
            DiskFileItemFactory factory = new DiskFileItemFactory();
            FileItem fileItem = factory.createItem(
                    "file",
                    mimeType,
                    true,
                    fileName  // 使用实际文件名
            );

            try (InputStream input = new FileInputStream(excelFile);
                 OutputStream os = fileItem.getOutputStream()) {
                IOUtils.copy(input, os);
            }

            CommonsMultipartFile multipartFile = new CommonsMultipartFile(fileItem);

            // 5. 导入Excel
            String incrementalImport = "false";
            String paramIsRequired = "true";
            messages = dbUtilUpLoadExcel.taskUpload(
                    directory,
                    multipartFile,
                    fileName,  // 传递实际文件名
                    userInfo,
                    loginName,
                    incrementalImport,
                    paramIsRequired
            );

            // 5. 拓扑发布

            log.info("USER:" + userInnerCode + " batch import callback topopublish !");
            TopoImportService.getInstance().topopublish(userInnerCode);

            // 6. 处理结果
            if (messages.contains("成功")) {
                map.put("success", true);
                map.put("msg", "晋级成功");
            } else {
                map.put("success", false);
                map.put("msg", messages);
            }
        } catch (Exception e) {
            map.put("success", false);
            map.put("msg", "晋级失败");
            log.error("promotionBusinessSystem:" + e);
            // 返回错误响应
            rsStr = "{\"success\":false, \"msg\":\"上传失败\"}";
            response.setContentType("text/html;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(rsStr);
            return map;
        } finally {
            // 清理临时文件
            if (null!=directory){
                FileUtils.deleteDirectory(directory);
            }

        }

        return map;
    }


    private RestTemplate createRestTemplateWithTimeout(int connectTimeout, int readTimeout) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);

        RestTemplate restTemplate = new RestTemplate(factory);

        // 添加对表单数据的支持
        List<HttpMessageConverter<?>> converters = new ArrayList<>();
        converters.add(new FormHttpMessageConverter()); // 添加表单转换器
        converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8)); // 添加字符串转换器
        converters.add(new MappingJackson2HttpMessageConverter()); // 添加JSON转换器

        restTemplate.setMessageConverters(converters);

        return restTemplate;
    }

}