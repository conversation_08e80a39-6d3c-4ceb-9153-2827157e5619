package com.ideal.service.jobscheduling.actmonitor;

import com.ideal.ieai.server.jobscheduling.repository.actmonitor.ActBean;
import com.ideal.ieai.server.jobscheduling.repository.actmonitor.ActMonitorManager;
import com.ideal.ieai.server.jobscheduling.repository.actmonitor.FilterActBean;
import com.ideal.ieai.server.repository.RepositoryException;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 
 * <ul>
 * <li>Title: ActMonitorService.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2016年9月21日
 */
public class ActMonitorService
{
    private static  ActMonitorService intance    = new ActMonitorService();
    public static final String       THREADNUM   = "computer.start.threadnum";
    public static final String       UPDATESLEEP = "computer.start.progress.updatesleep";
    private static final Logger log = Logger.getLogger(ActMonitorService.class);
    public static  ActMonitorService getInstance ()
    {
        if (intance == null)
        {
            intance = new ActMonitorService();
        }
        return intance;
    }

    public Map<String, Object>  getPrjName ( ActBean actBean ) throws RepositoryException
    {
        return ActMonitorManager.getInstance().getPrjName(actBean);
    }

    /**
     *
     * @Title: getPrjname
     * @Description: 业务系统名称查询
     * @return
     * @throws Exception
     * @author: Administrator
     * @throws RepositoryException
     * @date:   2021年9月26日 下午7:41:58
     */
    @SuppressWarnings("rawtypes")
    public List<ActBean> getGroName () throws RepositoryException
    {

        return ActMonitorManager.getInstance().getGroName();
    }

    /**
     *
     * @Title: getPrjname
     * @Description: 业务系统名称查询
     * @return
     * @throws Exception
     * @author: Administrator
     * @throws RepositoryException
     * @date:   2021年9月26日 下午7:41:58
     */
    @SuppressWarnings("rawtypes")
    public List<String> getLastSysNameList (String gro) throws RepositoryException
    {
        String prjName  = "";
        List<String> list = new ArrayList<>();
        List<ActBean> lastSysName  =  new ArrayList<>();
        lastSysName = ActMonitorManager.getInstance().getLastSysName(gro);
        if (lastSysName.size()>0 &&lastSysName != null){
            List<String> system = lastSysName.stream().map(ActBean::getSystemname).collect(Collectors.toList());
            List<String> lastsys = lastSysName.stream().map(ActBean::getLastsysname).collect(Collectors.toList());
            list.addAll(system);
            list.addAll(lastsys);
            List<String> sysname = list.stream().distinct().collect(Collectors.toList());

            return sysname;
        }else {
            return list;
        }

    }

    public Map<String, Object> listActs ( Long userid, FilterActBean filter )
            throws RepositoryException
    {
        return ActMonitorManager.getInstance().listActMonitor(userid, filter, "", "", "", "");
    }

    public Map<String, Object> listActsByPrj(Long userid, FilterActBean filter, String prjName, String state)
            throws RepositoryException
    {
        return ActMonitorManager.getInstance().listActMonitor(userid, filter, prjName, state, "", "");
    }

    public Map<String, Object> listActsByPrjFlow(Long userid, FilterActBean filter, String prjName, String tagName, String flowName)
            throws RepositoryException {
        return ActMonitorManager.getInstance().listActMonitor(userid, filter, prjName, tagName, "", flowName);
    }

    /**
     * 新增查询Gro
     * @param userid
     * @param filter
     * @param prjName
     * @return
     * @throws RepositoryException
     */
    public Map<String, Object> listActsByGro ( Long userid, FilterActBean filter, String prjNames,String prjName,String sysName )
            throws RepositoryException
    {
        return ActMonitorManager.getInstance().listGroActMonitor(userid, filter,prjNames,prjName,sysName);
    }
    
    public Map<String, Object> listSDActs ( Long userid, FilterActBean filter ,String prjName,long flowId)
            throws RepositoryException
    {
        return ActMonitorManager.getInstance().listActSDMonitor(userid, filter, prjName,flowId);
    }

    public Map<String, Object> listActsBySys(Long userid, FilterActBean filter, String prjName, String newTextField, String sysName)
            throws RepositoryException
    {
        return ActMonitorManager.getInstance().listActMonitor(userid, filter, prjName, newTextField, sysName, "");
    }
    
    public List<Long> getCallFlowId(long mainFlowId) {
        List<Long> callFlowIds = new ArrayList<Long>();
        try
        {
            callFlowIds =  ActMonitorManager.getInstance().getCallFlowId(mainFlowId);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        return callFlowIds;
    }
    
    
    public Long getMainFlowId(long callFlowId){
        
        Long mainFlowId = 0L;
        
        try
        {
            mainFlowId= ActMonitorManager.getInstance().getUpFlowId(callFlowId);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        
        return mainFlowId;
    }
    
    
    public String getPrjNameByFlowId(long flowId){
        
        try
        {
            return ActMonitorManager.getInstance().getPrjNameByFlowId(flowId);
        } catch (RepositoryException e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        return "";
    }
    /**
     * 
     * <li>Description:锦州查询所属系统</li> 
     * <AUTHOR>
     * 2019年3月26日 
     * @return
     * return Map
     */
    public Map<String, Object>  querySystemCombox(ActBean actBean){
        return ActMonitorManager.getInstance().querySystemCombox(actBean);
    }

    public boolean getFilterActList(Long flowID) throws RepositoryException {
        return ActMonitorManager.getInstance().getFilterActList(flowID);
    }
}
