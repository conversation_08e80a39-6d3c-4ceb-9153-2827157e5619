package com.ideal.service.jobscheduling.taskCrossMainline;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.jobscheduling.repository.taskCrossMainline.*;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.DbUtilUpLoadExcel;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.topo.dataimport.ImportModel;
import com.ideal.ieai.server.repository.topo.dataimport.TopoImport;
import com.ideal.ieai.server.repository.topo.sequence.GantManager;
import com.ideal.ieai.server.repository.topo.sequence.GantModel;
import com.ideal.ieai.server.repository.topo.toposys.TopoSysManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

/**
 * 名称:TaskCrossMainlineService<br>
 * 描述:<br>
 * 类型:JAVA<br>
 * 最近修改时间: <br>
 *
 * <AUTHOR>
 */
public class TaskCrossMainlineService {
    private static final Logger logger = Logger.getLogger(TaskCrossMainlineService.class);

    private static final TaskCrossMainlineService self = new TaskCrossMainlineService();

    public static TaskCrossMainlineService getInstance() {
        return self;
    }

    public Map<String, Object> importDepend(File file, String username, String suffix) {
        Map<String, Object> map = new HashMap<>(2);
        List runningProjects = new ArrayList();
        List<TaskCrossMainlineBean> taskCrossMainlineBeanList = getImportData(map, file, suffix);
        //获取导入数据
        if (CollectionUtils.isEmpty(taskCrossMainlineBeanList)) {
            return map;
        }
        Map<String, List<TaskCrossMainlineBean>> isDelGroup = taskCrossMainlineBeanList.stream().collect(Collectors.groupingBy(TaskCrossMainlineBean::getIsDel));

        //获取本次上传的所有系统名称
        List allProjectList = taskCrossMainlineBeanList.stream().map(TaskCrossMainlineBean ->
                Stream.of(TaskCrossMainlineBean.getProjectName(),TaskCrossMainlineBean.getDependProjectName()).collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        try
        {
            runningProjects = TaskCrossMainlineManager.getInstance().getRunningProject(allProjectList);
        } catch (RepositoryException e)
        {
            logger.error("跨主线获取运行中工程报错！", e);
            map.put("msg", "跨主线获取运行中工程报错！");
            return map;
        }
        if(!runningProjects.isEmpty()){
            logger.info("上传失败！上传Excel中存在运行中的工程！运行中的工程为：" + StringUtils.join(runningProjects));
            map.put("msg", "上传失败！上传Excel中存在运行中的工程！运行中的工程为：" + StringUtils.join(runningProjects));
            return map;
        }
       /* rebuildTopology(isDelGroup,map);
        if (!CollectionUtils.isEmpty(map)) {
            return map;
        }*/
        if (!CollectionUtils.isEmpty(isDelGroup.get("是"))) {
            List<TaskCrossMainlineBean> deleteList = isDelGroup.get("是");
            crossTry("deletePreAndSuccForCrossMainline", (conn) -> {
                checkData(conn, map, deleteList);
                if (!CollectionUtils.isEmpty(map)) {
                    return;
                }
                deletePreAndSuccForCrossMainline(conn, deleteList,map);
            });
        }
        if (!CollectionUtils.isEmpty(map)) {
            return map;
        }
        if (!CollectionUtils.isEmpty(isDelGroup.get("否"))) {
            List<TaskCrossMainlineBean> saveList = isDelGroup.get("否");
            crossTry("importDepend", (conn) -> {
                //根据保存活动和现有已生效活动
                // 取差集获取真实需要保存的跨主线依赖活动
                List<TaskCrossMainlineBean> realSaveTaskCrossMainlineBeanList = getRealSaveTaskCrossMainlineBeanList(saveList, conn);
                if (CollectionUtils.isEmpty(realSaveTaskCrossMainlineBeanList)) {
                    map.put("msg", "当前导入数据的跨主线依赖已存在");
                    return;
                }
                //验证活动是否存在，依赖活动是否存在
                checkData(conn, map, realSaveTaskCrossMainlineBeanList);
                if (!CollectionUtils.isEmpty(map)) {
                    return;
                }
                //保存跨主线依赖关系
                List<TaskCrossMainlineBean> list = saveTaskCrossMainline(conn, realSaveTaskCrossMainlineBeanList, username);
                //新更，针对不写活动名情况
                List<TaskCrossMainlineBean> tempList = TaskCrossMainlineManager.getInstance().assemData(conn, realSaveTaskCrossMainlineBeanList);
                realSaveTaskCrossMainlineBeanList.addAll(tempList);
                //创建及保存前后继活动
                savePreAndSuccAct(conn, realSaveTaskCrossMainlineBeanList, map);
                //调用线程处理连线
                if (CollectionUtils.isEmpty(map)) {
                    logger.info("excel跨主线开始保存topo信息===解析跨系统依赖关系");
                    //跨主线依赖保存没问题后，保存topo信息并发布
                    List<ImportModel> servers = taskCrossMainlineBeanList.stream()
                            .filter(bean -> "否".equals(bean.getIsDel()))
                            .map(taskCrossMainlineBean -> {
                                ImportModel server = getMonitorActModel();
                                // 查询工程对应的组,以及子系统名称
                                getGroup(taskCrossMainlineBean,server);
                                return server;
                            })
                            .collect(Collectors.toList());
                    if(!servers.isEmpty()){
                        logger.info("excel跨主线开始保存topo信息===开始保存依赖关系");
                        TopoImport di1 = new TopoImport();
                        try {
                            allProjectList.stream().forEach(prjName -> {
                                try {
                                    delImportTopo((String) prjName);
                                } catch (RepositoryException e) {
                                    logger.error("delImportTopo() is error2 "+",prjName = " + prjName + e.getMessage(),e);
                                }
                            });
                            // di1.excelImportdb(servers, 16, 0);
                            //保存系统内上下游关系,获取时间
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            String idataDate =  sdf.format(new Date());
                            //删除原系统间间依赖关系
                            deleteUpDownInfo(idataDate);
                            //调用发布方法,自动将保存的依赖关系发布
                            topoPublish(idataDate);
                        } catch (Exception e) {
                            logger.error("ProjectAutoAnalyzeActMonitor analyzeMonitor error : "+e.getMessage(),e);
                        }
                    }
//                    TopoSysThread tsThread = new TopoSysThread(list);
//                    tsThread.start();
                }

            });
            if (!CollectionUtils.isEmpty(map)) {
                return map;
            }
        }
        return null;
    }

    public void rebuildTopology(Map<String, List<TaskCrossMainlineBean>> isDelGroup, Map<String, Object> map) {
        List<TaskCrossMainlineBean> deleteCrossMainlineBeans = isDelGroup.get("是");
        List<TaskCrossMainlineBean> addCrossMainlineBeans = isDelGroup.get("否");
        // 避免 NPE
        if (deleteCrossMainlineBeans == null) deleteCrossMainlineBeans = Collections.emptyList();
        if (addCrossMainlineBeans == null) addCrossMainlineBeans = Collections.emptyList();
        try {
            for (TaskCrossMainlineBean mainlineBean : deleteCrossMainlineBeans) {
                List<ExcelModelBean> actExcelModels = TaskCrossMainlineManager.getInstance().getActExcelModel(DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI), mainlineBean.getMainLineName(), mainlineBean.getProjectName(),mainlineBean.getActName());
                ExcelModelBean actExcelModel = actExcelModels.get(0);
                ImportModel importModel = DbUtilUpLoadExcel.getInstance().queryExistTopoInfo(actExcelModel.getiChildProjectName(), actExcelModel.getiActName());
                 if (!Objects.isNull(importModel)) {
                     // DbUtilUpLoadExcel.getInstance().deleteTopoInfo(actExcelModel.getiChildProjectName(), actExcelModel.getiActName());
                     importModel.setLastsysname("");
                     importModel.setLasttaskname("");
                     DbUtilUpLoadExcel.getInstance().syncTopoInfo(importModel);
                 }
            }
            for (TaskCrossMainlineBean mainlineBean : addCrossMainlineBeans) {
                List<ExcelModelBean> actExcelModels = TaskCrossMainlineManager.getInstance().getActExcelModel(DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI), mainlineBean.getMainLineName(), mainlineBean.getProjectName(),mainlineBean.getActName());
                ExcelModelBean actExcelModel = actExcelModels.get(0);
                List<ExcelModelBean> dependActExcelModels = TaskCrossMainlineManager.getInstance().getActExcelModel(DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI), mainlineBean.getDependMainLineName(), mainlineBean.getDependProjectName(),mainlineBean.getDependActName());
                ExcelModelBean dependActExcelModel = dependActExcelModels.get(0);
                ImportModel importModel = DbUtilUpLoadExcel.getInstance().queryExistTopoInfo(actExcelModel.getiChildProjectName(), actExcelModel.getiActName());
                if (Objects.isNull(importModel)) {
                    importModel = createImportModel(actExcelModel, dependActExcelModel);
                    DbUtilUpLoadExcel.getInstance().syncTopoInfo(importModel);
                    continue;
                }
                if(StringUtils.isBlank(importModel.getLastsysname()) && StringUtils.isBlank(importModel.getLasttaskname())){
                    importModel.setLastsysname(dependActExcelModel.getiChildProjectName());
                    importModel.setLasttaskname(dependActExcelModel.getiActName());
                    DbUtilUpLoadExcel.getInstance().syncTopoInfo(importModel);
                }else{
                    importModel =  createImportModel(actExcelModel, dependActExcelModel);
                    DbUtilUpLoadExcel.getInstance().syncTopoInfo(importModel);
                }
            }
        } catch (Exception e) {
            logger.error("rebuildTopology error : " + e.getMessage(), e);
            map.put("msg", "跨主线同步拓扑导入上游信息失败！");
        }

    }
    private ImportModel createImportModel(ExcelModelBean actExcelModel, ExcelModelBean dependActExcelModel) {
        ImportModel model = new ImportModel();
        model.setSystemname(actExcelModel.getiChildProjectName());
        model.setTaskname(actExcelModel.getiActName());
        model.setGro("默认组");
        model.setLastsysname(dependActExcelModel.getiChildProjectName());
        model.setLasttaskname(dependActExcelModel.getiActName());
        return model;
    }

    public void delImportTopo(String prjName) throws RepositoryException {
        if(StringUtils.isBlank(prjName)) {
            logger.info("delImportTopo prjName is null"+prjName);
        }else {
            Connection conn = null;
            PreparedStatement ps = null;
            String sql = "delete from ieai_topoinfo_pre  where systemname = ('" + prjName + "')";
            try {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                ps = conn.prepareStatement(sql);
                ps.executeUpdate();
                conn.commit();
            } catch (Exception e) {
                logger.error("delImportTopo() is error2" + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY, e);
            } finally {
                DBResource.closePSConn(conn, ps, "delImportTopo", logger);
            }
        }
    }

    private void deleteUpDownInfo(String queryDate) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            String deleteUpSql = " delete from IEAI_ACT_TOPO_INS_UPDOWN where IACTMESSID in (SELECT DISTINCT AA.IID FROM IEAI_TOPOINFO A,IEAI_ACT_TOPO_INSTANCE AA \n" +
                    "                    WHERE A.SYSTEMNAME=AA.IPROJECTNAME AND A.TASKNAME=AA.IACTNAME AND A.LASTSYSNAME IS NOT NULL AND AA.IDATADATE= ?) and IUPDOWNTYPE = 0";
            String deleteDownSql = "\tdelete from IEAI_ACT_TOPO_INS_UPDOWN where IACTMESSID in ( SELECT DISTINCT AA.IID FROM IEAI_TOPOINFO A,IEAI_ACT_TOPO_INSTANCE AA \n" +
                    "                    WHERE A.LASTSYSNAME=AA.IPROJECTNAME AND A.LASTTASKNAME=AA.IACTNAME AND A.SYSTEMNAME IS NOT NULL AND AA.IDATADATE= ?) and IUPDOWNTYPE = 1";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = conn.prepareStatement(deleteUpSql);
            ps.setString(1,queryDate);
            ps.execute();

            ps = conn.prepareStatement(deleteDownSql);
            ps.setString(1,queryDate);
            ps.execute();

            conn.commit();
        }catch (Exception e)
        {
            logger.error(method + " : " + e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE, e);
        } finally
        {
            DBResource.closePSConn(conn, ps, "saveUpInfo", logger);
        }
    }

    private void topoPublish(String idataDate) throws RepositoryException {
        Map map = new HashMap();
        map.put("success", false);
        map.put("message", "发布失败");
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        ResultSet rs = null;
        ResultSet rs2 = null;
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            //ieai_topoinfo_pre
            String sql0 = "select systemname,taskname,taskdes,lastsysname,lasttaskname,lasttaskdes,opentime,arrivetime,information,remark,gro,gjtask,idate,iopendatetype,iarrivedatetype,ishowtype from ieai_topoinfo_pre";
            String sql1 = "delete from ieai_topoinfo";
            String sql4 = "insert into ieai_topoinfo_version_his(iid,systemname,taskname,taskdes,lastsysname,lasttaskname,lasttaskdes,opentime,arrivetime,information,remark,gro," +
                    "gjtask,idate,iopendatetype,iarrivedatetype,ISHOWTYPE,ITOPOVERSIONID) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            String sql5 = "insert into ieai_topoinfo(iid,systemname,taskname,taskdes,lastsysname,lasttaskname,lasttaskdes,opentime,arrivetime,information,remark,gro," +
                    "gjtask,idate,iopendatetype,iarrivedatetype,ishowtype,iversionname) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            String sql2 = "select max(iorder) from ieai_topoinfo_version";
            ps1 = conn.prepareStatement(sql1);
            ps1.executeUpdate();
            ps2 = conn.prepareStatement(sql2);
            rs2 = ps2.executeQuery();
            long iorder = 1;
            if (rs2.next()) {
                iorder = rs2.getLong(1) + 1;
            }
            String ordername = "V"+iorder;
            long vid = IdGenerator.createIdFor("ieai_topoinfo_version", conn);
            String sql3 = "insert into ieai_topoinfo_version(iid,itopoversion,iorder,ipublisher,icreatetime)" +
                    " values('"+vid+"','"+ordername+"','"+iorder+"','"+4+"',FUN_GET_DATE_NUMBER_NEW(" + Constants.getCurrentSysDate() + ",8))";
            ps3 = conn.prepareStatement(sql3);
            ps3.executeUpdate();
            ps = conn.prepareStatement(sql0);
            rs = ps.executeQuery();
            ps5 = conn.prepareStatement(sql5);
            ps4 = conn.prepareStatement(sql4);
            Map<String,String> cellmap = new HashMap();
            int incount = 0;
            while (rs.next()) {
                long topoid = IdGenerator.createIdFor("ieai_topoinfo", conn);
                int index1 = 0;
                incount++;
                String group = rs.getString("gro");
                ps5.setLong(++index1, topoid);
                ps5.setString(++index1,rs.getString("systemname"));
                ps5.setString(++index1,rs.getString("taskname"));
                ps5.setString(++index1,rs.getString("taskdes"));
                ps5.setString(++index1,rs.getString("lastsysname"));
                ps5.setString(++index1,rs.getString("lasttaskname"));
                ps5.setString(++index1,rs.getString("lasttaskdes"));
                ps5.setString(++index1,rs.getString("opentime"));
                ps5.setString(++index1,rs.getString("arrivetime"));
                ps5.setString(++index1,rs.getString("information"));
                ps5.setString(++index1,rs.getString("remark"));
                ps5.setString(++index1,group);
                ps5.setInt(++index1,rs.getInt("gjtask"));
                ps5.setString(++index1,rs.getString("idate"));
                ps5.setString(++index1,rs.getString("iopendatetype"));
                ps5.setString(++index1,rs.getString("iarrivedatetype"));
                ps5.setInt(++index1,rs.getInt("ishowtype"));
                ps5.setString(++index1, ordername);
                ps5.addBatch();
                index1 = 0;
                long vhisid = IdGenerator.createIdFor("ieai_topoinfo_version_his", conn);
                ps4.setLong(++index1, vhisid);
                ps4.setString(++index1,rs.getString("systemname"));
                ps4.setString(++index1,rs.getString("taskname"));
                ps4.setString(++index1,rs.getString("taskdes"));
                ps4.setString(++index1,rs.getString("lastsysname"));
                ps4.setString(++index1,rs.getString("lasttaskname"));
                ps4.setString(++index1,rs.getString("lasttaskdes"));
                ps4.setString(++index1,rs.getString("opentime"));
                ps4.setString(++index1,rs.getString("arrivetime"));
                ps4.setString(++index1,rs.getString("information"));
                ps4.setString(++index1,rs.getString("remark"));
                ps4.setString(++index1,group);
                ps4.setInt(++index1,rs.getInt("gjtask"));
                ps4.setString(++index1,rs.getString("idate"));
                ps4.setString(++index1,rs.getString("iopendatetype"));
                ps4.setString(++index1,rs.getString("iarrivedatetype"));
                ps4.setInt(++index1,rs.getInt("ishowtype"));
                ps4.setLong(++index1,vid);
                ps4.addBatch();
                if(org.apache.commons.lang.StringUtils.isNotBlank(group)&&!cellmap.containsKey(group)) {
                    cellmap.put(group, group);
                }
            }
            if(0 != incount) {
                ps4.executeBatch();
                ps5.executeBatch();
            }
            ps4.executeBatch();
            ps5.executeBatch();
            //主动触发保存instance
            updateGant(conn,idataDate,0L);
            //TODO 主动触发方式有待商榷，目前有太多的逻辑都在轮询线程中，无法确认影响有多大
            conn.commit();
            logger.info("excel跨主线依赖======自动解析发布成功======");
        } catch (Exception e) {
            logger.error("topopublish() is error2",e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY, e);
        } finally {
            DBResource.closePreparedStatement(ps5, "topopublish", logger);
            DBResource.closePreparedStatement(ps4, "topopublish", logger);
            DBResource.closePreparedStatement(ps3, "topopublish", logger);
            DBResource.closePSRS(rs2, ps2, "topopublish", logger);
            DBResource.closePSRS(rs, ps, "topopublish", logger);
            DBResource.closePSConn(conn, ps1, "topopublish", logger);
        }
    }

    public void updateGant(Connection conn,String queryDate,Long flag){
        logger.info("queryDate: "+queryDate +" flag: "+flag);
        if(flag==0){
            //存IEAI_ACT_TOPO_INSTANCE表的IID（相当于查询存储过程的临时表IID）
            List idList=new ArrayList();
            //获取解析数据
            List<GantModel> list = GantManager.getInstance().getActRelationMessList(conn,queryDate,idList);
            //保存IEAI_ACT_TOPO_INSTANCE表、 IEAI_EXEACT_TOPO_INSTANCE表
            GantManager.getInstance().saveActTopoInstance(conn,list);
            //更新活动跨天规则
            GantManager.getInstance().updateCrossSkyRule(conn,queryDate,idList);
            //活动平均耗时
            GantManager.getInstance().updateAverageTimeConsuming(conn,queryDate,idList);
            GantManager.getInstance().updateAvgtime(conn,queryDate,idList);
            //删除关联信息
            GantManager.getInstance().delteACTTopoInsUpdown(conn,queryDate);
            //保存上关联信息
            GantManager.getInstance().saveUpInfo(conn,queryDate);
            //保存下关联信息
            GantManager.getInstance().saveDownInfo(conn,queryDate);
        }
        //更新上下关联状态
        GantManager.getInstance().updateUpAndDownInfo(conn,queryDate);
        //警报清除
        GantManager.getInstance().updateAlarmClear(conn,queryDate);
        logger.info("============================updateGant...end===============================");
    }
    public ImportModel getMonitorActModel(){
        ImportModel server = new ImportModel();
        server.setDelete("否");
        server.setGroup("默认组");
        server.setGjtask("");
        server.setTaskdes("");
        server.setLastsysname("");
        server.setLasttaskname("");
        server.setLasttaskdes("");
        server.setOpentime("");
        server.setArrivetime("");
        server.setInformation("");
        server.setRemark("");
        server.setOpendatetype("");
        server.setArrivedatetype("");
        server.setRelationFile("");
        server.setFileResourse("");
        return server;
    }

    private void getGroup(TaskCrossMainlineBean bean,ImportModel server) {

        String sql = "select iname,DEPARTMENT from ieai_project where protype = 1 and iname = ? order by iid desc";

        String sqlExcel = "select distinct ichildproname from ieai_excelmodel where imainproname = ? and imainlinename = ? and IACTNAME = ?";

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String group = "";

        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            //查询系统的子系统名称
            ps = conn.prepareStatement(sqlExcel);
            ps.setString(1,bean.getProjectName());
            ps.setString(2,bean.getMainLineName());
            ps.setString(3,bean.getActName());
            rs = ps.executeQuery();
            while (rs.next()) {
                String  childProname =  rs.getString("ichildproname");
                server.setSystemname(childProname);
                server.setTaskname(bean.getActName());
            }
            rs.close();
            ps.close();
            //查询被依赖的子系统名称
            ps = conn.prepareStatement(sqlExcel);
            ps.setString(1,bean.getDependProjectName());
            ps.setString(2,bean.getDependMainLineName());
            ps.setString(3,bean.getDependActName());
            rs = ps.executeQuery();
            while (rs.next()) {
                String  depChildProname =  rs.getString("ichildproname");
                server.setLastsysname(depChildProname);
                server.setLasttaskname(bean.getDependActName());
            }
            rs.close();
            ps.close();

            ps = conn.prepareStatement(sql);
            ps.setString(1 ,server.getSystemname());
            rs = ps.executeQuery();
            while (rs.next()) {
                group =  rs.getString("DEPARTMENT");
                if(StringUtils.isNotBlank(group)){
                    server.setGroup(group);
                }
            }
        } catch (DBException | SQLException e) {
            logger.error("getGroup is error2"+e.getMessage(),e);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getGroup", logger);
        }
    }

    /**
     * 通过与当前数据库存在的跨主线依赖表差集获取真实存储的数据列表
     *
     * @param saveList 导入的删除状态为否的跨主线关联数据列表
     * @param conn     数据库链接
     * @return 真正需要存储的跨主线依赖数据
     */
    private List<TaskCrossMainlineBean> getRealSaveTaskCrossMainlineBeanList(List<TaskCrossMainlineBean> saveList, Connection conn) {
        List<TaskCrossMainlineBean> allCrossMainlineList = TaskCrossMainlineManager.getInstance().getAllCrossMainlineList(conn);
        List<TaskCrossMainlineBean> all = new ArrayList<>();
        all.addAll(allCrossMainlineList);
        all.addAll(saveList);
        return saveList.stream().filter(item -> !contains(allCrossMainlineList, item))
                .filter(item -> !dependExit(all, item)).peek(TaskCrossMainlineBean::parse).collect(Collectors.toList());
    }

    private boolean contains(List<TaskCrossMainlineBean> allCrossMainlineList, TaskCrossMainlineBean taskCrossMainlineBean) {
        if (CollectionUtils.isEmpty(allCrossMainlineList)) {
            return false;
        }
        return allCrossMainlineList.stream().anyMatch(item ->
                item.getProjectName().equals(taskCrossMainlineBean.getProjectName()) &&
                        item.getMainLineName().equals(taskCrossMainlineBean.getMainLineName()) &&
                        item.getActName().equals(taskCrossMainlineBean.getActName()) &&
                        item.getDependProjectName().equals(taskCrossMainlineBean.getDependProjectName()) &&
                        item.getDependMainLineName().equals(taskCrossMainlineBean.getDependMainLineName()) &&
                        item.getDependActName().equals(taskCrossMainlineBean.getDependActName())
        );
    }

    private boolean dependExit ( List<TaskCrossMainlineBean> allCrossMainlineList,
                                 TaskCrossMainlineBean taskCrossMainlineBean )
    {
        if (CollectionUtils.isEmpty(allCrossMainlineList))
        {
            return false;
        }
        return allCrossMainlineList.stream()
                .anyMatch(item -> item.getProjectName().equals(taskCrossMainlineBean.getDependProjectName())
                        && item.getMainLineName().equals(taskCrossMainlineBean.getDependMainLineName())
                        && item.getActName().equals(taskCrossMainlineBean.getDependActName())
                        && item.getDependProjectName().equals(taskCrossMainlineBean.getProjectName())
                        && item.getDependMainLineName().equals(taskCrossMainlineBean.getMainLineName())
                        && item.getDependActName().equals(taskCrossMainlineBean.getActName()));
    }

    private void savePreAndSuccAct(Connection conn, List<TaskCrossMainlineBean> taskCrossMainlineBeanList, Map<String, Object> map) {
        Map<String, List<ExcelModelBean>> preAndSuccMap = taskCrossMainlineBeanList.stream()
                .peek(item -> makePreAndSuccData(conn, item))
                .reduce(new HashMap<>(2), (memo, item) -> {
                    if (CollectionUtils.isEmpty(memo.get("pre"))) {
                        List<ExcelModelBean> preList = new ArrayList<>();
                        preList.add(item.getPreExcelModelBean());
                        memo.put("pre", preList);
                    } else {
                        memo.get("pre").add(item.getPreExcelModelBean());
                    }
                    if (CollectionUtils.isEmpty(memo.get("succ"))) {
                        List<ExcelModelBean> preList = new ArrayList<>();
                        preList.add(item.getSussExcelModelBean());
                        memo.put("succ", preList);
                    } else {
                        memo.get("succ").add(item.getSussExcelModelBean());
                    }
                    return memo;
                }, (map1, map2) -> {
                    map1.putAll(map2);
                    return map1;
                });

        if (CollectionUtils.isEmpty(preAndSuccMap.get("pre"))) {
            map.put("msg", "前继关系存储异常");
            try {
                conn.rollback();
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
            }
            return;
        }
        if (CollectionUtils.isEmpty(preAndSuccMap.get("succ"))) {
            map.put("msg", "后继关系存储异常");
            try {
                conn.rollback();
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
            }
            return;
        }



        TaskCrossMainlineManager.getInstance().saveActPre(conn, preAndSuccMap.get("pre"));
        TaskCrossMainlineManager.getInstance().saveActSucc(conn, preAndSuccMap.get("succ"));
    }

    private List<TaskCrossMainlineBean> saveTaskCrossMainline(Connection conn, List<TaskCrossMainlineBean> taskCrossMainlineBeanList, String username) {
        return TaskCrossMainlineManager.getInstance().save(conn, taskCrossMainlineBeanList, username);
    }

    public Map<String, Object> getTaskCrossMainlineInfo(
            TaskCrossMainlineBean taskCrossMainlineBean, int limit, int start) {
        AtomicReference<Map<String, Object>> map = new AtomicReference<>();
        crossTry("getTaskCrossMainlineInfo", (conn) ->
                map.set(TaskCrossMainlineManager.getInstance().getTaskCrossMainlineInfo(conn, taskCrossMainlineBean, limit,
                        start)));
        return map.get();
    }

    public Map<String, Object> getAllCrossMainlineListForExport ( TaskCrossMainlineBean taskCrossMainlineBean,
                                                                  int limit, int start )
    {
        AtomicReference<Map<String, Object>> map = new AtomicReference<>();
        crossTry("getTaskCrossMainlineInfo", ( conn ) -> map.set(TaskCrossMainlineManager.getInstance()
                .getTaskCrossMainlineInfo(conn, taskCrossMainlineBean, limit, start)));
        return map.get();
    }

    public Map deleteTaskCrossMainlineInfo(String ids) {
        List<TaskCrossMainlineBean> tmList = new ArrayList();
        Connection con = null;
        Map map = new HashMap();
        map.put("success", true);
        map.put("message", "删除成功");
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            tmList = TaskCrossMainlineManager.getInstance().selectTaskCrossMainlineInfoByIds(con, ids);
        } catch (DBException e1)
        {
            logger.error(e1);
        } finally
        {
            DBResource.closeConnection(con, "deleteTaskCrossMainlineInfo", logger);
        }
        AtomicReference<Boolean> flag = new AtomicReference<>(false);
        crossTry("deleteTaskCrossMainlineInfo", (connection) -> {
            List runningProjects = new ArrayList();
            List<TaskCrossMainlineBean> taskCrossMainlineBeanList = TaskCrossMainlineManager.getInstance().selectTaskCrossMainlineInfoByIds(connection,
                    ids);
            //获取本次上传的所有系统名称
            List allProjectList = taskCrossMainlineBeanList.stream().map(TaskCrossMainlineBean ->
                    Stream.of(TaskCrossMainlineBean.getProjectName(),TaskCrossMainlineBean.getDependProjectName()).collect(Collectors.toList()))
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
            try
            {
                runningProjects = TaskCrossMainlineManager.getInstance().getRunningProject(allProjectList);
            } catch (RepositoryException e)
            {
                map.put("success", false);
                map.put("message", "跨主线获取运行中工程报错！");
                logger.error("跨主线获取运行中工程报错！", e);
            }
            if(!runningProjects.isEmpty()){
                logger.info("删除失败！删除跨主线依赖中存在运行中的工程！运行中的工程为：" + StringUtils.join(runningProjects));
                map.put("success", false);
                map.put("message", "删除失败！删除跨主线依赖中中存在运行中的工程！运行中的工程为：" + StringUtils.join(runningProjects));
                return;
            }

            boolean flag1 = deletePreAndSuccForCrossMainline(connection, taskCrossMainlineBeanList,map);
            boolean flag2 = TaskCrossMainlineManager.getInstance().delateTaskCrossMainlineInfoByIds(connection, ids);
            flag.set(flag1 && flag2);
            if(!flag.get()){
                map.put("success", false);
                map.put("message", "删除失败");
            }
        });
        //删除成功后更新topoinfo
        if(flag.get()) {
            TopoSysManager.getInstance().updateTopoinfo(tmList);
        }
        return map;
    }

    private boolean deletePreAndSuccForCrossMainline(Connection connection, List<TaskCrossMainlineBean> taskCrossMainlineBeanList,Map map) {
        boolean flag1 = true;
        boolean flag = true;
        boolean flag2 = true;
        for (TaskCrossMainlineBean taskCrossMainlineBean : taskCrossMainlineBeanList) {
            ExcelModelBean preExcelModelBean = new ExcelModelBean();
            if (null==taskCrossMainlineBean.getDependActName() || taskCrossMainlineBean.getDependActName().equals("")) {
                preExcelModelBean.setiActName(TaskCrossMainlineManager.getInstance().assemActName(connection, taskCrossMainlineBean));
            }else {
                preExcelModelBean.setiActName(taskCrossMainlineBean.getDependActName());
            }
            preExcelModelBean.setiMainlineName(taskCrossMainlineBean.getDependMainLineName());
            preExcelModelBean.setiMainProName(taskCrossMainlineBean.getDependProjectName());
            taskCrossMainlineBean.setPreExcelModelBean(preExcelModelBean);
            ExcelModelBean succExcelModelBean = new ExcelModelBean();
            succExcelModelBean.setiActName(taskCrossMainlineBean.getActName());
            succExcelModelBean.setiMainlineName(taskCrossMainlineBean.getMainLineName());
            succExcelModelBean.setiMainProName(taskCrossMainlineBean.getProjectName());
            taskCrossMainlineBean.setSussExcelModelBean(succExcelModelBean);
            makePreAndSuccData(connection, taskCrossMainlineBean);
            if (taskCrossMainlineBean.getPreExcelModelBean().getiOperationId() != null
                    && taskCrossMainlineBean.getPreExcelModelBean().getiOperationId() != 0
                    && taskCrossMainlineBean.getPreExcelModelBean().getiSelfOperationId() != null
                    && taskCrossMainlineBean.getPreExcelModelBean().getiSelfOperationId() != 0
                    && taskCrossMainlineBean.getSussExcelModelBean().getiOperationId() != null
                    && taskCrossMainlineBean.getSussExcelModelBean().getiOperationId() != 0
                    && taskCrossMainlineBean.getSussExcelModelBean().getiSelfOperationId() != null
                    && taskCrossMainlineBean.getSussExcelModelBean().getiSelfOperationId() != 0)
            {
                // todo 分别删除前继和后继任务数据
                flag1 = TaskCrossMainlineManager.getInstance().deletePreAndSuccForCrossMainline(connection,
                        taskCrossMainlineBean.getSussExcelModelBean(), taskCrossMainlineBean.getPreExcelModelBean())
                        && flag1;
                flag2 = TaskCrossMainlineManager.getInstance().delateTaskCrossMainlineInfo(connection,
                        taskCrossMainlineBean,map) && flag2;
            } else
            {
                flag = false;
            }


        }

        if (flag1 && flag2)
        {
            flag = true;
        }else {
            flag = false;
        }
        return flag;
    }

    /**
     * 根据导入数据构建的前后继数据补充前后继数据的id
     *
     * @param connection            数据库连接
     * @param taskCrossMainlineBean 导入数据实体
     */
    private void makePreAndSuccData(Connection connection, TaskCrossMainlineBean taskCrossMainlineBean) {
        TaskCrossMainlineManager.getInstance().getActExcelModel(connection,
                taskCrossMainlineBean.getPreExcelModelBean());

        TaskCrossMainlineManager.getInstance().getActExcelModel(connection,
                taskCrossMainlineBean.getSussExcelModelBean());

        taskCrossMainlineBean.getSussExcelModelBean()
                .setiOperationId(taskCrossMainlineBean.getPreExcelModelBean().getiSelfOperationId());
        taskCrossMainlineBean.getPreExcelModelBean()
                .setiOperationId(taskCrossMainlineBean.getSussExcelModelBean().getiSelfOperationId());
    }

    private void checkData(Connection conn, Map<String, Object> map, List<TaskCrossMainlineBean> taskCrossMainlineBeanList) {
        Map<String, List<TaskCrossMainlineBean>> res = TaskCrossMainlineManager.getInstance().checkTaskExist(conn, taskCrossMainlineBeanList);
        if (!CollectionUtils.isEmpty(res)) {
            map.put("msg", "导入验证未通过");
            map.put("data", res);
        }
    }

    private List<TaskCrossMainlineBean> getImportData(Map<String, Object> map, File file, String suffix) {

        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            logger.error("根据文件生成文件流失败");
        }
        ExcelUtil<TaskCrossMainlineBean> excelUtil = ExcelUtil.init(ExcelTypeEnum.get(suffix), fileInputStream, TaskCrossMainlineBean.class);
        excelUtil.setHeader("工程名称", "主线名称", "作业名称", "依赖工程名称", "依赖主线名称", "依赖作业名称", "是否删除");
        if (!excelUtil.checkHeader(0)) {
            map.put("msg", "导入配置表与标准模板表头不同，请核验");
            return null;
        }
        List<TaskCrossMainlineBean> taskCrossMainlineBeanList;
        try {
            taskCrossMainlineBeanList = excelUtil.getData(1,map);
        } catch (NoSuchMethodException | InvocationTargetException | InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
            logger.error("导入跨主线依赖异常", e);
            map.put("msg", "导入失败，请联系管理员");
            return null;
        }
        return taskCrossMainlineBeanList;
    }

    private void crossTry(String method, Consumer<Connection> func) {
        int dbType = Constants.IEAI_IEAI_BASIC;
        Connection conn = null;
        try {
            conn = DBResource.getConnection(method, logger, dbType);
            func.accept(conn);
            conn.commit();
        } catch (Exception e) {
            e.printStackTrace();
            try {
                e.printStackTrace();
                DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, Thread.currentThread().getStackTrace()[1].getMethodName(), logger);
            } catch (RepositoryException repositoryException) {
                repositoryException.printStackTrace();
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + "回滚异常");
            }
            logger.error(e.getMessage());
        } finally {
            DBResource.closeConnection(conn, method, logger);
        }
    }

    public void doExportExcel ( OutputStream out, Map systemActInfo )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        HSSFWorkbook hssfworkbook = new HSSFWorkbook();
        // 获取所有记录
        try
        {
            // 将同一系统的数据放到同一个sheet页中

            // 说明样式
            HSSFFont headFont = hssfworkbook.createFont();
            headFont.setFontHeightInPoints((short) 11);
            headFont.setColor(IndexedColors.BLACK.getIndex());
            HSSFCellStyle headStyle = hssfworkbook.createCellStyle();
            headStyle.setFont(headFont);
            headStyle.setFillForegroundColor(IndexedColors.GOLD.getIndex());
            headStyle.setFillPattern(SOLID_FOREGROUND);
            headStyle.setWrapText(true);

            // 标题栏样式
            HSSFFont titleFont = hssfworkbook.createFont();
            titleFont.setFontHeightInPoints((short) 10);
            titleFont.setBold(true);
            HSSFCellStyle titleStyle = hssfworkbook.createCellStyle();
            titleStyle.setFont(titleFont);
            titleStyle.setFillForegroundColor(IndexedColors.GOLD.getIndex());
            titleStyle.setFillPattern(SOLID_FOREGROUND);
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBottomBorderColor(IndexedColors.GOLD.getIndex());
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setLeftBorderColor(IndexedColors.GOLD.getIndex());
            titleStyle.setBorderRight(BorderStyle.THIN);
            titleStyle.setRightBorderColor(IndexedColors.GOLD.getIndex());
            titleStyle.setBorderTop(BorderStyle.THIN);
            titleStyle.setTopBorderColor(IndexedColors.GOLD.getIndex());

            // 数据样式
            HSSFFont dataFont = hssfworkbook.createFont();
            dataFont.setFontHeightInPoints((short) 10);
            HSSFCellStyle dataStyle = hssfworkbook.createCellStyle();
            dataStyle.setFont(dataFont);

            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBottomBorderColor(IndexedColors.GOLD.getIndex());
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setLeftBorderColor(IndexedColors.GOLD.getIndex());
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setRightBorderColor(IndexedColors.GOLD.getIndex());
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setTopBorderColor(IndexedColors.GOLD.getIndex());

            List homeBeanActInfo = (List) systemActInfo.get("dataList");

            // 创建sheet页
            HSSFSheet disBackupActInfoSheet = hssfworkbook.createSheet("跨主线依赖");
            // 标题栏
            HSSFRow disBackupActInfoRowTitle = disBackupActInfoSheet.createRow(0);
            disBackupActInfoRowTitle.setHeightInPoints(24);
            for (int j = 0; j < infoTitle.length; j++)
            {
                HSSFCell titleCell = disBackupActInfoRowTitle.createCell(j);
                titleCell.setCellValue(infoTitle[j]);
                titleCell.setCellStyle(titleStyle);
            }
            // 数据栏
            for (int k = 0; k < homeBeanActInfo.size(); k++)
            {
                if (k > 20000)
                {
                    break;
                }
                {
                    Map taskCrossMainlineBean = (Map) homeBeanActInfo.get(k);
                    HSSFRow dataRow = disBackupActInfoSheet.createRow(k + 1);
                    dataRow.setHeightInPoints(27);


                    HSSFCell actNameCell = dataRow.createCell(0);
                    actNameCell.setCellValue(taskCrossMainlineBean.get("projectName").toString());
                    actNameCell.setCellStyle(dataStyle);

                    HSSFCell MAINLINENAMECell = dataRow.createCell(1);
                    MAINLINENAMECell.setCellValue(taskCrossMainlineBean.get("mainLineName").toString());
                    MAINLINENAMECell.setCellStyle(dataStyle);

                    HSSFCell flowIdCell = dataRow.createCell(2);
                    flowIdCell.setCellValue(taskCrossMainlineBean.get("actName").toString());
                    flowIdCell.setCellStyle(dataStyle);
                    HSSFCell taskNameCell = dataRow.createCell(3);
                    taskNameCell.setCellValue(taskCrossMainlineBean.get("dependProjectName").toString());
                    taskNameCell.setCellStyle(dataStyle);

                    HSSFCell taskDescCell = dataRow.createCell(4);
                    taskDescCell.setCellValue(taskCrossMainlineBean.get("dependMainLineName").toString());
                    taskDescCell.setCellStyle(dataStyle);

                    HSSFCell errorTypeCell = dataRow.createCell(5);
                    errorTypeCell.setCellValue(taskCrossMainlineBean.get("dependActName").toString());
                    errorTypeCell.setCellStyle(dataStyle);


                    HSSFCell deleteFlag= dataRow.createCell(6);
                    deleteFlag.setCellValue("否");
                    deleteFlag.setCellStyle(dataStyle);


                }
            }
            hssfworkbook.write(out);
            out.flush();
        } catch (IOException e)
        {
            logger.error(method, e);
        } finally
        {
            try
            {
                out.close();
            } catch (IOException e)
            {
                logger.error(method, e);
            }
        }
    }

    private static final String[] infoTitle = new String[] { "工程名称", "主线名称", "作业名称", "依赖工程名称", "依赖主线名称", "依赖作业名称", "是否删除" };
}
