<mxEditor layoutDiagram="1" layoutSwimlanes="1" maintainSwimlanes="1">
	<include name="config/wfeditor-commons.xml"/>
	<ui>
		<add as="graph" element="graph"
			style="left:70px;right:20px;top:20px;bottom:40px"/>
		<add as="status" element="status"
			style="height:20px;bottom:20px;left:20px;right:20px"/>
		<add as="toolbar" x="10" y="20" width="54"/>
	</ui>
	<Array as="templates">
		<add as="swimlane">
			<Swimlane label="Swimlane" customAttribute="text value">
				<mxCell vertex="1" style="swimlane;horizontal=1" connectable="0">
					<mxGeometry as="geometry" width="190" height="400"/>
				</mxCell>
			</Swimlane>
		</add>
	</Array>
	<mxGraph as="graph" swimlaneNesting="0">
		<include name="config/wfgraph-commons.xml"/>
		<mxStylesheet as="stylesheet">
			<add as="defaultEdge">
				<add as="shape" value="connector"/>
				<add as="elbow" value="vertical"/>
				<add as="fontSize" value="10"/>
				<add as="strokeColor" value="black"/>
				<add as="rounded" value="1"/>
				<add as="edgeStyle" value="elbowEdgeStyle"/>
				<add as="endArrow" value="classic"/>
			</add>
		</mxStylesheet>
	</mxGraph>
	<mxDefaultToolbar as="toolbar">
		<include name="config/wftoolbar-commons.xml"/>
	</mxDefaultToolbar>
</mxEditor>
