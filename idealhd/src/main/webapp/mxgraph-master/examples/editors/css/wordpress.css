/* Begin Typography & Colors */
body {
	font-size: 62.5%; /* Resets 1em to 10px */
	font-family: 'Lucida Grande', Verdana, Arial, Sans-Serif;
	background: #d5d6d7 url('../images/draw/drawbgcolor.jpg');
	color: #333;
	text-align: center;
	}

#page {
	background-color: white;
	border: 1px solid #959596;
	text-align: left;
	}

#header {
	background: #73a0c5 url('../images/draw/drawheader.jpg') no-repeat bottom center;
	}

#headerimg 	{ 
	margin: 7px 9px 0; 
	height: 62px; 
	width: 740px; 
	} 

#content {
	font-size: 1.2em
	}

.widecolumn .entry p {
	font-size: 1.05em;
	}

.narrowcolumn .entry, .widecolumn .entry {
	line-height: 1.4em;
	}

.widecolumn {
	line-height: 1.6em;
	}

.narrowcolumn .postmetadata {
	text-align: center;
	}

.alt {
	background-color: #f8f8f8;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	}

pre {
	background: #f8f8f8;
	font-size: 12px;
	padding: 8px;
}

#footer {
	background: #eee url('../images/draw/drawfooter.jpg') no-repeat top; 
	border: none;
	}

small {
	font-family: Arial, Helvetica, Sans-Serif;
	font-size: 0.9em;
	line-height: 1.5em;
	}

h1, h2, h3 {
	font-family: 'Trebuchet MS', 'Lucida Grande', Verdana, Arial, Sans-Serif;
	font-weight: bold;
	}

h1 {
	font-size: 2em;
	text-align: center;
	}

#headerimg .description {
	font-size: 1.2em;
	text-align: center;
	}

h2 {
	font-size: 1.6em;
	}

h2.pagetitle {
	font-size: 1.6em;
	}

#sidebar h2 {
	font-family: 'Lucida Grande', Verdana, Sans-Serif;
	font-size: 1.2em;
	}

h3 {
	font-size: 1.3em;
	}

h1, h1 a, h1 a:hover, h1 a:visited, #headerimg .description {
	text-decoration: none;
	color: white;
	}

h2, h2 a, h2 a:visited, h3, h3 a, h3 a:visited {
	color: #333;
	}

h2, h2 a, h2 a:hover, h2 a:visited, h3, h3 a, h3 a:hover, h3 a:visited, #sidebar h2, #wp-calendar caption, cite {
	text-decoration: none;
	}

.entry p a:visited {
	color: #b85b5a;
	}

.commentlist li, #commentform input, #commentform textarea {
	font: 0.9em 'Lucida Grande', Verdana, Arial, Sans-Serif;
	}

.commentlist li {
	font-weight: bold;
	}

.commentlist cite, .commentlist cite a {
	font-weight: bold;
	font-style: normal;
	font-size: 1.1em;
	}

.commentlist p {
	font-weight: normal;
	line-height: 1.5em;
	text-transform: none;
	}

#commentform p {
	font-family: 'Lucida Grande', Verdana, Arial, Sans-Serif;
	}

.commentmetadata {
	font-weight: normal;
	}

#sidebar {
	font: 1em 'Lucida Grande', Verdana, Arial, Sans-Serif;
	}

small, #sidebar ul ul li, #sidebar ul ol li, .nocomments, .postmetadata, blockquote, strike {
	color: #777;
	}

code {
	font: 1.1em 'Courier New', Courier, Fixed;
	}

acronym, abbr, span.caps
{
	font-size: 0.9em;
	letter-spacing: .07em;
	}

a, h2 a:hover, h3 a:hover {
	color: #06c;
	text-decoration: none;
	}

a:hover {
	color: #147;
	text-decoration: underline;
	}

#wp-calendar #prev a {
	font-size: 9pt;
	}

#wp-calendar a {
	text-decoration: none;
	}

#wp-calendar caption {
	font: bold 1.3em 'Lucida Grande', Verdana, Arial, Sans-Serif;
	text-align: center;
	}

#wp-calendar th {
	font-style: normal;
	text-transform: capitalize;
	}
/* End Typography & Colors */



/* Begin Structure */
body {
	margin: 0 0 20px 0;
	padding: 0; 
	}

#page {
	background-color: white;
	margin: 20px auto;
	padding: 0;
	width: 760px;
	border: 1px solid #959596;
	}

#header {
	background-color: #73a0c5;
	margin: 0 0 0 1px; 
	padding: 0; 
	height: 70px;
	width: 758px;
	}

#headerimg {
	margin: 0;
	height: 70px;
	width: 100%;
	}

.narrowcolumn {
	float: left;
	padding: 0 0 20px 45px;
	margin: 0px 0 0;
	width: 450px;
	}

.widecolumn {
	padding: 10px 0 20px 0;
	margin: 5px 0 0 150px;
	width: 450px;
	}

.post {
	margin: 0 0 40px;
/*	text-align: justify; */
	}

.widecolumn .post {
	margin: 0;
	}

.narrowcolumn .postmetadata {
	padding-top: 5px;
	}

.widecolumn .postmetadata {
	margin: 30px 0;
	}

.widecolumn .smallattachment {
	text-align: center;
	float: left;
	width: 128px;
	margin: 5px 5px 5px 0px;
}

.widecolumn .attachment {
	text-align: center;
	margin: 5px 0px;
}

.postmetadata {
	clear: left;
}

#footer {
	padding: 0;
	margin: 0 auto;
	width: 760px;
	clear: both;
	}

#footer p {
	margin: 0;
	padding: 20px 0;
	text-align: center;
	}
/* End Structure */



/*	Begin Headers */
h1 {
	padding-top: 30px;
	margin: 0;
	}

h2 {
	margin: 30px 0 0;
	}

h2.pagetitle {
	margin-top: 30px;
	text-align: center;
}

#sidebar h2 {
	margin: 5px 0 0;
	padding: 0;
	}

h3 {
	padding: 0;
	margin: 30px 0 0;
	}

h3.comments {
	padding: 0;
	margin: 40px auto 20px ;
	}
/* End Headers */



/* Begin Images */
p img {
	padding: 0;
	max-width: 100%;
	}

/*	Using 'class="alignright"' on an image will (who would've
	thought?!) align the image to the right. And using 'class="centered',
	will of course center the image. This is much better than using
	align="center", being much more futureproof (and valid) */

img.centered {
	display: block;
	margin-left: auto;
	margin-right: auto;
	}

img.alignright {
	padding: 4px;
	margin: 0 0 2px 7px;
	display: inline;
	}

img.alignleft {
	padding: 4px;
	margin: 0 7px 2px 0;
	display: inline;
	}

.alignright {
	float: right;
	}

.alignleft {
	float: left
	}
/* End Images */



/* Begin Lists

	Special stylized non-IE bullets
	Do not work in Internet Explorer, which merely default to normal bullets. */

html>body .entry ul {
	margin-left: 0px;
	padding: 0 0 0 30px;
	list-style: none;
	padding-left: 10px;
	text-indent: -10px;
	} 

html>body .entry li {
	margin: 7px 0 8px 10px;
	}

.entry ul li:before, #sidebar ul ul li:before {
	content: "\00BB \0020";
	}

.entry ol {
	padding: 0 0 0 35px;
	margin: 0;
	}

.entry ol li {
	margin: 0;
	padding: 0;
	}

.postmetadata ul, .postmetadata li {
	display: inline;
	list-style-type: none;
	list-style-image: none;
	}

#sidebar ul, #sidebar ul ol {
	margin: 0;
	padding: 0;
	}

#sidebar ul li {
	list-style-type: none;
	list-style-image: none;
	margin-bottom: 15px;
	}

#sidebar ul p, #sidebar ul select {
	margin: 5px 0 8px;
	}

#sidebar ul ul, #sidebar ul ol {
	margin: 5px 0 0 10px;
	}

#sidebar ul ul ul, #sidebar ul ol {
	margin: 0 0 0 10px;
	}

ol li, #sidebar ul ol li {
	list-style: decimal outside;
	}

#sidebar ul ul li, #sidebar ul ol li {
	margin: 3px 0 0;
	padding: 0;
	}
/* End Entry Lists */



/* Begin Form Elements */
#searchform {
	margin: 10px auto;
	padding: 5px 3px; 
	text-align: center;
	}

#sidebar #searchform #s {
	width: 108px;
	padding: 2px;
	}

#sidebar #searchsubmit {
	padding: 1px;
	}

.entry form { /* This is mainly for password protected posts, makes them look better. */
	text-align:center;
	}

select {
	width: 130px;
	}

#commentform input {
	width: 170px;
	padding: 2px;
	margin: 5px 5px 1px 0;
	}

#commentform textarea {
	width: 100%;
	padding: 2px;
	}

#commentform #submit {
	margin: 0;
	float: right;
	}
/* End Form Elements */



/* Begin Comments*/
.alt {
	margin: 0;
	padding: 10px;
	}

.commentlist {
	padding: 0;
/*	text-align: justify; */
	}

.commentlist li {
	margin: 15px 0 3px;
	padding: 5px 10px 3px;
	list-style: none;
	}

.commentlist p {
	margin: 10px 5px 10px 0;
	}

#commentform p {
	margin: 5px 0;
	}

.nocomments {
	text-align: center;
	margin: 0;
	padding: 0;
	}

.commentmetadata {
	margin: 0;
	display: block;
	}
/* End Comments */



/* Begin Sidebar */
#sidebar
{
	padding: 20px 0 10px 0;
	margin-left: 545px;
	width: 190px;
	}

#sidebar form {
	margin: 0;
	}
/* End Sidebar */



/* Begin Calendar */
#wp-calendar {
	empty-cells: show;
	margin: 10px auto 0;
	width: 155px;
	}

#wp-calendar #next a {
	padding-right: 10px;
	text-align: right;
	}

#wp-calendar #prev a {
	padding-left: 10px;
	text-align: left;
	}

#wp-calendar a {
	display: block;
	}

#wp-calendar caption {
	text-align: center;
	width: 100%;
	}

#wp-calendar td {
	padding: 3px 0;
	text-align: center;
	}

#wp-calendar td.pad:hover { /* Doesn't work in IE */
	background-color: #fff; }
/* End Calendar */



/* Begin Various Tags & Classes */
acronym, abbr, span.caps {
	cursor: help;
	}

acronym, abbr {
	border-bottom: 1px dashed #999;
	}

blockquote {
	margin: 15px 30px 0 10px;
	padding-left: 20px;
	border-left: 5px solid #ddd;
	}

blockquote cite {
	margin: 5px 0 0;
	display: block;
	}

.center {
	text-align: center;
	}

a img {
	border: none;
	}

.navigation {
	display: block;
	text-align: center;
	margin-top: 10px;
	margin-bottom: 60px;
	}
/* End Various Tags & Classes*/

