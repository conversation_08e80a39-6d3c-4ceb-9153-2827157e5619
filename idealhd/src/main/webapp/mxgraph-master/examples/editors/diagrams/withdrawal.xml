<mxGraphModel>
	<root>
		<Workflow label="Withdrawal" id="0">
			<mxCell />
		</Workflow>
		<Layer label="Default Layer" id="1">
			<mxCell parent="0" />
		</Layer>
		<Swimlane label="Customer" customAttribute="text value"
			id="2">
			<mxCell style="swimlane;fillColor=#66B922"
				parent="1" connectable="0" vertex="1">
				<mxGeometry x="20" y="20" width="220" height="900"
					as="geometry" />
			</mxCell>
		</Swimlane>
		<Shape label="Start" id="3">
			<mxCell style="ellipse" parent="2" vertex="1">
				<mxGeometry x="70" y="40" width="60" height="50"
					as="geometry" />
			</mxCell>
		</Shape>
		<Subprocess label="Insert Card" id="4">
			<mxCell style="rounded" parent="2" vertex="1">
				<mxGeometry x="50" y="110" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Edge id="5">
			<mxCell parent="2" source="3" target="4" edge="1">
				<mxGeometry as="geometry" />
			</mxCell>
		</Edge>
		<Subprocess label="Enter PIN" id="6">
			<mxCell style="rounded" parent="2" vertex="1">
				<mxGeometry x="50" y="160" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Edge id="7">
			<mxCell parent="2" source="4" target="6" edge="1">
				<mxGeometry as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Subprocess label="Enter Amount" id="8">
			<mxCell style="rounded" parent="2" vertex="1">
				<mxGeometry x="50" y="260" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Shape label="" id="9">
			<mxCell style="hline" parent="2" vertex="1">
				<mxGeometry y="390" width="600" height="10"
					as="geometry" />
			</mxCell>
		</Shape>
		<Subprocess label="Take Money&#xa;from Slot" id="10">
			<mxCell style="rounded" parent="2" vertex="1">
				<mxGeometry x="40" y="430" width="100" height="50"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Edge id="11">
			<mxCell parent="2" source="9" target="10" edge="1">
				<mxGeometry as="geometry">
					<Array as="points">
						<mxPoint x="90" y="420" />
					</Array>
				</mxGeometry>
			</mxCell>
		</Edge>
		<Subprocess label="Take Card" id="12">
			<mxCell style="rounded" parent="2" vertex="1">
				<mxGeometry x="40" y="790" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Shape label="End" id="13">
			<mxCell style="ellipse" parent="2" vertex="1">
				<mxGeometry x="60" y="850" width="60" height="50"
					as="geometry" />
			</mxCell>
		</Shape>
		<Edge id="14">
			<mxCell parent="2" source="12" target="13" edge="1">
				<mxGeometry as="geometry" />
			</mxCell>
		</Edge>
		<Swimlane label="ATM Machine" customAttribute="text value"
			id="15">
			<mxCell style="swimlane;fillColor=#CF0056"
				parent="1" connectable="0" vertex="1">
				<mxGeometry x="240" y="20" width="200" height="900"
					as="geometry" />
			</mxCell>
		</Swimlane>
		<Shape label="" id="16">
			<mxCell style="hline" parent="15" vertex="1">
				<mxGeometry x="40" y="510" width="100" height="10"
					as="geometry" />
			</mxCell>
		</Shape>
		<Shape label="" id="17">
			<mxCell style="rhombus" parent="15" vertex="1">
				<mxGeometry x="70" y="550" width="40" height="40"
					as="geometry" />
			</mxCell>
		</Shape>
		<Edge id="18">
			<mxCell parent="15" source="16" target="17" edge="1">
				<mxGeometry as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Subprocess label="Show Balance" id="19">
			<mxCell style="rounded" parent="15" vertex="1">
				<mxGeometry x="40" y="610" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Edge id="20">
			<mxCell parent="15" source="17" target="19" edge="1">
				<mxGeometry y="10" as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Shape label="" id="21">
			<mxCell style="rhombus" parent="15" vertex="1">
				<mxGeometry x="70" y="660" width="40" height="40"
					as="geometry" />
			</mxCell>
		</Shape>
		<Edge id="22">
			<mxCell parent="15" source="19" target="21" edge="1">
				<mxGeometry as="geometry" />
			</mxCell>
		</Edge>
		<Subprocess label="Eject Card" id="23">
			<mxCell style="rounded" parent="15" vertex="1">
				<mxGeometry x="40" y="730" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Edge id="24">
			<mxCell parent="15" source="21" target="23" edge="1">
				<mxGeometry as="geometry" />
			</mxCell>
		</Edge>
		<Swimlane label="Bank" customAttribute="text value" id="25">
			<mxCell style="swimlane;fillColor=#4679B6"
				parent="1" connectable="0" vertex="1">
				<mxGeometry x="440" y="20" width="210" height="900"
					as="geometry" />
			</mxCell>
		</Swimlane>
		<Shape label="" id="26">
			<mxCell style="rhombus" parent="25" vertex="1">
				<mxGeometry x="80" y="160" width="40" height="40"
					as="geometry" />
			</mxCell>
		</Shape>
		<Subprocess label="Check Account&#xa;Balance" id="27">
			<mxCell style="rounded" parent="25" vertex="1">
				<mxGeometry x="50" y="250" width="100" height="50"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Subprocess label="Authorize" id="28">
			<mxCell style="rounded" parent="25" vertex="1">
				<mxGeometry x="50" y="110" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Edge id="29">
			<mxCell parent="25" source="28" target="26" edge="1">
				<mxGeometry y="-50" as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Shape label="" id="30">
			<mxCell style="rhombus" parent="25" vertex="1">
				<mxGeometry x="80" y="330" width="40" height="40"
					as="geometry" />
			</mxCell>
		</Shape>
		<Edge id="31">
			<mxCell parent="25" source="27" target="30" edge="1">
				<mxGeometry as="geometry" />
			</mxCell>
		</Edge>
		<Subprocess label="Debit Account" id="32">
			<mxCell style="rounded" parent="25" vertex="1">
				<mxGeometry x="50" y="440" width="100" height="30"
					as="geometry" />
			</mxCell>
		</Subprocess>
		<Edge id="33">
			<mxCell parent="1" source="6" target="28" edge="1">
				<mxGeometry as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge label="[Valid PIN]" id="34">
			<mxCell style="verticalEdge" parent="1" source="26"
				target="8" edge="1">
				<mxGeometry as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge id="35">
			<mxCell parent="1" source="8" target="27" edge="1">
				<mxGeometry as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge label="[balance &gt;= amount]" id="36">
			<mxCell parent="1" source="30" target="9" edge="1">
				<mxGeometry as="geometry">
					<Array as="points" />
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge id="37">
			<mxCell parent="1" source="9" target="32" edge="1">
				<mxGeometry as="geometry">
					<Array as="points">
						<mxPoint x="540" y="440" />
					</Array>
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge id="38">
			<mxCell parent="1" source="32" target="16" edge="1">
				<mxGeometry as="geometry">
					<Array as="points">
						<mxPoint x="364" y="503" />
					</Array>
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge id="39">
			<mxCell parent="1" source="10" target="16" edge="1">
				<mxGeometry as="geometry">
					<Array as="points">
						<mxPoint x="304" y="503" />
					</Array>
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge label="" id="40">
			<mxCell parent="1" source="30" target="17" edge="1">
				<mxGeometry as="geometry">
					<Array as="points">
						<mxPoint x="654" y="463" />
					</Array>
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge id="41">
			<mxCell parent="1" source="26" target="21" edge="1">
				<mxGeometry as="geometry">
					<Array as="points">
						<mxPoint x="664" y="453" />
					</Array>
				</mxGeometry>
			</mxCell>
		</Edge>
		<Edge id="42">
			<mxCell parent="1" source="23" target="12" edge="1">
				<mxGeometry as="geometry">
					<Array as="points">
						<mxPoint x="110" y="783" />
					</Array>
				</mxGeometry>
			</mxCell>
		</Edge>
	</root>
</mxGraphModel>
