# Custom file format for fileio.html (comments start with #, all vertices first)

# Vertices (id: label)
1: <img src="editors/images/overlays/user3.png" width="16" height="16"><br><b>Last, First</b><br>Status<br>Info
2: <img src="editors/images/overlays/error.png" width="16" height="16"><br><b>Errorcode</b><br>Status<br>Info
3: <img src="editors/images/overlays/flash.png" width="16" height="16"><br><b>Warning</b><br>Status<br>Info
4: <img src="editors/images/overlays/users3.png" width="16" height="16"><br><b>Groupname</b><br>Status<br>Info
5: <img src="editors/images/overlays/workplace.png" width="16" height="16"><br><b>Workplace</b><br>Status<br>Info
6: <img src="editors/images/overlays/information.png" width="16" height="16"><br><b>Information</b><br>Status<br>Info
7: <img src="editors/images/overlays/printer.png" width="16" height="16"><br><b>Printername</b><br>Status<br>Info

# Edges (source-id,target-id: label)
1,2: <img src="editors/images/overlays/lightbulb_on.png" width="16" height="16"> Hint
1,3: <img src="editors/images/overlays/help.png" width="16" height="16"> News
1,4: <img src="editors/images/overlays/information.png" width="16" height="16"> Member
5,6: <img src="editors/images/overlays/pencil.png" width="16" height="16"> Details
5,7: <img src="editors/images/overlays/check.png" width="16" height="16"> Access
4,5: <img src="editors/images/overlays/forbidden.png" width="16" height="16"> Access
1,5: <img src="editors/images/overlays/lightbulb_on.png" width="16" height="16"> 2-Way
