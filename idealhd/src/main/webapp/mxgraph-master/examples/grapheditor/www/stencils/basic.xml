<shapes name="mxgraph.basic">
<shape name="4 Point Star" h="92" w="92" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="46" y="0"/>
<line x="56" y="36"/>
<line x="92" y="46"/>
<line x="56" y="56"/>
<line x="46" y="92"/>
<line x="36" y="56"/>
<line x="0" y="46"/>
<line x="36" y="36"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="6 Point Star" h="84.5" w="96" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.24" y="0" perimeter="0" name="N1"/>
<constraint x="0.24" y="1" perimeter="0" name="S1"/>
<constraint x="0.76" y="0" perimeter="0" name="N2"/>
<constraint x="0.76" y="1" perimeter="0" name="S2"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="23" y="28.9"/>
<line x="23" y="0"/>
<line x="48" y="14.4"/>
<line x="73" y="0"/>
<line x="73" y="28.9"/>
<line x="96" y="42.2"/>
<line x="73" y="55.6"/>
<line x="73" y="84.5"/>
<line x="48" y="70"/>
<line x="23" y="84.5"/>
<line x="23" y="55.6"/>
<line x="0" y="42.2"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="8 Point Star" h="96" w="96" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.29" y="0" perimeter="0" name="N1"/>
<constraint x="0.29" y="1" perimeter="0" name="S1"/>
<constraint x="0.71" y="0" perimeter="0" name="N2"/>
<constraint x="0.71" y="1" perimeter="0" name="S2"/>
<constraint x="0" y="0.29" perimeter="0" name="W1"/>
<constraint x="0" y="0.71" perimeter="0" name="W2"/>
<constraint x="1" y="0.29" perimeter="0" name="E1"/>
<constraint x="1" y="0.71" perimeter="0" name="E2"/>
</connections>
<background>
<path>
<move x="28" y="28"/>
<line x="28" y="0"/>
<line x="48" y="20"/>
<line x="68" y="0"/>
<line x="68" y="28"/>
<line x="96" y="28"/>
<line x="76" y="48"/>
<line x="96" y="68"/>
<line x="68" y="68"/>
<line x="68" y="96"/>
<line x="48" y="76"/>
<line x="28" y="96"/>
<line x="28" y="68"/>
<line x="0" y="68"/>
<line x="20" y="48"/>
<line x="0" y="28"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Banner" h="50" w="96" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.8" perimeter="0" name="S"/>
<constraint x="0.13" y="0.6" perimeter="0" name="W"/>
<constraint x="0.87" y="0.6" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="0" y="50"/>
<line x="38" y="50"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="40.5" y="47.5"/>
<line x="40.5" y="40"/>
<line x="55.5" y="40"/>
<line x="55.5" y="47.5"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="58" y="50"/>
<line x="96" y="50"/>
<line x="83" y="30"/>
<line x="96" y="10"/>
<line x="70.5" y="10"/>
<line x="70.5" y="2.5"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="68" y="0"/>
<line x="28" y="0"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="25.5" y="2.5"/>
<line x="25.5" y="10"/>
<line x="0" y="10"/>
<line x="13" y="30"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
<path>
<move x="40.5" y="47.5"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="38" y="45"/>
<line x="28" y="45"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="1" sweep-flag="1" x="28" y="40"/>
<line x="68" y="40"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="68" y="45"/>
<line x="58" y="45"/>
<arc rx="2.5" ry="2.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="55.5" y="47.5"/>
<move x="25.5" y="42.5"/>
<line x="25.5" y="10"/>
<move x="70.5" y="42.5"/>
<line x="70.5" y="10"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Cloud Callout" h="61.4" w="90.41" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.74" perimeter="0" name="S"/>
<constraint x="0.015" y="0.4" perimeter="0" name="W"/>
<constraint x="0.993" y="0.4" perimeter="0" name="E"/>
<constraint x="0.01" y="0.995" perimeter="0" name="SW"/>
</connections>
<background>
<save/>
<linejoin join="round"/>
<path>
<move x="12.1" y="31.8"/>
<arc rx="8" ry="8" x-axis-rotation="0" large-arc-flag="1" sweep-flag="1" x="12.1" y="16.8"/>
<arc rx="12" ry="12" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="33.1" y="8.8"/>
<arc rx="14" ry="14" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="59.1" y="8.8"/>
<arc rx="12" ry="12" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="79.1" y="16.8"/>
<arc rx="8" ry="8" x-axis-rotation="0" large-arc-flag="1" sweep-flag="1" x="79.1" y="31.8"/>
<arc rx="12" ry="12" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="58.1" y="38.8"/>
<arc rx="14" ry="14" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="34.1" y="38.8"/>
<arc rx="10" ry="8" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="12.1" y="31.8"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
<restore/>
<linejoin join="miter"/>
<ellipse x="9.1" y="46.1" w="12" h="5.4"/>
<fillstroke/>
<ellipse x="4.3" y="53.5" w="7.6" h="3.6"/>
<fillstroke/>
<ellipse x="0" y="58.8" w="4.8" h="2.6"/>
<fillstroke/>
</foreground>
</shape>
<shape name="Cone" h="96.91" w="99" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
</connections>
<background>
<path>
<move x="49.5" y="0"/>
<line x="99" y="88"/>
<arc rx="25" ry="4.5" x-axis-rotation="0" large-arc-flag="1" sweep-flag="1" x="0" y="88"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
<path>
<move x="0" y="88"/>
<arc rx="25" ry="4.5" x-axis-rotation="0" large-arc-flag="1" sweep-flag="1" x="99" y="88"/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Cross" h="98" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="0" y="34"/>
<line x="34" y="34"/>
<line x="34" y="0"/>
<line x="64" y="0"/>
<line x="64" y="34"/>
<line x="98" y="34"/>
<line x="98" y="64"/>
<line x="64" y="64"/>
<line x="64" y="98"/>
<line x="34" y="98"/>
<line x="34" y="64"/>
<line x="0" y="64"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Document" h="98" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="98" y="14"/>
<line x="98" y="98"/>
<line x="0" y="98"/>
<line x="0" y="0"/>
<line x="84" y="0"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
<path>
<move x="84" y="0"/>
<arc rx="18" ry="10" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="79" y="9"/>
<line x="98" y="14"/>
</path>
<stroke/>
</foreground>
</shape>
<shape name="Flash" h="95.5" w="60" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.565" y="0" perimeter="0" name="N"/>
<constraint x="0" y="0.995" perimeter="0" name="SW"/>
</connections>
<background>

<miterlimit limit="6"/>
<path>
<move x="0" y="95.5"/>
<line x="20" y="75.5"/>
<line x="3" y="61.5"/>
<line x="20" y="49.5"/>
<line x="3" y="31.5"/>
<line x="34" y="0"/>
<line x="60" y="25.5"/>
<line x="36" y="39.5"/>
<line x="50" y="53.5"/>
<line x="29" y="65.5"/>
<line x="42" y="76.5"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Half Circle" h="49" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
</connections>
<background>
<path>
<move x="0" y="0"/>
<arc rx="44.5" ry="44.5" x-axis-rotation="0" large-arc-flag="1" sweep-flag="0" x="98" y="0"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Heart" h="94.74" w="103.89" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.115" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.07" y="0.5" perimeter="0" name="W"/>
<constraint x="0.93" y="0.5" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="51.94" y="94.74"/>
<curve x1="55.79" y1="90.78" x2="77.8" y2="68.16" x3="91.56" y3="54.03"/>
<curve x1="103.89" y1="41.37" x2="103.62" y2="22.91" x3="92.42" y3="11.46"/>
<curve x1="81.21" y1="0" x2="63.09" y2="0.05" x3="51.94" y3="11.56"/>
<curve x1="40.79" y1="0.05" x2="22.67" y2="0" x3="11.47" y3="11.45"/>
<curve x1="0.26" y1="22.9" x2="0" y2="41.36" x3="12.32" y3="54.03"/>
<curve x1="26.08" y1="68.16" x2="48.09" y2="90.78" x3="51.94" y3="94.74"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Loud Callout" h="59.9" w="93.3" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.49" y="0" perimeter="0" name="N"/>
<constraint x="0.52" y="0.91" perimeter="0" name="S"/>
<constraint x="0" y="0.51" perimeter="0" name="W"/>
<constraint x="0.99" y="0.503" perimeter="0" name="E"/>
<constraint x="0.04" y="1" perimeter="0" name="SE"/>
</connections>
<background>

<miterlimit limit="10"/>
<path>
<move x="14.9" y="43.9"/>
<line x="9.3" y="46.7"/>
<line x="11.1" y="40.9"/>
<line x="6.6" y="43.9"/>
<line x="8.3" y="39.2"/>
<line x="2.8" y="40.8"/>
<line x="6.6" y="36.4"/>
<line x="0.9" y="36.2"/>
<line x="5.8" y="32.7"/>
<line x="0" y="30.8"/>
<line x="5.3" y="28.2"/>
<line x="0.3" y="25.6"/>
<line x="5.9" y="24.19"/>
<line x="0.8" y="19.9"/>
<line x="6.5" y="19.8"/>
<line x="2.8" y="15.1"/>
<line x="8.2" y="16.1"/>
<line x="5.9" y="11.3"/>
<line x="11.5" y="13.2"/>
<line x="10.2" y="8.7"/>
<line x="15.7" y="10.6"/>
<line x="14.9" y="6.15"/>
<line x="19.2" y="9.3"/>
<line x="19.8" y="4.3"/>
<line x="23.4" y="8"/>
<line x="23.8" y="3.4"/>
<line x="28.5" y="6.9"/>
<line x="30.3" y="1.3"/>
<line x="33.3" y="6.2"/>
<line x="34.7" y="0.6"/>
<line x="38.2" y="6"/>
<line x="40.6" y="0"/>
<line x="42.8" y="5.8"/>
<line x="45.6" y="0"/>
<line x="47.1" y="6"/>
<line x="51.3" y="1"/>
<line x="50.8" y="6.3"/>
<line x="55.4" y="0.6"/>
<line x="55.1" y="6.6"/>
<line x="60.5" y="1.4"/>
<line x="61.1" y="7.1"/>
<line x="66.1" y="2.7"/>
<line x="66.2" y="8.7"/>
<line x="71.9" y="4.4"/>
<line x="70.5" y="10"/>
<line x="77.6" y="6.2"/>
<line x="74.9" y="11.8"/>
<line x="83.9" y="7.8"/>
<line x="80.1" y="13.6"/>
<line x="88.1" y="11.9"/>
<line x="85.2" y="17"/>
<line x="91.2" y="16.9"/>
<line x="87" y="20.1"/>
<line x="93.3" y="21.2"/>
<line x="87.9" y="24"/>
<line x="93.2" y="25.8"/>
<line x="86.8" y="26.8"/>
<line x="92.4" y="30.3"/>
<line x="86.6" y="30.8"/>
<line x="90.9" y="34.8"/>
<line x="84.2" y="33.5"/>
<line x="87.8" y="38.8"/>
<line x="82" y="36.6"/>
<line x="84.7" y="41.7"/>
<line x="79.2" y="40.7"/>
<line x="79.8" y="46"/>
<line x="76.3" y="42.9"/>
<line x="75.6" y="48.6"/>
<line x="72" y="44.7"/>
<line x="71.7" y="51.2"/>
<line x="68" y="46"/>
<line x="66.2" y="52.1"/>
<line x="63.7" y="46.6"/>
<line x="61.2" y="53.7"/>
<line x="59.7" y="47.6"/>
<line x="56.9" y="53.8"/>
<line x="55" y="48.1"/>
<line x="52.8" y="53.9"/>
<line x="50.9" y="48.1"/>
<line x="48.4" y="54.5"/>
<line x="47" y="48.1"/>
<line x="44.4" y="53.7"/>
<line x="43.2" y="47.4"/>
<line x="40.1" y="54.2"/>
<line x="38.8" y="47.4"/>
<line x="36.3" y="54.7"/>
<line x="35.6" y="47.8"/>
<line x="32.4" y="55.1"/>
<line x="30.9" y="46.6"/>
<line x="28.6" y="53.3"/>
<line x="26.8" y="47.8"/>
<line x="3.8" y="59.9"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Moon" h="103.05" w="77.05" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.48" y="0" perimeter="0" name="N"/>
<constraint x="1" y="0.89" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="37.05" y="0"/>
<arc rx="48" ry="48" x-axis-rotation="0" large-arc-flag="1" sweep-flag="0" x="77.05" y="92"/>
<arc rx="60" ry="60" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="37.05" y="0"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="No Symbol" h="100" w="100" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.145" y="0.145" perimeter="0" name="NW"/>
<constraint x="0.145" y="0.855" perimeter="0" name="SW"/>
<constraint x="0.855" y="0.145" perimeter="0" name="NE"/>
<constraint x="0.855" y="0.855" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="50"/>
<arc rx="35" ry="35" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="100" y="50"/>
<arc rx="35" ry="35" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="0" y="50"/>
<close/>
<move x="78.95" y="69.7"/>
<arc rx="35" ry="35" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="30.3" y="21.05"/>
<close/>
<move x="21.15" y="30.3"/>
<arc rx="35" ry="35" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="69.7" y="79"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Octagon" h="98" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="0" y="29"/>
<line x="29" y="0"/>
<line x="69" y="0"/>
<line x="98" y="29"/>
<line x="98" y="69"/>
<line x="69" y="98"/>
<line x="29" y="98"/>
<line x="0" y="69"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Orthogonal Triangle" h="97" w="97" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
<constraint x="0.5" y="0.5" perimeter="0" name="center"/>
</connections>
<background>
<path>
<move x="0" y="97"/>
<line x="0" y="0"/>
<line x="97" y="97"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Oval Callout" h="63.15" w="109.43" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.045" perimeter="0" name="N"/>
<constraint x="0.5" y="0.84" perimeter="0" name="S"/>
<constraint x="0.045" y="0.45" perimeter="0" name="W"/>
<constraint x="0.945" y="0.45" perimeter="0" name="E"/>
<constraint x="0.08" y="1" perimeter="0" name="SW"/>
</connections>
<background>

<miterlimit limit="15"/>
<path>
<move x="20.53" y="46.15"/>
<arc rx="49" ry="25" x-axis-rotation="0" large-arc-flag="1" sweep-flag="1" x="31.53" y="50.15"/>
<arc rx="30" ry="30" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="9.03" y="63.15"/>
<arc rx="30" ry="30" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="20.53" y="46.15"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Parallelepiped" h="60" w="97" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.12" y="0.5" perimeter="0" name="W"/>
<constraint x="0.88" y="0.5" perimeter="0" name="E"/>
<constraint x="0.24" y="0" perimeter="0" name="NW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="0.76" y="1" perimeter="0" name="SE"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
</connections>
<background>
<path>
<move x="0" y="60"/>
<line x="23.5" y="0"/>
<line x="97" y="0"/>
<line x="73.5" y="60"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Pentagon" h="90" w="97" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0" y="0.365" perimeter="0" name="W"/>
<constraint x="1" y="0.365" perimeter="0" name="E"/>
<constraint x="0.81" y="1" perimeter="0" name="SE"/>
<constraint x="0.19" y="1" perimeter="0" name="SW"/>
</connections>
<background>
<path>
<move x="18.5" y="90"/>
<line x="0" y="33"/>
<line x="48.5" y="0"/>
<line x="97" y="33"/>
<line x="78.5" y="90"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Rectangular Callout" h="60" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.715" perimeter="0" name="S"/>
<constraint x="0" y="0.355" perimeter="0" name="W"/>
<constraint x="1" y="0.355" perimeter="0" name="E"/>
<constraint x="0.04" y="1" perimeter="0" name="SW"/>
</connections>
<background>

<miterlimit limit="10"/>
<path>
<move x="15" y="43"/>
<line x="0" y="43"/>
<line x="0" y="0"/>
<line x="98" y="0"/>
<line x="98" y="43"/>
<line x="29" y="43"/>
<line x="4" y="60"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Rounded Rectangular Callout" h="60" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.715" perimeter="0" name="S"/>
<constraint x="0" y="0.355" perimeter="0" name="W"/>
<constraint x="1" y="0.355" perimeter="0" name="E"/>
<constraint x="0.04" y="1" perimeter="0" name="SW"/>
</connections>
<background>

<miterlimit limit="15"/>
<path>
<move x="15.5" y="43"/>
<line x="5" y="43"/>
<arc rx="5" ry="5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="0" y="38"/>
<line x="0" y="5"/>
<arc rx="5" ry="5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="5" y="0"/>
<line x="93" y="0"/>
<arc rx="5" ry="5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="98" y="5"/>
<line x="98" y="38"/>
<arc rx="5" ry="5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="93" y="43"/>
<line x="29" y="43"/>
<arc rx="30" ry="30" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="4" y="60"/>
<arc rx="30" ry="30" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="15.5" y="43"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Smiley" h="98" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.145" y="0.145" perimeter="0" name="NW"/>
<constraint x="0.145" y="0.855" perimeter="0" name="SW"/>
<constraint x="0.855" y="0.145" perimeter="0" name="NE"/>
<constraint x="0.855" y="0.855" perimeter="0" name="SE"/>
</connections>
<background>
<ellipse x="0" y="0" w="98" h="98"/>
</background>
<foreground>
<fillstroke/>
<save/>
<path>
<move x="11" y="54"/>
<arc rx="38" ry="27" x-axis-rotation="0" large-arc-flag="1" sweep-flag="0" x="87" y="54"/>
</path>
<stroke/>
<restore/>
<strokewidth width="1"/>
<path>
<move x="16" y="51"/>
<line x="6" y="57"/>
</path>
<stroke/>
<path>
<move x="82" y="51"/>
<line x="92" y="57"/>
</path>
<stroke/>
<strokewidth width="6"/>
<ellipse x="24" y="27" w="6" h="16"/>
<fillstroke/>
<ellipse x="68" y="27" w="6" h="16"/>
<fillstroke/>
</foreground>
</shape>
<shape name="Star" h="90" w="95" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="0.76" perimeter="0" name="S"/>
<constraint x="0" y="0.367" perimeter="0" name="W"/>
<constraint x="1" y="0.367" perimeter="0" name="E"/>
<constraint x="0.185" y="1" perimeter="0" name="SW"/>
<constraint x="0.815" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="33"/>
<line x="36.4" y="33"/>
<line x="47.5" y="0"/>
<line x="58.6" y="33"/>
<line x="95" y="33"/>
<line x="66" y="55.1"/>
<line x="77.5" y="90"/>
<line x="47.5" y="68.4"/>
<line x="17.5" y="90"/>
<line x="29" y="55.1"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Sun" h="95" w="95" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
<constraint x="0.145" y="0.145" perimeter="0" name="NW"/>
<constraint x="0.145" y="0.855" perimeter="0" name="SW"/>
<constraint x="0.855" y="0.145" perimeter="0" name="NE"/>
<constraint x="0.855" y="0.855" perimeter="0" name="SE"/>
</connections>
<background>
<ellipse x="17.5" y="17.5" w="60" h="60"/>
</background>
<foreground>
<fillstroke/>
<path>
<move x="42.5" y="14.5"/>
<line x="47.5" y="0"/>
<line x="52.5" y="14.5"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="42.5" y="80.5"/>
<line x="47.5" y="95"/>
<line x="52.5" y="80.5"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="14.5" y="42.5"/>
<line x="0" y="47.5"/>
<line x="14.5" y="52.5"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="80.5" y="42.5"/>
<line x="95" y="47.5"/>
<line x="80.5" y="52.5"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="67.5" y="20.5"/>
<line x="81.2" y="13.9"/>
<line x="74.5" y="27.5"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="67.5" y="74.5"/>
<line x="81.2" y="81.1"/>
<line x="74.5" y="67.5"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="27.5" y="20.5"/>
<line x="13.8" y="13.9"/>
<line x="20.5" y="27.5"/>
<close/>
</path>
<fillstroke/>
<path>
<move x="27.5" y="74.5"/>
<line x="13.8" y="81.1"/>
<line x="20.5" y="67.5"/>
<close/>
</path>
<fillstroke/>
</foreground>
</shape>
<shape name="Tick" h="97.54" w="84.4" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.9" y="0.01" perimeter="0" name="N"/>
<constraint x="0.32" y="0.992" perimeter="0" name="S"/>
<constraint x="0" y="0.7" perimeter="0" name="W"/>
<constraint x="1" y="0.06" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="0.36" y="66.69"/>
<arc rx="12" ry="12" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="16.36" y="58.69"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="26.36" y="69.69"/>
<arc rx="200" ry="200" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="63.36" y="5.69"/>
<arc rx="18" ry="18" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="80.36" y="1.69"/>
<arc rx="4.5" ry="4.5" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="83.36" y="8.69"/>
<arc rx="230" ry="230" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="35.36" y="94.69"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="17.36" y="94.69"/>
<arc rx="100" ry="100" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="0.36" y="68.69"/>
<arc rx="2" ry="2" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="0.36" y="66.69"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Trapezoid" h="98" w="97" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0.12" y="0.5" perimeter="0" name="W"/>
<constraint x="0.88" y="0.5" perimeter="0" name="E"/>
<constraint x="0.24" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="0.76" y="0" perimeter="0" name="NE"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="98"/>
<line x="23.5" y="0"/>
<line x="73.5" y="0"/>
<line x="97" y="98"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="Wave" h="56.7" w="98" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.295" perimeter="0" name="N"/>
<constraint x="0.5" y="1" perimeter="0" name="S"/>
<constraint x="0" y="0.5" perimeter="0" name="W"/>
<constraint x="1" y="0.5" perimeter="0" name="E"/>
</connections>
<background>
<path>
<move x="0" y="8.7"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="33" y="8.7"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="65" y="8.7"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="98" y="8.7"/>
<line x="98" y="48.7"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="65" y="48.7"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="1" x="33" y="48.7"/>
<arc rx="20" ry="20" x-axis-rotation="0" large-arc-flag="0" sweep-flag="0" x="0" y="48.7"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
<shape name="X" h="98" w="96" aspect="variable" strokewidth="inherit">
<connections>
<constraint x="0.5" y="0.29" perimeter="0" name="N"/>
<constraint x="0.5" y="0.71" perimeter="0" name="S"/>
<constraint x="0.33" y="0.5" perimeter="0" name="W"/>
<constraint x="0.65" y="0.5" perimeter="0" name="E"/>
<constraint x="0" y="0" perimeter="0" name="NW"/>
<constraint x="0" y="1" perimeter="0" name="SW"/>
<constraint x="1" y="0" perimeter="0" name="NE"/>
<constraint x="1" y="1" perimeter="0" name="SE"/>
</connections>
<background>
<path>
<move x="0" y="0"/>
<line x="28" y="0"/>
<line x="48" y="29"/>
<line x="68" y="0"/>
<line x="96" y="0"/>
<line x="62" y="49"/>
<line x="96" y="98"/>
<line x="68" y="98"/>
<line x="48" y="69"/>
<line x="28" y="98"/>
<line x="0" y="98"/>
<line x="32" y="49"/>
<close/>
</path>
</background>
<foreground>
<fillstroke/>
</foreground>
</shape>
</shapes>