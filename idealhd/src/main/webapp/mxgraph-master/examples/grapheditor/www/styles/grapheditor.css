.geEditor {
	font-family:Helvetica Neue,Helvetica,Arial Unicode MS,Arial;
	font-size:10pt;
	border:none;
	margin:0px;
}
.geMenubarContainer .geItem, .geToolbar .geButton, .geToolbar .geLabel, .geSidebarContainer .geTitle {
	cursor:pointer !important;
}
.geContentEditable div, .geContentEditable table, .geContentEditable table td {
	border: 1px dashed lightGray;
}
.geBackgroundPage {
  	-webkit-box-shadow:0px 0px 3px 0px #d9d9d9;
	-moz-box-shadow:0px 0px 3px 0px #d9d9d9;
  	box-shadow:0px 0px 3px 0px #d9d9d9;
}
.geSidebarContainer a, .geMenubarContainer a, .geToolbar a {
	color:#000000;
	text-decoration:none;
}
.geMenubarContainer, .geToolbarContainer, .geDiagramContainer, .geSidebarContainer, .geFooterContainer, .geHsplit, .geVsplit {
	overflow:hidden;
	position:absolute;
	cursor:default;
}
.geDiagramContainer {
	background-color:#ffffff;
	outline:none;
}
.geMenubar, .geToolbar {
	white-space:nowrap;
	display:block;
	width:100%;
}
.geMenubarContainer .geItem, .geToolbar .geButton, .geToolbar .geLabel, .geSidebar, .geSidebarContainer .geTitle, .geSidebar .geItem, .mxPopupMenuItem {
	-webkit-transition: all 0.1s ease-in-out;
	-moz-transition: all 0.1s ease-in-out;
	-o-transition: all 0.1s ease-in-out;
	-ms-transition: all 0.1s ease-in-out;
	transition: all 0.1s ease-in-out;
}
.geHint {
	background-color: #ffffff;
	border: 1px solid gray;
	padding: 4px 16px 4px 16px;
	border-radius:3px;
	-webkit-box-shadow: 1px 1px 2px 0px #ddd;
	-moz-box-shadow: 1px 1px 2px 0px #ddd;
	box-shadow: 1px 1px 2px 0px #ddd;
	opacity:0.8;
	filter:alpha(opacity=80);
}
.geStatusAlert {
	white-space:nowrap;
	margin-top:-5px;
	font-size:12px;
	padding:4px 6px 4px 6px;
	background-color:#f2dede;
	border:1px solid #ebccd1;
	color:#a94442 !important;
	border-radius:3px;
}
.geStatusAlert:hover {
	background-color:#f1d8d8;
	border-color:#d6b2b8;
}
.geStatusMessage {
	white-space:nowrap;
	margin-top:-5px;
	padding:4px 6px 4px 6px;
	font-size:12px;
	background-image: -webkit-linear-gradient(top,#dff0d8 0,#c8e5bc 100%);
    background-image: -o-linear-gradient(top,#dff0d8 0,#c8e5bc 100%);
    background-image: -webkit-gradient(linear,left top,left bottom,from(#dff0d8),to(#c8e5bc));
    background-image: linear-gradient(to bottom,#dff0d8 0,#c8e5bc 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdff0d8', endColorstr='#ffc8e5bc', GradientType=0);
    background-repeat: repeat-x;
    border:1px solid #b2dba1;
	border-radius:3px;
	color:#3c763d !important;
}
.geStatusMessage:hover {
	background:#c8e5bc;
	border-color:#b2dba1;
}
.geAlert {
	position:absolute;
	white-space:nowrap;
	padding:14px;
	background-color:#f2dede;
	border:1px solid #ebccd1;
	color:#a94442;
	border-radius:3px;
	-webkit-box-shadow: 2px 2px 3px 0px #ddd;
	-moz-box-shadow: 2px 2px 3px 0px #ddd;
	box-shadow: 2px 2px 3px 0px #ddd;
}
.geBtn {
	background-color: #f5f5f5;
	border-radius: 2px;
	border: 1px solid #d8d8d8;
	color: #333;
	cursor: default;
	font-size: 11px;
	font-weight: bold;
	height: 29px;
	line-height: 27px;
	margin: 0 0 0 8px;
	min-width: 72px;
	outline: 0;
	padding: 0 8px;
	cursor: pointer;
}
.geBtn:hover {
	-webkit-box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	-moz-box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	border: 1px solid #c6c6c6;
	background-color: #f8f8f8;
	background-image: linear-gradient(#f8f8f8 0px,#f1f1f1 100%);
	color: #111;
}
.geBtn:disabled {
	opacity: .5;
}
.geBtnUp {
	background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECAGZmZtXV1f///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJERjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJFMDg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkREODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRFODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjwiQBhGYglCKhXFLBQA7);
	_background-image: url(up.gif);
	background-position: center center;
	background-repeat: no-repeat;
}
.geBtnUp:active {
	background-color: #4d90fe;
	background-image: linear-gradient(#4d90fe 0px,#357ae8 100%);
}
.geBtnDown {
	background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECANXV1WZmZv///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJEQjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJEQzg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkQ5ODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRBODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjxLLewEiCAnOZBzeBQA7);
	_background-image: url(down.gif);
	background-position: center center;
	background-repeat: no-repeat;
}
.geBtnDown:active {
	background-color: #4d90fe;
	background-image: linear-gradient(#4d90fe 0px,#357ae8 100%);
}
.geColorBtn {
	background-color: #f5f5f5;
	background-image: linear-gradient(#f5f5f5 0px,#e1e1e1 100%);
	border-radius: 4px;
	border: 1px solid rgba(0,0,0,0.5);
	color: #333;
	cursor: default;
	margin: 0px;
	outline: 0;
	padding: 0px;
	cursor: pointer;		
}
.geColorBtn:hover {
	-webkit-box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	-moz-box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	box-shadow: 0px 1px 1px rgba(0,0,0,0.1);
	border: 1px solid rgba(0,0,0,0.7);
}
.geColorBtn:active {
	background-color: #4d90fe;
	background-image: linear-gradient(#4d90fe 0px,#357ae8 100%);
	border: 1px solid #2f5bb7;
	color: #fff;
}
.geColorBtn:disabled {
	opacity: .5;
}
.gePrimaryBtn {
	background-color: #4d90fe;
	background-image: linear-gradient(#4d90fe 0px,#4787ed 100%);
	border: 1px solid #3079ed;
	color: #fff;
}
.gePrimaryBtn:hover {
	background-color: #357ae8;
	background-image: linear-gradient(#4d90fe 0px,#357ae8 100%);
	border: 1px solid #2f5bb7;
	color: #fff;
}
.gePrimaryBtn:disabled {
	opacity: .5;
}
.geAlertLink {
	color:#843534;
	font-weight:700;
	text-decoration:none;
}
.geMenubarContainer {
	background-color:#ffffff;
}
.geMenubar {
	padding:0px 2px 0px 2px;
	vertical-align:middle;
}
.geMenubarContainer .geItem, .geToolbar .geItem {
	padding:6px 8px 6px 8px;
	cursor:default;
}
.geMenubarContainer .geItem:hover, .geToolbarContainer .geItem:hover {
	background:#eeeeee;
}
.mxDisabled:hover {
	background:inherit !important;
}
.geMenubar a.geStatus {
	color:#b3b3b3;
	padding-left:6px;
	display:inline-block;
	cursor:default !important;
}
.geMenubar a.geStatus:hover {
	background:transparent;
}
.geMenubarMenu {
	border:1px solid #d5d5d5 !important;
}
.geToolbarContainer {
	background:whiteSmoke;
	border-bottom:1px solid #e0e0e0;
}
.geSidebarContainer .geToolbarContainer {
	background:transparent;
	border-bottom:none;
}
.geSidebarContainer button {
	text-overflow:ellipsis;
	overflow:hidden;
}
.geToolbar {
	padding:1px 0px 0px 6px;
	border-top:1px solid #e0e0e0;
	-webkit-box-shadow: inset 0 1px 0 0 #fff;
	-moz-box-shadow: inset 0 1px 0 0 #fff;
	box-shadow: inset 0 1px 0 0 #fff;
}
.geToolbarContainer .geSeparator {
	float:left;
	width:1px;
	height:34px;
	background:#e5e5e5;
	margin-left:6px;
	margin-right:6px;
	margin-top:-2px;
}
.geToolbarContainer .geButton {
	float:left;
	width:20px;
	height:20px;
	padding:0px 2px 4px 2px;
	margin:2px;
	border:1px solid transparent;
	cursor:pointer;
	opacity:0.6;
	filter:alpha(opacity=60);
}
.geToolbarContainer .geButton:hover {
	border:1px solid gray;
	border-radius:2px;
	opacity:1;
	filter:none !important;
}
.geToolbarContainer .geButton:active {
	border:1px solid black;
}
div.mxWindow .geButton {
	margin: -1px 2px 2px 2px;
	padding: 1px 2px 2px 1px;
}
.geToolbarContainer .geLabel {
	float:left;
	margin:2px;
	cursor:pointer;
	padding:3px 5px 3px 5px;
	border:1px solid transparent;
	opacity:0.6;
	filter:alpha(opacity=60);
}
.geToolbarContainer .geLabel:hover {
	border:1px solid gray;
	border-radius:2px;
	opacity:0.9;
	filter:alpha(opacity=90) !important;
}
.geToolbarContainer .geLabel:active {
	border:1px solid black;
	opacity:1;
	filter:none !important;
}
.geToolbarContainer .mxDisabled:hover {
	border:1px solid transparent !important;
	opacity:0.2 !important;
	filter:alpha(opacity=20) !important;
}
.geToolbarMenu {
	border:3px solid #e0e0e0 !important;
	-webkit-box-shadow:none !important;
	-moz-box-shadow:none !important;
	box-shadow:none !important;
	filter:none !important;
}
.geDiagramBackdrop {
	background-color: #ebebeb;
	border-style: solid;
	border-color: #e5e5e5;
	border-width: 1px 0px 0px 1px;
}
.geSidebarContainer {
	background:#ffffff;
	overflow:hidden;
	position:absolute;
	border-top:1px solid #e5e5e5;
	overflow:auto;
}
.geSidebar {
	background:whiteSmoke;
	border-bottom:1px solid #e5e5e5;
	padding:5px;
	_padding:1px;
	padding-bottom:12px;
	overflow:hidden;
}
.geSidebarContainer .geTitle {
	display:block;
	font-size:9pt;
	border-bottom:1px solid #e5e5e5;
	font-weight:normal;
	padding:6px 0px 6px 14px;
	margin:0px;
	cursor:default;
	background:#eeeeee;
	white-space:nowrap;
	overflow:hidden;
	text-overflow:ellipsis;
	line-height:1.4em;
}
.geSidebarContainer .geTitle:hover {
	background:#e5e5e5;
}
.geTitle img {
	opacity:0.5;
	_filter:alpha(opacity=50);	
}
.geTitle img:hover {
	opacity:1;
	_filter:alpha(opacity=100);	
}
.geSidebar .geItem {
	display:inline-block;
	background-repeat:no-repeat;
	background-position:50% 50%;
	border:1px solid transparent;
	border-radius:2px;
	cursor: move;
}
.geSidebar .geItem:hover {
	border:1px solid gray !important;
}
.geItem {
	vertical-align: top;
	display: inline-block;
}
.geSidebarTooltip {
	position:absolute;
	background:white;
	overflow:hidden;
	border:1px solid gray;
	border-radius:8px;
	-webkit-box-shadow:0px 0px 2px 2px #d5d5d5;
	-moz-box-shadow:0px 0px 2px 2px #d5d5d5;
	box-shadow:0px 0px 2px 2px #d5d5d5;
	_filter:progid:DXImageTransform.Microsoft.DropShadow(OffX=2, OffY=2, Color='#d5d5d5', Positive='true');
}
.geFooterContainer {
	background:#e5e5e5;
	border-top:1px solid #c0c0c0;
}
.geFooterContainer a {
	font-size:14px;
	color:#235695;
	font-weight:bold;
	text-decoration:none;
}
.geFooterContainer table {
	border-collapse:collapse;
	margin:0 auto;
}
.geFooterContainer td {
	border-left:1px solid #c0c0c0;
	border-right:1px solid #c0c0c0;
}
.geFooterContainer td:hover {
	background-color: #b3b3b3;
}
.geFooterContainer a {
	display:block;
	box-sizing:border-box;
	width:100%;
	height:100%;
	padding:11px 42px 11px 42px;
	white-space:nowrap;
}
.geHsplit {
	cursor:col-resize;
	background-color:#e5e5e5;
	background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHBAMAAADdS/HjAAAAGFBMVEUzMzP///9tbW1QUFCKiopBQUF8fHxfX1/IXlmXAAAAHUlEQVQImWMQEGAQFWUQFmYQF2cQEmIQE2MQEQEACy4BF67hpEwAAAAASUVORK5CYII=);
	_background-image:url('thumb_vertical.png');
	background-repeat:no-repeat;
	background-position:center center;
}
.geVsplit {
	font-size:1pt;
	cursor:row-resize;
	background-color:#e5e5e5;
	background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAEBAMAAACw6DhOAAAAGFBMVEUzMzP///9tbW1QUFCKiopBQUF8fHxfX1/IXlmXAAAAFElEQVQImWNgNVdzYBAUFBRggLMAEzYBy29kEPgAAAAASUVORK5CYII=);
	_background-image:url('thumb_horz.png');
	background-repeat:no-repeat;
	background-position:center center;
}
.geHsplit:hover, .geVsplit:hover {
	background-color:#d5d5d5;
}
.geDialog {
	position:absolute;
	background:white;
	overflow:hidden;
	padding:30px;
	border:1px solid #acacac;
	-webkit-box-shadow:0px 0px 2px 2px #d5d5d5;
	-moz-box-shadow:0px 0px 2px 2px #d5d5d5;
	box-shadow:0px 0px 2px 2px #d5d5d5;
	_filter:progid:DXImageTransform.Microsoft.DropShadow(OffX=2, OffY=2, Color='#d5d5d5', Positive='true');
	z-index: 2;
}
.geDialogClose {
	position:absolute;
	width:9px;
	height:9px;
	opacity:0.5;
	cursor:pointer;
	_filter:alpha(opacity=50);
}
.geDialogClose:hover {
	opacity:1;
}
.geDialogTitle {
	box-sizing:border-box;
	white-space:nowrap;
	background:rgb(229, 229, 229);
	border-bottom:1px solid rgb(192, 192, 192);
	font-size:15px;
	font-weight:bold;
	text-align:center;
	color:rgb(35, 86, 149);
}
.geDialogFooter {
	background:whiteSmoke;
	white-space:nowrap;
	text-align:right;
	box-sizing:border-box;
	border-top:1px solid #e5e5e5;
	color:darkGray;
}
.geSprite {
	background:url('data:image/png;base64,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') no-repeat top left;
	_background:url('sprites.png') no-repeat top left;
	width:21px;
	height:21px;
}
.geBaseButton {
	padding:10px;
	border-radius:6px;
	border:1px solid #c0c0c0;
	cursor:pointer;
	background-color:#ececec;
	background-image:linear-gradient(#ececec 0%, #fcfcfc 100%);
}
.geBaseButton:hover {
	background:#ececec;
}
.geBigButton {
	color:#ffffff;
	border: none;
	padding:10px;
	font-size:14pt;
	white-space: nowrap;
	border-radius:6px;
	text-shadow: rgb(41, 89, 137) 0px 1px 0px;
	background-color:#428bca;
	background-image:linear-gradient(rgb(70, 135, 206) 0px, rgb(48, 104, 162) 100%);
	-webkit-box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
	-moz-box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
	box-shadow: rgba(255, 255, 255, 0.0980392) 0px 1px 0px 0px inset, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px;
}
.geBigButton:hover {
	background-color:#2d6ca2;
	background-image: linear-gradient(rgb(90, 148, 211) 0px, rgb(54, 115, 181) 100%);
}
.geBigButton:active {
	background-color: rgb(54, 115, 181);
	background-image: none;
}
@media print {
	div.geNoPrint { display: none !important; }
}
.geSprite-actualsize { background-position: 0 0; }
.geSprite-bold { background-position: 0 -46px; }
.geSprite-bottom { background-position: 0 -92px; }
.geSprite-center { background-position: 0 -138px; }
.geSprite-delete { background-position: 0 -184px; }
.geSprite-fillcolor { background-position: 0 -229px; }
.geSprite-fit { background-position: 0 -277px; }
.geSprite-fontcolor { background-position: 0 -322px; }
.geSprite-gradientcolor { background-position: 0 -368px; }
.geSprite-image { background-position: 0 -414px; }
.geSprite-italic { background-position: 0 -460px; }
.geSprite-left { background-position: 0 -505px; }
.geSprite-middle { background-position: 0 -552px; }
.geSprite-print { background-position: 0 -598px; }
.geSprite-redo { background-position: 0 -644px; }
.geSprite-right { background-position: 0 -689px; }
.geSprite-shadow { background-position: 0 -735px; }
.geSprite-strokecolor { background-position: 0 -782px; }
.geSprite-top { background-position: 0 -828px; }
.geSprite-underline { background-position: 0 -874px; }
.geSprite-undo { background-position: 0 -920px; }
.geSprite-zoomin { background-position: 0 -966px; }
.geSprite-zoomout { background-position: 0 -1012px; }
.geSprite-arrow { background-position: 0 -1059px; }
.geSprite-linkedge { background-position: 0 -1105px; }
.geSprite-straight { background-position: 0 -1150px; }
.geSprite-entity { background-position: 0 -1196px; }
.geSprite-orthogonal { background-position: 0 -1242px; }
.geSprite-curved { background-position: 0 -1288px; }
.geSprite-noarrow { background-position: 0 -1334px; }
.geSprite-endclassic { background-position: 0 -1380px; }
.geSprite-endopen { background-position: 0 -1426px; }
.geSprite-endblock { background-position: 0 -1472px; }
.geSprite-endoval { background-position: 0 -1518px; }
.geSprite-enddiamond { background-position: 0 -1564px; }
.geSprite-endthindiamond { background-position: 0 -1610px; }
.geSprite-endclassictrans { background-position: 0 -1656px; }
.geSprite-endblocktrans { background-position: 0 -1702px; }
.geSprite-endovaltrans { background-position: 0 -1748px; }
.geSprite-enddiamondtrans { background-position: 0 -1794px; }
.geSprite-endthindiamondtrans { background-position: 0 -1840px; }
.geSprite-startclassic { background-position: 0 -1886px; }
.geSprite-startopen { background-position: 0 -1932px; }
.geSprite-startblock { background-position: 0 -1978px; }
.geSprite-startoval { background-position: 0 -2024px; }
.geSprite-startdiamond { background-position: 0 -2070px; }
.geSprite-startthindiamond { background-position: 0 -2116px; }
.geSprite-startclassictrans { background-position: 0 -2162px; }
.geSprite-startblocktrans { background-position: 0 -2208px; }
.geSprite-startovaltrans { background-position: 0 -2254px; }
.geSprite-startdiamondtrans { background-position: 0 -2300px; }
.geSprite-startthindiamondtrans { background-position: 0 -2346px; }
.geSprite-globe { background-position: 0 -2392px; }
.geSprite-orderedlist { background-position: 0 -2438px; }
.geSprite-unorderedlist { background-position: 0 -2484px; }
.geSprite-horizontalrule { background-position: 0 -2530px; }
.geSprite-link { background-position: 0 -2576px; }
.geSprite-indent { background-position: 0 -2622px; }
.geSprite-outdent { background-position: 0 -2668px; }
.geSprite-code { background-position: 0 -2714px; }
.geSprite-fontbackground { background-position: 0 -2760px; }
.geSprite-removeformat { background-position: 0 -2806px; }
.geSprite-superscript { background-position: 0 -2852px; }
.geSprite-subscript { background-position: 0 -2898px; }
.geSprite-table { background-position: 0 -2944px; }
.geSprite-deletecolumn { background-position: 0 -2990px; }
.geSprite-deleterow { background-position: 0 -3036px; }
.geSprite-insertcolumnafter { background-position: 0 -3082px; }
.geSprite-insertcolumnbefore { background-position: 0 -3128px; }
.geSprite-insertrowafter { background-position: 0 -3174px; }
.geSprite-insertrowbefore { background-position: 0 -3220px; }
.geSprite-grid { background-position: 0 -3272px; }
.geSprite-guides { background-position: 0 -3324px; }
.geSprite-dots { background-position: 0 -3370px; }
.geSprite-alignleft { background-position: 0 -3416px; }
.geSprite-alignright { background-position: 0 -3462px; }
.geSprite-aligncenter { background-position: 0 -3508px; }
.geSprite-aligntop { background-position: 0 -3554px; }
.geSprite-alignbottom { background-position: 0 -3600px; }
.geSprite-alignmiddle { background-position: 0 -3646px; }
.geSprite-justifyfull { background-position: 0 -3692px; }
.geSprite-formatpanel { background-position: 0 -3738px; }
.geSprite-connection { background-position: 0 -3784px; }
.geSprite-vertical { background-position: 0 -3830px; }
.geSprite-simplearrow { background-position: 0 -3876px; }
.geSprite-plus { background-position: 0 -3922px; }
.geSprite-rounded { background-position: 0 -3968px; }
.geSprite-toback { background-position: 0 -4014px; }
.geSprite-tofront { background-position: 0 -4060px; }
.geSprite-duplicate { background-position: 0 -4106px; }
.geSprite-insert { background-position: 0 -4152px; }
.geSprite-endblockthin { background-position: 0 -4201px; }
.geSprite-endblockthintrans { background-position: 0 -4247px; }
.geSprite-enderone { background-position: 0 -4293px; }
.geSprite-enderonetoone { background-position: 0 -4339px; }
.geSprite-enderonetomany { background-position: 0 -4385px; }
.geSprite-endermany { background-position: 0 -4431px; }
.geSprite-enderoneopt { background-position: 0 -4477px; }
.geSprite-endermanyopt { background-position: 0 -4523px; }
.geSprite-endclassicthin { background-position: 0 -4938px; }
.geSprite-endclassicthintrans { background-position: 0 -4984px; }
.geSprite-enddash { background-position: 0 -5029px; }
.geSprite-endcircleplus { background-position: 0 -5075px; }
.geSprite-endcircle { background-position: 0 -5121px; }
.geSprite-endasync { background-position: 0 -5167px; }
.geSprite-endasynctrans { background-position: 0 -5213px; }
.geSprite-startblockthin { background-position: 0 -4569px; }
.geSprite-startblockthintrans { background-position: 0 -4615px; }
.geSprite-starterone { background-position: 0 -4661px; }
.geSprite-starteronetoone { background-position: 0 -4707px; }
.geSprite-starteronetomany { background-position: 0 -4753px; }
.geSprite-startermany { background-position: 0 -4799px; }
.geSprite-starteroneopt { background-position: 0 -4845px; }
.geSprite-startermanyopt { background-position: 0 -4891px; }
.geSprite-startclassicthin { background-position: 0 -5259px; }
.geSprite-startclassicthintrans { background-position: 0 -5305px; }
.geSprite-startdash { background-position: 0 -5351px; }
.geSprite-startcircleplus { background-position: 0 -5397px; }
.geSprite-startcircle { background-position: 0 -5443px; }
.geSprite-startasync { background-position: 0 -5489px; }
.geSprite-startasynctrans { background-position: 0 -5535px; }
.geSprite-startcross { background-position: 0 -5581px; }
.geSprite-startopenthin { background-position: 0 -5627px; }
.geSprite-startopenasync { background-position: 0 -5673px; }
.geSprite-endcross { background-position: 0 -5719px; }
.geSprite-endopenthin { background-position: 0 -5765px; }
.geSprite-endopenasync { background-position: 0 -5811px; }
.geSprite-verticalelbow { background-position: 0 -5857px; }
.geSprite-horizontalelbow { background-position: 0 -5903px; }
.geSprite-horizontalisometric { background-position: 0 -5949px; }
.geSprite-verticalisometric { background-position: 0 -5995px; }
html div.mxRubberband {
	border-color:#0000DD;
	background:#99ccff;
}
td.mxPopupMenuIcon div {
	width:16px;
	height:16px;
}
html div.mxPopupMenu {
	-webkit-box-shadow:2px 2px 3px #d5d5d5;
	-moz-box-shadow:2px 2px 3px #d5d5d5;
	box-shadow:2px 2px 3px #d5d5d5;
	_filter:progid:DXImageTransform.Microsoft.DropShadow(OffX=2, OffY=2, Color='#d0d0d0', Positive='true');
	background:white;
	position:absolute;
	border:3px solid #e7e7e7;
	padding:3px;
}
html table.mxPopupMenu {
	border-collapse:collapse;
	margin:0px;
}
html td.mxPopupMenuItem {
	padding:7px 30px 7px 30px;
	font-family:Helvetica Neue,Helvetica,Arial Unicode MS,Arial;
	font-size:10pt;
}
html td.mxPopupMenuIcon {
	background-color:white;
	padding:0px;
}
td.mxPopupMenuIcon .geIcon {
	padding:2px;
	padding-bottom:4px;
	margin:2px;
	border:1px solid transparent;
	opacity:0.5;
	_width:26px;
	_height:26px;
}
td.mxPopupMenuIcon .geIcon:hover {
	border:1px solid gray;
	border-radius:2px;
	opacity:1;
}
html tr.mxPopupMenuItemHover {
	background-color: #eeeeee;
	color: black;
}
table.mxPopupMenu hr {
	color:#cccccc;
	background-color:#cccccc;
	border:none;
	height:1px;
}
table.mxPopupMenu tr {
	font-size:4pt;
}
html td.mxWindowTitle {
	font-family:Helvetica Neue,Helvetica,Arial Unicode MS,Arial;
 	text-align:left;
 	font-size:12px;
 	color:rgb(112, 112, 112);
 	padding:4px;
}