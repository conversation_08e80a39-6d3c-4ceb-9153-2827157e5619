/**
 *
 */
var actMonitorAlarmPanel;
var actMonitorAlarmStore;
var actMonitorAlarmFreshForm;
var actMonitorAlarmRefreshObj;
var actMonitorActListStroe;
var actMonitorAutoFreshTime;
var prjNameComBox = v_filterPrjName;
var treeStore;
var mainItems;
Ext.onReady(function () {
//	$("body").unbind();
    destroyRubbish();// 销毁其他页面的监听事件

    var selModel;
    var pageBar;

    var columns = [{
        text: '活动状态',
        sortable: true,
        dataIndex: 'actState',
        width: 80,
        renderer: actMonitorStateImage
    }, {
        text: '快照',
        sortable: true,
        dataIndex: 'pic',
        width: 60
    }, {
        text: 'topo依赖',
        sortable: true,
        dataIndex: 'topoPic',
        width: 60
    }, {
        text: '工程名',
        sortable: true,
        dataIndex: 'prjName',
        width: 150
    }, {
        text: '工作流ID',
        sortable: true,
        dataIndex: 'flowID',
        width: 150
    }, {
        text: '活动ID',
        sortable: true,
        hidden: true,
        dataIndex: 'actIID',
        width: 150
    }, {
        text: '工作流名',
        sortable: true,
        dataIndex: 'flowName',
        width: 150
    }, {
        text: '实例名',
        sortable: true,
        dataIndex: 'flowInsName',
        width: 150
    }, {
        text: '活动名称',
        sortable: true,
        dataIndex: 'actName',
        width: 150,
        renderer: actOpen
    }, {
        text: 'AgentIP',
        sortable: true,
        dataIndex: 'agentIp',
        flex: 1
    },{
        text: '活动描述',
        sortable: true,
        dataIndex: 'actDes',
        flex: 1
    }, {
        text: '开始时间',
        sortable: true,
        dataIndex: 'startTime',
        width: 145
    }, {
        text: '昨日活动执行开始时间',
        width: 170,
        align: 'center',
        hidden: true,
        dataIndex: 'yesterdayBeginNextTime'
    }, {
        text: '今日活动预计开始执行时间',
        width: 190,
        align: 'center',
        hidden: true,
        dataIndex: 'toDayPlanBeginTime'
    }, {
        text: '今日活动执行开始时间',
        width: 170,
        align: 'center',
        hidden: true,
        dataIndex: 'beginNextTime'
    }, {
        text: '今日活动预计执行结束时间',
        width: 190,
        align: 'center',
        hidden: true,
        dataIndex: 'toDayPlanEndTime'
    }, {
        text: '经办人',
        sortable: true,
        dataIndex: 'startUser',
        width: 100
    }, {
        text: '工作流暂停状态',
        sortable: true,
        hidden: true,
        dataIndex: 'isFlowPause',
        width: 100
    }];
    Ext.define('actModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'actruntimeid',
            type: 'long'
        }, {
            name: 'iid',
            type: 'long'
        }, {
            name: 'pic',
            type: 'string'
        }, {
            name: 'actState',
            type: 'string'
        }, {
            name: 'flowInsName',
            type: 'string'
        }, {
            name: 'taskID',
            type: 'string'
        }, {
            name: 'errorTaskId',
            type: 'long'
        }, {
            name: 'flowID',
            type: 'long'
        }, {
            name: 'actIID',
            type: 'long'
        }, {
            name: 'reqId',
            type: 'string'
        }, {
            name: 'actName',
            type: 'string'
        }, {
            name: 'agentIp',
            type: 'string'
        }, {
            name: 'actDefName',
            type: 'string'
        }, {
            name: 'startTime',
            type: 'string'
        }, {
            name: 'actDes',
            type: 'string'
        }, {
            name: 'startUser',
            type: 'string'
        }, {
            name: 'prjName',
            type: 'string'
        }, {
            name: 'flowName',
            type: 'string'
        }, {
            name: 'isFlowPause',
            type: 'boolean'
        }, {
            name: 'showColor',
            type: 'string'
        }, {
            name: 'beginNextTime',
            type: 'string'
        }, {
            name: 'yesterdayBeginNextTime',
            type: 'string'
        }, {
            name: 'toDayPlanBeginTime',
            type: 'string'
        }, {
            name: 'toDayPlanEndTime',
            type: 'string'
        }, {
            name: 'topoPic',
            type: 'string'
        }]
    });

    /**
     * 查询条件
     */
    var sysName = "";
    var iprjName = "";
    var projectName = "";
    var tagTextField = "";
    Ext.define('projectModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iprjName', // 系统名称
            type: 'string'
        }, {
            name: 'iprjId', // 系统ID
            type: 'long'
        }, {
            name: 'iupperId',
            type: 'long'
        }]
    });
    /**
     * 新增查询条件
     */

    var groName = "";
    Ext.define('groModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'groName', // 组名称
            type: 'string'
        }]
    });
    // 工程名称下拉列表stor
    var getPrjNameStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'projectModel',
        proxy: {
            type: 'ajax',
            url: 'actmonitor/getPrjName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    // a.gro下拉列表stor
    var getRroStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'groModel',
        proxy: {
            type: 'ajax',
            url: 'actmonitor/getGroName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    // 所属系统下拉框数据源Model
    Ext.define('systemModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'prjId',
                    type: 'string'
                }, {
                    name: 'sysName',
                    type: 'string'
                }
            ]
        });
    // 所属系统下拉框数据源
    var cstore_isystem = Ext.create('Ext.data.Store',
        {
            autoLoad: true,
            autoDestroy: true,
            model: 'systemModel',
            proxy:
                {
                    type: 'ajax',
                    url: 'jzActMonitorQuerySystem.do',
                    reader:
                        {
                            type: 'json',
                            root: 'dataList'
                        }
                }
        });


    function selectFunction() {
        submitIns();
        filterTree(); // 使用filterTree函数刷新树状结构，支持查询条件
        if (Ext.isIE) {
            CollectGarbage();
        }
    }
    function selectFunction1() {
        submitIns1();
        if (Ext.isIE) {
            CollectGarbage();
        }
    }

    function submitIns1() {

        groName = Ext.getCmp("groComBox_id").getRawValue();
        pageBar.moveFirst();
        actMonitorActListStroe.load({
                    params: {
                        page: 1,
                        groName: groName
                    }
                });
    }

    function submitIns() {
        //iprjName = Ext.getCmp("prjNameComBox_id").getValue();
        projectName = Ext.getCmp("prjNameComBox_id").getRawValue();
        var stateValue = Ext.getCmp("stateComboBox_id").getValue();
        // pageBar.moveFirst();
        if(ycQueryGroNameSwitch){
            groName = Ext.getCmp("groComBox_id").getRawValue();
            actMonitorActListStroe.load({
                params: {
                    page: 1,
                    groName: groName,
                    prjName: projectName
                }
            });
        } else if (jobSchedulingQuerySystemSwitch) {
            sysName = Ext.getCmp('actMonitorSysName').getRawValue();
            if (projectName != '') {
                actMonitorActListStroe.load({
                    params: {
                        page: 1,
                        sysName: sysName,
                        prjName: projectName
                    }
                });
            }
        } else {
            if (projectName != '' || stateValue != '') {
                actMonitorActListStroe.load({
                    params: {
                        page: 1,
                        prjName: projectName,
                        state: stateValue
                    }
                });
            }else {
                actMonitorActListStroe.load({
                    params: {
                        page: 1
                    }
                });
            }
        }

    }

    Ext.define('Aoms.view.Condition', {
        extend: 'Ext.panel.Panel',
        alias: 'widget.condition',

        requires: ['Ext.form.Panel', 'Ext.form.field.ComboBox',
            'Ext.form.field.Date', 'Ext.button.Button'],
        border: false,
        //    height: 37,
        height: 45,
        layout: 'column',
        initComponent: function () {
            var me = this;
            var stateStore = Ext.create('Ext.data.Store', {
                fields: ['id', 'name'],
                data: [{
                    "id": "Ready",
                    "name": "就绪"
                }, {
                    "id": "Running",
                    "name": "运行"
                }, {
                    "id": "Fail",
                    "name": "失败"
                }, {
                    "id": "ManualRunning",
                    "name": "异常处理中"
                }]
            });

            var insNameBox = Ext.widget('combobox', {
                id: 'insNameBox',
                padding: '8 8 8 8',
                width: 600,
                emptyText: '实例名称',
                labelWidth: 0,
                hidden: true
            });
            var actTypeBox = Ext.widget('combobox', {
                padding: '8 8 8 8',
                width: 100,
                emptyText: '活动状态',
                labelWidth: 0,
                store: stateStore,
                displayField: 'name',
                hidden: true,
                valueField: 'id'
            });
            var freshBtn = Ext.create("Ext.Button", {
                id: 'freshBtn',
                text: '刷新',
                margin: '0 0 0 10',
//				baseCls:'Common_Btn',
                handler: me.funFresh
            });
            var freshTxt = Ext.widget('textfield', {
                labelWidth: 89,
                width: 140,
                labelAlign: 'right',
                fieldLabel: '刷新时间(秒)',
                value: '60'
            });

            var batchTryAgain_btn = Ext.create('Ext.Button', {
                text: '批量重试',
//				iconCls : 'monitor_execute2',
                baseCls: 'Common_Btn',
                margin: '0 0 0 10',
                handler: function () {
                    if (jobscDoubleCheck == 'true') {
                        //flowDoubleCheck(1);
                        doubleCheckBatch('1');
                    } else {
                        batchOperationTask('1');
                    }
                }
            });

            var batchIgnore_btn = Ext.create('Ext.Button', {
                text: '批量略过',
//				iconCls : 'monitor_skip2',
                baseCls: 'Common_Btn',
                margin: '0 0 0 10',
                handler: function () {
                    if (jobscDoubleCheck == 'true') {
                        //flowDoubleCheck(2);
                        doubleCheckBatch('2');
                    } else {
                        batchOperationTask('2');
                    }
                }
            });

            var actTypeStore = Ext.create('Ext.data.Store', {
                fields: ['id', 'name'],
                data: [
                    {id: 'all', name: '显示所有'},
                    {id: 'schedule', name: '只显示计划'},
                    {id: 'task', name: '只显示任务'}
                ]
            });

            var actTypeCombo = Ext.create('Ext.form.field.ComboBox', {
                id: 'actTypeCombo',
                padding: '0 0 0 5',
                width: 200,
                //fieldLabel: '  活动类型',
                labelWidth: 60,
                store: actTypeStore,
                displayField: 'name',
                valueField: 'id',
                value: 'all',  // 默认选择"显示所有"
                queryMode: 'local',
                editable: false,
                listeners: {
                    // 添加select事件监听器
                    select: function (combo, records) {
                        // 获取两个下拉框的值
                        var actTypeValue = combo.getValue();
                        var prjNameValue = Ext.getCmp('prjNameComBox_id').getValue();

                        // 调用控制器方法并传递参数
                        filterActList(actTypeValue, prjNameValue);
                    }
                }
            });

            // 新增的控制器方法
            function filterActList(actType, prjName) {
                // 获取工程名称
                var projectName = prjName || Ext.getCmp('prjNameComBox_id').getRawValue();

                // 设置store参数
                actMonitorActListStroe.proxy.extraParams = {
                    stateValue: actType,
                    prjName: projectName
                };

                // 重新加载数据
                actMonitorActListStroe.loadPage(1);
            }
            // 所属系统下拉框
            var combobox_isystem = Ext.create('Ext.form.field.ComboBox',
                {
                    width: 400,
                    labelWidth: 70,
                    padding: '5 5 5 5',
                    fieldLabel: '所属系统',
                    labelAlign: 'right',
                    displayField: 'sysName',
                    valueField: 'sysName',
                    id: 'actMonitorSysName',
                    //emptyText : '--请输选择所属系统名称--',
                    editable: true,
                    typeAhead: true,
                    hidden: true,
                    store: cstore_isystem,
                    queryMode: 'local',
                    listeners: {
                        select: function () {
                            Ext.getCmp("prjNameComBox_id").clearValue();
                            var sysName = this.value;
                            // 参数信息列表grid重新加载
                            getPrjNameStore.load({
                                params: {
                                    sysName: sysName
                                }
                            });
                        },
                        beforequery: function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var value = e.query;
                                combo.store.filterBy(function (record, id) {
                                    var text = record.get(combo.displayField);
                                    return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                                });
                                combo.expand();
                                return false;
                            }
                        }
                    }
                });

            //  var projectTxt = Ext.widget('combo',{padding: '5 5 5 5',labelWidth : 90,width:240,labelAlign : 'right',fieldLabel: '工程名称',value: '',store : getPrjNameStore});
            var prjNameComBox = Ext.create('Ext.form.field.Text', {
                id: 'prjNameComBox_id',
                name: 'form_prjName',
                fieldLabel: '查询条件',
                emptyText: '--请输入--',
                width: 250,
                labelWidth: 65,
                value: v_filterPrjName,
                listeners: {
                    specialkey: function (field, e) {
                        if (e.getKey() == e.ENTER) {
                            selectFunction();
                        }
                    }
                }
            });


            // 将原有的newTextField替换为状态下拉框
            var stateStore = Ext.create('Ext.data.Store', {
                fields: ['id', 'name'],
                data: [
                    {"id": "", "name": "全部"},
                    {"id": "失败", "name": "失败"},
                    {"id": "就绪", "name": "就绪"},
                    {"id": "运行", "name": "运行"},
                    {"id": "异常处理中", "name": "异常处理中"},
                    {"id": "连接失败处理中", "name": "连接失败处理中"},
                    {"id": "运行连接失败", "name": "运行连接失败"},
                    {"id": "超时", "name": "超时"},
                    {"id": "排队", "name": "排队"},
                    {"id": "暂停", "name": "暂停"},
                    {"id": "挂起", "name": "挂起"},
                    {"id": "失败:业务异常", "name": "失败:业务异常"}
                ]
            });

            var stateComboBox = Ext.create('Ext.form.field.ComboBox', {
                fieldLabel: '活动状态',
                labelWidth: 65,
                width: 250,
                margin: '0 10 0 10',
                id: 'stateComboBox_id',
                store: stateStore,
                queryMode: 'local',
                displayField: 'name',
                valueField: 'id',
                editable: false,
                emptyText: '--请选择状态--'
            });

            // var newTextField = Ext.create('Ext.form.field.Text', {
            //     fieldLabel: '标签',      // 设置标签文字
            //     labelWidth: 40,          // 与prjNameComBox的标签宽度保持一致
            //     emptyText: '--请输入标签--',
            //     width: 200,               // 设置宽度
            //     margin: '0 10 0 10',
            //     id: 'newTextField_id'     // 设置ID便于后续引用
            // });

            var selectButton = Ext.create("Ext.Button", {
                textAlign: 'center',
//				cls:'Common_Btn',
                margin: '0 0 0 10',
                text: "查询",
                handler: selectFunction
            });

            var groComBox = Ext.create('Ext.form.field.ComboBox', {
                id: 'groComBox_id',
                name: 'form_groName',
                fieldLabel: '拓扑组名称',
                emptyText: '--请选择拓扑组名称--',
                displayField: 'groName',
                valueField: 'groName',
                margin: '0 10 0 10',
                width: 400,
                queryMode: 'local',
                labelWidth: 90,
                editable: true,
                typeAhead: true,
                store: getRroStore,
                listeners: {

                    specialkey: function (field, e) {
                        if (e.getKey() == e.ENTER) {
                            selectFunction();
                        }
                    }
                }
            });


            if(ycQueryGroNameSwitch){
            Ext.applyIf(me, {

                    items: [insNameBox, actTypeBox, combobox_isystem, prjNameComBox,stateComboBox,groComBox,selectButton,
                        freshTxt, freshBtn,batchTryAgain_btn,batchIgnore_btn,actTypeCombo]

            });
            }else {
                Ext.applyIf(me, {

                    items: [insNameBox, actTypeBox, combobox_isystem, prjNameComBox,stateComboBox,selectButton,
                        freshTxt, freshBtn,batchTryAgain_btn,batchIgnore_btn,actTypeCombo]

                });

            }
            me.callParent(arguments);
            /**锦州展示所属系统**/
            if (jobSchedulingQuerySystemSwitch) {
                combobox_isystem.show();
            }
        }
    });

    var condition = Ext.create('Aoms.view.Condition', {
        region: 'north',
        funFresh: function () {
            Ext.applyIf(actMonitorActListStroe.proxy.extraParams, {
                insName: condition.items.items[0].value,
                stateValue: condition.items.items[1].value,
                prjName:Ext.getCmp("prjNameComBox_id").getRawValue(),
                state: Ext.getCmp("stateComboBox_id").getValue()
            });

            console.log('状态：' + Ext.getCmp("stateComboBox_id").getValue());

            if (actMonitorAlarmRefreshObj) {
                clearInterval(actMonitorAlarmRefreshObj);
            }
            if(ycQueryGroNameSwitch){
                var refreshTime = condition.items.items[7].value;
            }else{
                var refreshTime = condition.items.items[6].value;
            }
            // aoms.log(refreshTime);
            actMonitorAlarmRefreshObj = setInterval(freshGrid, parseInt(refreshTime) * 1000);
            actMonitorActListStroe.load();
            treeStore.load(); // 刷新树状结构
        }
    });
    actMonitorActListStroe = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'actModel',
        pageSize: 100,
        proxy: {
            type: 'ajax',
            url: 'actList.do',
            reader: {
                type: 'json',
                root: 'datalist',
                totalProperty: 'total'
            }
        }
    });
    // 分页工具
    pageBar = Ext.create('Ext.PagingToolbar', {
        store: actMonitorActListStroe,
        dock: 'bottom',
        displayInfo: true,
        //pageSize : 30,
        afterPageText: ' 页 共 {0} 页',
        beforePageText: '第 ',
        firstText: '第一页 ',
        prevText: '前一页',
        nextText: '下一页',
        lastText: '最后一页',
        refreshText: '刷新',
        displayMsg: '第{0}条 到 {1} 条数据  共找到{2}条记录',
        emptyMsg: '找不到任何记录',
        baseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar'
    });

    actMonitorActListStroe.on('beforeload', function (store, options) {
        var projectName = Ext.getCmp("prjNameComBox_id").getRawValue();
        var systemName = Ext.getCmp('actMonitorSysName').getRawValue();
        var state = Ext.getCmp("stateComboBox_id").getValue();
        var new_params = {
            iparam_name: iprjName,
            sysName: systemName,
            prjName: projectName,
            state: state
        };
        Ext.apply(actMonitorActListStroe.proxy.extraParams, new_params);
    });
    var actListGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        store: actMonitorActListStroe,
        iqueryFun: selectFunction,
        region: 'center',
        columns: columns,
        id:'actListGrid',
        cls: 'customize_panel_back actmonitor',
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border: false,
        columnLines: true,
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true,
            // 添加选择前检查
            listeners: {
                beforeSelect: function(selModel, record, index) {
                    // 当状态为"Running"时禁止选择
                    return record.get('actState') !== 'Running';
                }
            }
        }),
        // margins: grid_margin,
        padding: grid_space,
//		bbar : pageBar,
//		loadMask : {
//			msg : " 数据加载中，请稍等 ",
//			removeMask : true
//		},
        viewConfig: {
            getRowClass: changeRowClassActMonitor
        },
        dockedItems: [{

            items: [export_excel_act]
        }]
    });

    if (jobSchedulingQuerySystemSwitch) {
        actListGrid.down('gridcolumn[text=昨日活动执行开始时间]').show();
        actListGrid.down('gridcolumn[text=今日活动执行开始时间]').show();
        actListGrid.down('gridcolumn[text=今日活动预计开始执行时间]').show();
        actListGrid.down('gridcolumn[text=今日活动预计执行结束时间]').show();
    }

    var bottomPanel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        xtype: 'box',
        border: false,
        // region: 'south',
        loader: {
            url: 'page/jobScheduling/actmonitor/workflowBottom.jsp',
            autoLoad: true,
            scripts: true
        },
        height: '5px'
    });

// alarm自动刷新区域
    actMonitorAlarmFreshForm = Ext.create('Ext.form.Panel', {
        frame: true,
        border: false,
        bodyCls: 'fm-spinner',
        layout: {
            type: 'hbox',
            align: 'middle'
        },
        defaults: {
            anchor: '100%'
        },
        items: [
            {
                fieldLabel: '自动刷新',
                xtype: 'checkbox',
                name: 'isAutoRefresh_alarm',
                id: 'isAutoRefresh_alarm',
                padding: '0 0 10 0',
                checked: true,
                labelWidth: 60,
                handler: function () {
                    if (!actMonitorAlarmFreshForm.getForm().isValid()) {
                        Ext.Msg.alert('提示', "自动刷新时间不符合要求!");
                        return;
                    }
                    if (this.value == true) {
                        var refreshTime = actMonitorAlarmFreshForm
                            .queryById('refreshTime_alarm')
                            .getValue();
                        actMonitorAutoFresh_alarm(refreshTime);
                    } else {
                        actMonitorStopAutoFresh_alarm();
                    }
                }
            }, {
                fieldLabel: '  ',
                hideLabel: true,
                labelWidth: 60,
                width: 50,
                name: 'refreshTime_alarm',
                padding: '0 10 0 0',
                id: 'refreshTime_alarm',
                value: '1',
                editable: false,
                xtype: 'numberfield',
                allowDecimals: true,
                step: 1,
                minValue: 1,
                maxValue: 10
            }, {
                xtype: 'label',
                forId: 'myFieldId_alarm',
                text: '分',
                padding: '0 10 0 0'
            }]
    });


    var aram_grid_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        align: 'left',
        width: 40
    }, {
        text: 'ID',
        dataIndex: 'id',
        hidden: true
    }, {
        text: '子系统',
        dataIndex: 'prjName'
    }, {
        text: '工作流名',
        dataIndex: 'flowName'
    }, {
        text: '作业名称',
        dataIndex: 'actName',
        width: 290,
        locked: true
    }, {
        text: '报警类型',
        dataIndex: 'type',
        renderer: function (value, cellmeta, record) {
            var v = actMonitorGetType(value);
            return v;
        }
    }, {
        text: '报警时间',
        dataIndex: 'warnTime',
        width: 160
    }, {
        text: '报警描述',
        dataIndex: 'warnDes',
        flex: 1
    },
        { text: '处理描述',  dataIndex: 'delDes',width:200,
            editor: {
                allowBlank: true
            }
        },
        {
        text: '报警处理',
        dataIndex: 'oper',
            renderer : function (value, metaData, record)
            {
                return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"actMonitorClbj('"
                    + record.get ('id')
                    + "','"
                    + record.get ('delDes')
                    + "');\">处理</a>";
            }
    }];

    Ext.define('aramData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'string'
        }, {
            name: 'prjName',
            type: 'string'
        }, {
            name: 'flowName',
            type: 'string'
        }, {
            name: 'actName',
            type: 'string'
        }, {
            name: 'type',
            type: 'string'
        }, {
            name: 'warnTime',
            type: 'string'
        }, {
            name: 'warnDes',
            type: 'string'
        }, {
            name: 'delDes',
            type: 'string'
        }, {
            name: 'oper',
            type: 'string'
        }
        ]
    });


    actMonitorAlarmStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'aramData',
        pageSize: 8,
        proxy: {
            type: 'ajax',
            url: 'queryAlarmList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });


    var batckDealAlarmbutn = Ext.create('Ext.Button', {
        text: '批量处理',
        baseCls: 'Common_Btn',
        handler: function () {
            actMonitorClbj();
        }
    });

    selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        selType: 'cellmodel'
    });
//var alarm_grid = Ext.create('Ext.grid.Panel', {
//	store : actMonitorAlarmStore,
//	columnLines : true,
//	selModel : selModel,
//	columns : aram_grid_columns,
//	id : 'alarm_grid',
//	height : 251,
//	viewConfig : {
//				 stripeRows : true,// 在表格中显示斑马线
//				 enableTextSelection : true
//				},
//	dockedItems : [ {
//					id : 'pagingbar_alarm',
//					xtype : 'pagingtoolbar',
//					store : actMonitorAlarmStore,
//					dock : 'bottom',
//					displayInfo : true,
//					autoScroll : true,
//					items : [ '-', '&nbsp&nbsp&nbsp&nbsp&nbsp',
//					batckDealAlarmbutn, '&nbsp&nbsp&nbsp&nbsp&nbsp',
//					'-', actMonitorAlarmFreshForm ]
//				} ]
//	});


    // 1. 定义树状结构的数据模型
    Ext.define('PrjFlowModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'id', type: 'string'},
            {name: 'text', type: 'string'},  // 映射到后台返回的name字段
            {name: 'prjName', type: 'string'},
            {name: 'sysName', type: 'string'},  // 添加系统名字段
            {name: 'flowName', type: 'string'},
            {name: 'leaf', type: 'boolean'}
        ]
    });

// 2. 创建树状结构的数据存储
    treeStore = Ext.create('Ext.data.TreeStore', {
        model: 'PrjFlowModel',
        proxy: {
            type: 'ajax',
            url: 'getPrjFlowTree.do' // 后台接口，返回工程和工作流数据
        },
        root: {
            text: '所有工程',
            id: 'root',
            expanded: false
        },
        folderSort: true,
        sorters: [{
            property: 'text',
            direction: 'ASC'
        }]
    });

    // 3. 创建树状面板
    var treePanel = Ext.create('Ext.tree.Panel', {
        title: '',
        store: treeStore,
        rootVisible: false,
        useArrows: true,
        border: false,
        width: 250,
        region: 'west',
        split: true,
        cls: 'customize_panel_back',
        autoExpand: false,
        padding: "0 10 10",
        collapsible: true,
        // 添加搜索框
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top'
            // ,
            // items: [{
            //     xtype: 'textfield',
            //     id: 'treeSearchField', // 添加ID便于获取
            //     emptyText: '请输入',
            //     flex: 1,
            //     enableKeyEvents: true, // 启用键盘事件
            //     listeners: {
            //         // 添加键盘事件监听
            //         keyup: function(field, e) {
            //             if (e.getKey() === e.ENTER) {
            //                 filterTree();
            //             }
            //         },
            //         // 添加清除按钮
            //         specialkey: function(field, e) {
            //             if (e.getKey() === e.ESC) {
            //                 field.setValue('');
            //                 filterTree();
            //             }
            //         }
            //     }
            // }]
        }],
        listeners: {
            itemclick: function(view, record) {
                // 清除当前过滤
                actMonitorActListStroe.clearFilter();
                if (record.get('leaf')) {
                    // 点击工作流节点：过滤该工作流
                    var flowName = record.get('flowName');
                    var prjName = record.get('prjName');
                    var projectName =  Ext.getCmp('prjNameComBox_id').getRawValue();
                    var actTypeValue = Ext.getCmp('actTypeCombo').getValue();
                    actMonitorActListStroe.proxy.extraParams = {
                        treeFlowName: flowName,
                        treePrjName: prjName,
                        projectName:projectName,
                        stateValue:actTypeValue
                    };
                    // 重新加载数据
                    actMonitorActListStroe.loadPage(1);
                } else {
                    var sysName = record.get('sysName');
                    var prjName = record.get('prjName');
                    if (sysName === prjName) {
                        // 系统名和工程名不同时的处理逻辑
                        sysName = "";
                    }
                    var projectName =  Ext.getCmp('prjNameComBox_id').getRawValue();
                    var actTypeValue = Ext.getCmp('actTypeCombo').getValue();
                    actMonitorActListStroe.proxy.extraParams = {
                        treePrjName: prjName,
                        treeSysName: sysName,
                        projectName:projectName,
                        stateValue:actTypeValue
                    };
                    // 重新加载数据
                    actMonitorActListStroe.loadPage(1);
                }
            }
        }
    });

// 添加过滤功能函数
    function filterTree() {
        var searchText = Ext.getCmp('treeSearchField') ? Ext.getCmp('treeSearchField').getValue() : '';
        var prjName = Ext.getCmp('prjNameComBox_id').getRawValue();
        var stateValue = Ext.getCmp("stateComboBox_id").getValue();

        // 设置treeStore的额外参数
        treeStore.getProxy().extraParams = {
            prjFolwName: searchText,
            state: stateValue,
            prjName: prjName
        };

        // 重新加载数据
        treeStore.load({
            callback: function() {
                if (searchText || prjName || stateValue) {
                    // 展开所有节点便于查看结果
                    treePanel.expandAll();
                }
            }
        });
    }
    var alarm_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        store: actMonitorAlarmStore,
        selModel: selModel,
        columns: aram_grid_columns,
        id: 'alarm_grid',
        cls: 'customize_panel_back',
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        viewConfig: {
            stripeRows: true,// 在表格中显示斑马线
            enableTextSelection: true
        },
        ipageItems: [
            batckDealAlarmbutn,
            '-',
            actMonitorAlarmFreshForm
        ],
        padding: "0 20 20"
    });
    actMonitorAlarmPanel = Ext.create('Ext.Panel', {
        title: '报警信息',
        collapsible: true,
        witdh: '100%',
        height: 300,
        split: true,
        region: 'south',
        layout: 'fit',
        collapsed: true,
        border: false,
        titleAlign: 'left',
        titleCollapse: true,
        items: [alarm_grid]
    });

    if (!isCZbankSwitch) {
        // 当开关为true时，包含树状结构
        mainItems = [condition, actListGrid, bottomPanel, actMonitorAlarmPanel];
    } else {
        // 当开关为false时，不包含树状结构
        mainItems = [treePanel, condition, actListGrid, bottomPanel, actMonitorAlarmPanel];
    }

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "monitorGrid",
        layout: 'border',
        border: false,
        bodyPadding : grid_space,
        cls: 'act_discolor',
        bodyCls: 'service_platform_bodybg',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        items: mainItems
    });
    contentPanel.on('resize', function () {
        mainPanel.setWidth(contentPanel.getWidth());
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
    });

    function freshGrid() {
        // aoms.log('fresh grid');
        if (contentPanel.title == '活动监控') {
            console.log('活动监控自动刷新时间'+Date.now());
            if (actMonitorAlarmRefreshObj) {
                clearInterval(actMonitorAlarmRefreshObj);
            }
            if(ycQueryGroNameSwitch){
                var refreshTime = condition.items.items[7].value;
            }else{
                var refreshTime = condition.items.items[6].value;
            }

            // aoms.log(refreshTime);
            actMonitorAlarmRefreshObj = setInterval(freshGrid, parseInt(refreshTime) * 1000);
            //actMonitorActListStroe.reload();
            // 获取工程名称
            if (treePanel && treePanel.getSelectionModel) {
                treePanel.getSelectionModel().deselectAll();
            }
            Ext.getCmp('actTypeCombo').setValue('all');
            var projectName = Ext.getCmp('prjNameComBox_id').getRawValue();
            var proxy = actMonitorActListStroe.getProxy();
            proxy.extraParams = {
                stateValue: 'all',  // 确保stateValue为all
                prjName: projectName
            };

            // 重新加载数据
            actMonitorActListStroe.loadPage(1);
            filterTree(); // 使用filterTree函数刷新树状结构，支持查询条件

        }
    }
    //防止定时器重复创建
    if (actMonitorAlarmRefreshObj) {
        clearInterval(actMonitorAlarmRefreshObj);
    }
    if(actMonitorAutoFreshTime){
        clearTimeout(actMonitorAutoFreshTime);
    }
    actMonitorAlarmRefreshObj = setInterval(freshGrid, 60 * 1000);
    actMonitorStartAutoFreshAlarm();

    function actOpen(value, metaData, record) {
        // aoms.log('actDefName:'+record.get('actDefName'));
        var func = '';
        var flowId = record.get('flowID');
        var actId = record.get('actIID');
        var actDefName = record.get('actDefName');
        var state = record.get('actState');
        if (state == 'Paused') {
            func = "openFlowNotPauseNotRunningActInfoWindow('" + flowId + "','" + actId + "')";
            return '<a href="javascript:' + func + ';">' + value + '</a>';
        } else {
            if (actDefName == 'UserTask') {
                var taskId = record.get('taskID');
                func = "openUTInfo('" + flowId + "','" + actId + "','" + state + "','" + taskId + "','" + 0 + "')";
                return '<a href="javascript:' + func + ';">' + value + '</a>';
            } else if (actDefName == 'ShellCmd') {
                if (state == 'Running' || state == 'Continue') {
                    func = "openWindowsRunning('getShellOutPutRunning_ieai.do', "
                        + record.get('flowID')
                        + ", '"
                        + record.get('actName')
                        + "', '"
                        + record.get('flowInsName')
                        + "', "
                        + null
                        + ", '"
                        + record.get('reqId') + "')";
                } else {
                    if (record.get('errorTaskId') > 0) {
                        if (state != 'QueueUp') {
                            func = "openErrorTaskDetail('" + record.get('actState')
                                + "'," + record.get('errorTaskId') + "," + 0 + ")";
                        }
                    }
                }
                return '<a href="javascript:' + func + ';">' + value + '</a>';
            } else if (actDefName == 'ScriptCall' || actDefName == 'TaskCall') {
                if (state == 'Running' || state == 'Continue') {
                    func = "openWindowsRunning('getScriptCallOutPutRunning.do', "
                        + record.get('iid')
                        + ", '"
                        + record.get('actName')
                        + "', '"
                        + record.get('flowInsName')
                        + "', "
                        + 1
                        + ", '"
                        + record.get('reqId') + "')";
                } else {
                    if (record.get('errorTaskId') > 0) {
                        if (state != 'QueueUp') {
                            func = "openErrorTaskDetail('" + record.get('actState')
                                + "'," + record.get('errorTaskId') + "," + 0 + ")";
                        }
                    }
                }
                return '<a href="javascript:' + func + ';">' + value + '</a>';
            } else if (actDefName == 'Tn5250') {
                if (state == 'Fail') {
                    if (record.get('errorTaskId') > 0) {
                        func = "openErrorTaskDetail('" + record.get('actState')
                            + "'," + record.get('errorTaskId') + ")";
                    }
                } else {
                    func = "openWindowsRunning_Tn('getTn5250OutPutRunning_ieai.do', "
                        + record.get('flowID')
                        + ", '"
                        + record.get('actName')
                        + "', '"
                        + record.get('flowInsName')
                        + "', "
                        + null
                        + ", '"
                        + record.get('reqId') + "')";
                }
                return '<a href="javascript:' + func + ';">' + value + '</a>';
            } else if (actDefName == "Act Monitor") {
                func = "openActMonitorInfo('" + record.get('flowID') + "','" + record.get('actName') + "')";
                return '<a href="javascript:' + func + ';">' + value + '</a>';
            } else if (actDefName == "httpscript") {
                if (state == 'Fail') {
                    if (record.get('errorTaskId') > 0) {
                        func = "openErrorTaskDetail('" + record.get('actState')
                            + "'," + record.get('errorTaskId') + "," + 0 + ")";
                    }
                    return '<a href="javascript:' + func + ';">' + value + '</a>';
                }
            } else if (actDefName == "KafkaConsumer") {
                if (state == 'Fail' || state == 'Fail:Business') {
                    if (record.get('errorTaskId') > 0) {
                        func = "openErrorTaskDetail('" + record.get('actState')
                            + "'," + record.get('errorTaskId') + "," + 0 + ")";
                    }
                    return '<a href="javascript:' + func + ';">' + value + '</a>';
                }
            } else if (actDefName == "PROC" || actDefName == "StoreProcedure") {
                if (state == 'Fail' || state == 'Fail:Business') {
                    if (record.get('errorTaskId') > 0) {
                        func = "openErrorTaskDetail('" + record.get('actState')
                            + "'," + record.get('errorTaskId') + "," + 0 + ")";
                    }
                    return '<a href="javascript:' + func + ';">' + value + '</a>';
                }
            }
            if (actDefName == 'Tn5250') {
                if (state == 'Fail') {
                    if (record.get('errorTaskId') > 0) {
                        func = "openErrorTaskDetail('" + record.get('actState')
                            + "'," + record.get('errorTaskId') + ")";
                    }
                } else {
                    func = "openWindowsRunning_Tn('getTn5250OutPutRunning_ieai.do', "
                        + record.get('flowID')
                        + ", '"
                        + record.get('actName')
                        + "', '"
                        + record.get('flowInsName')
                        + "', "
                        + null
                        + ", '"
                        + record.get('reqId') + "')";
                }

                return '<a href="javascript:' + func + ';">' + value + '</a>';
            } else {
                return value;
            }
        }
    }

//	$("body").keydown(function(event) {
// 	    if (event.keyCode == "13") { 
// 	    	selectFunction();
// 	    }
// 	});
});

function reloadGroup() {
    actMonitorActListStroe.reload();
}

function actMonitorStartAutoFreshAlarm() {
    // 自动刷新查询报警方法
    var refreshTime = actMonitorAlarmFreshForm.queryById('refreshTime_alarm').getValue();
    actMonitorAutoFresh_alarm(refreshTime);
}


function actMonitorAutoFresh_alarm(t) {
    //actMonitorLoadAlarmImgAndData();//test.暂时注释掉
    // 验证是否还在活动监控页面，如果不在，则取消页面的自动刷新功能
    console.log("contentPanel.title"+contentPanel.title);
    if (contentPanel.title == '活动监控') {
        console.log('报警刷新时间'+Date.now());
        actMonitorLoadAlarmImgAndData();
        var refreshTime = actMonitorAlarmFreshForm.queryById('refreshTime_alarm').getValue();
        console.log('refreshTime:'+refreshTime);
        actMonitorAutoFreshTime = setTimeout("actMonitorAutoFresh_alarm(" + refreshTime+ ")", 1000 * refreshTime * 60); // 以分为单位
    } else {
        actMonitorAlarmStore.reload()
        actMonitorStopAutoFresh_alarm();
        if (Ext.isIE) {
            CollectGarbage();
        }
    }

}
function actMonitorLoadAlarmImgAndData() {
    actMonitorAlarmStore.loadPage(1);// 显示第一页
    Ext.Ajax.request({
        url: 'queryAlarmNum.do',
        method: 'POST',
        success: function (response, opts) {
            var success = Ext.decode(response.responseText).success;
            var isalarm = Ext.decode(response.responseText).isalarm;
            //var alarmNumByPermit = Ext.decode(response.responseText).alarmNumByPermit;
            if (success) {
                actMonitorAlarmPanel.setTitle(isalarm);
            }
            var audio_player_actMonitor = document.getElementById("audio_player_actMonitor");
            if (audio_player_actMonitor != null && typeof (audio_player_actMonitor) != "undefined") {
                setTimeout(function () {
                    audio_player_actMonitor.play()
                }, 1500);
            }
        },
        failure: function (response, opts) {
            Ext.Msg.alert('提示', "查询请求未成功,请重新查询。");
        }
    });
}
function actMonitorStopAutoFresh_alarm() {
    clearTimeout(actMonitorAutoFreshTime);
}

function actMonitorClbj(iid,delDes) {
    var jsonData = actMonitorGetJSON(iid,delDes);
    if (jsonData == "") {
        Ext.Msg.alert('提示', "请选择要处理报警！");
        return;
    }
    Ext.MessageBox.confirm('提示', '确认要处理报警信息吗', callBack);
    function callBack(id) {
        if (id == 'yes') {
            Ext.Ajax.request({
                url: 'delAlarm.do',
                method: 'POST',
                params: {
                    jsonData: jsonData
                },
                success: function (response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.MessageBox.show({
                            title: "提示",
                            msg: message,
                            buttonText: {
                                yes: '确定'
                            },
                            buttons: Ext.Msg.YES
                        });
                        actMonitorLoadAlarmImgAndData();
                    } else {
                        Ext.MessageBox.show({
                            title: "提示",
                            msg: message,
                            buttonText: {
                                yes: '确定'
                            },
                            buttons: Ext.Msg.YES
                        });
                    }
                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        } else {
            return;
        }
    }
}


function actMonitorGetJSON(alarmid,delDes) {
    if (alarmid != undefined) {
        return '[{"id":"' + alarmid + '","delDes":"'+delDes+'"}]';
    }
    var m = Ext.getCmp('alarm_grid').getSelectionModel().getSelection();
    if (m.length < 1) {
        return "";
    } else {
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            if (i == 0)
                jsonData = jsonData + '{"id":"' + actMonitorParsIIDJson('id', m[i].data) + '","delDes":"' + actMonitorParsIIDJson('delDes', m[i].data) + '"}';
            else {
                jsonData = jsonData + "," + '{"id":"'
                    + actMonitorParsIIDJson('id', m[i].data) + '","delDes":"' + actMonitorParsIIDJson('delDes', m[i].data) + '"}';
            }
        }
        jsonData = jsonData + "]";
        return jsonData;
    }
}

//从一个json对象中，解析出key=iid的value,返回改val
function actMonitorParsIIDJson(key, jsonObj) {
    //var eValue = eval('jsonObj.' + key);
    return jsonObj['' + key + ''];
}

function actMonitorGetType(type) {
    if ("0" == type) {
        return "启动时间";
    } else if ("1" == type) {
        return "结束时间";
    } else if ("2" == type) {
        return "设置耗时";
    } else if ("3" == type) {
        return "平均耗时";
    } else if ("4" == type) {
        return "批量执行中断";
    } else if ("5" == type) {
        return ("Agent通信异常");
    } else if ("6" == type) {
        return "Server宕机";
    } else if ("7" == type) {
        return "Agent性能异常";
    } else if ("8" == type) {
        return "系统数据库异常";
    } else if ("9" == type) {
        return "超时异常";
    } else {
        return "";
    }
}


//function actMonitorStateImage(value, metaData, record) {
//	if (value == 'Fail')
//		return '<img src="images/fail.png" />';
//	else if (value == 'Ready')
//		return '<img src="images/ready.png" />';
//	else if (value == 'Running' || value == 'Continue')
//		return '<img src="images/running.png" />';
//	else if (value == 'ManualRunning')
//		return '<img src="images/manual.png" />';
//	else if (value == 'ManualDisconnect')
//		return '<img src="images/connection_failed.png" />';
//	else if (value == 'Disconnect')
//		return '<img src="images/connection_failed.png" />';
//	else if (value == 'Timeout')
//		return '<img src="images/timeout.png" />';
//	else if (value == 'QueueUp')
//		return '<img src="images/queue.png" />';
//	else if (value == 'Paused')
//		return '<img src="images/pausejob.gif" />';
//	return value;
//}

function actMonitorStateImage(value, metaData, record) {

    if (isHNbankSwitch) {
        if (value == 'Fail') {
            return '失败';
        } else if (value == 'Ready') {
            return '就绪';
        } else if (value == 'Running' || value == 'Continue') {
            return '运行';
        } else if (value == 'ManualRunning') {
            return '异常处理中';
        } else if (value == 'ManualDisconnect') {
            return '连接失败处理中';
        } else if (value == 'Disconnect') {
            return '运行连接失败';
        } else if (value == 'Timeout') {
            return '超时';
        } else if (value == 'QueueUp') {
            return '排队';
        } else if (value == 'Paused') {
            return '暂停';
        } else if (value == 'HangUp') {
            return '挂起';
        } else if (value == 'Fail:Business') {
            return '失败:业务异常';
        } else {
            return value;
        }

    } /*else if(isHLJbankSwitch)
    {
        if (value == 'Fail') {
        	return '<img src="images/tp_common.png" class="wflow_fail"/>';
        } else if (value == 'Ready') {
            return '<img src="images/tp_common.png" class="wflow_ready"/>';
        } else if (value == 'Running' || value == 'Continue') {
            return '<img src="images/tp_common.png" class="wflow_running"/>';
        } else if (value == 'ManualRunning') {
            return '<img src="images/tp_common.png" class="wflow_manual"/>';
        } else if (value == 'ManualDisconnect') {
            return '<img src="images/tp_common.png" class="wflow_connect"/>';
        } else if (value == 'Disconnect') {
            return '<img src="images/tp_common.png" class="wflow_connect"/>';
        }else if (value == 'Timeout') {
            return '<img src="images/tp_common.png" class="wflow_timeout"/>';
        } else if (value == 'QueueUp') {
            return '<img src="images/tp_common.png" class="wflow_queue"/>';
        } else if (value == 'Paused') {
            return '<img src="images/tp_common.png" class="wflow_pause"/>';
        } else if (value == 'HangUp') {
            return '<img src="images/tp_common.png" class="wflow_hangup"/>';
        } else if (value == 'Fail:Business') {
            return '<img src="images/tp_common.png" class="wflow_buss"/>';
        } else {
            return value;
        }
    
    }*/
    else 
    {
        if (value == 'Fail' && isAbnormalSwitch && !isHLJbankSwitch) {
            return '<img src="images/tp_common.png" class="wflow_fail"/><span id="playRing_actMonitor"><audio id="audio_player_actMonitor" autoplay="true"><source src="page/jobScheduling/actmonitor/ring.mp3" type="audio/mpeg"></audio></span>';
        }else if (value == 'Fail' && isHLJbankSwitch) {
            return '<img src="images/tp_common.png" class="wflow_fail"/>';
        } else if (value == 'Fail' && !isAbnormalSwitch) {
            return '<img src="images/tp_common.png" class="wflow_fail"/>';
        } else if (value == 'Fail') {
            return '<img src="images/tp_common.png" class="wflow_fail"/>';
        } else if (value == 'Ready' && isAbnormalSwitch && !isHLJbankSwitch) {
            return '<img src="images/tp_common.png" class="wflow_ready"/><span id="playRing_actMonitor"><audio id="audio_player_actMonitor" autoplay="true"><source src="page/jobScheduling/actmonitor/ring.mp3" type="audio/mpeg"></audio></span>';
        }else if (value == 'Ready' && isHLJbankSwitch) {
        	return '<img src="images/tp_common.png" class="wflow_ready"/>';
        } else if (value == 'Ready' && !isAbnormalSwitch) {
            return '<img src="images/tp_common.png" class="wflow_ready"/>';
        } else if (value == 'Ready') {
            return '<img src="images/tp_common.png" class="wflow_ready"/>';
        } else if (value == 'Running' || value == 'Continue') {
            return '<img src="images/tp_common.png" class="wflow_running"/>';
        } else if (value == 'ManualRunning') {
            return '<img src="images/tp_common.png" class="wflow_manual"/>';
        } else if (value == 'ManualDisconnect') {
            return '<img src="images/tp_common.png" class="wflow_connect"/>';
        } else if (value == 'Disconnect') {
            return '<img src="images/tp_common.png" class="wflow_connect"/>';
        } else if (value == 'Timeout' && isAbnormalSwitch && !isHLJbankSwitch) {
            return '<img src="images/tp_common.png" class="wflow_timeout"/><span id="playRing_actMonitor"><audio id="audio_player_actMonitor" autoplay="true"><source src="page/jobScheduling/actmonitor/ring.mp3" type="audio/mpeg"></audio></span>';
        }else if (value == 'Timeout' && isHLJbankSwitch) {
        	return '<img src="images/tp_common.png" class="wflow_timeout"/>';
        } else if (value == 'Timeout' && !isAbnormalSwitch) {
            return '<img src="images/tp_common.png" class="wflow_timeout"/>';
        } else if (value == 'Timeout') {
            return '<img src="images/tp_common.png" class="wflow_timeout"/>';
        } else if (value == 'QueueUp') {
            return '<img src="images/tp_common.png" class="wflow_queue"/>';
        } else if (value == 'Paused') {
            return '<img src="images/tp_common.png" class="wflow_pause"/>';
        } else if (value == 'HangUp') {
            return '<img src="images/tp_common.png" class="wflow_hangup"/>';
        } else if (value == 'Fail:Business') {
            return '<img src="images/tp_common.png" class="wflow_buss"/>';
        } else {
            return value;
        }
    }
}
//根据报警等级，改变行的颜色状态
function changeRowClassActMonitor(record, rowIndex, rowParams, store) {
    //console.log(isAbnormalSwitch);
    if (record.data.actDefName == "Tn5250" && record.data.actState == "AlterAS400") {
        return 'amtor_yellow';
    }
    var showColor = record.get("showColor");
    var isShow = showColor.split(';');
    console.log(showColor);
    if (isAbnormalSwitch || isHLJbankSwitch) {
        var showColor = record.get("showColor");
        var isShow = showColor.split(';');

        if (isShow[0] == 'true') {
            var color = isShow[1];
            if (color) {
                if (color == 'red') {
                    return 'amtor_red';
                }
                if (color == 'yellow') {
                    return 'amtor_yellow';
                }
            }
        }
    }
    if (isTimeSetAlarm) {
        var showColor = record.get("showColor");
        var isShow = showColor.split(';');
        if (isShow[0] == 'true') {
            var color = isShow[1];
            if (color) {
                if (color == 'red') {
                    return 'amtor_red';
                } else if (color == 'yellow') {
                    return 'amtor_yellow';
                } else if (color == 'green') {
                    return 'amtor_green';
                }else if (color == 'orange') {
                    return 'amtor_orange';
                }
            }
        }
    }
    if (isActmonitorStart) {
        var color = isShow[1];
        if (color && color == 'blue') {
            return 'amtor_blue';
        }
    }
    if (record.data.actDes.indexOf('sound') >= 0) {
        mySound.play();
    }
    if (record.data.actDes.indexOf('color') >= 0) {
        return "x-grid-row-yellow-poc";
    } else {
        return "";
    }
}
function openActMonitorInfo(flowID, actName) {
    Ext.define('showActMonitorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'flowID',
            type: 'long'
        }, {
            name: 'endTime',
            type: 'string'
        }, {
            name: 'startTime',
            type: 'string'
        }, {
            name: 'actState',
            type: 'string'
        }, {
            name: 'prjName',
            type: 'string'
        }, {
            name: 'flowName',
            type: 'string'
        }]
    });

    var showActMonitorStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'showActMonitorModel',
        pageSize: 25,
        proxy: {
            type: 'ajax',
            url: 'showActMonitorInfo.do?flowID=' + flowID + '&actName=' + actName,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var showFlowGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
        split: true,
        multiSelect: true,
        emptyText: '没有流程详细信息',
        width: contentPanel.getWidth() - 30,
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        height: contentPanel.getHeight(),
        columnLines: true,
        store: showActMonitorStore,
        padding: grid_space,
        columns: [{
            text: '序号',
            align: 'left',
            xtype: 'rownumberer',
            width: 40
        }, {
            text: '工程名称',
            align: 'left',
            dataIndex: 'prjName',
            flex: 1,
            sortable: true,
            editor: {
                allowBlank: false
            }
        }, {
            text: '工作流ID',
            align: 'left',
            dataIndex: 'flowID',
            flex: 1,
            sortable: true,
            editor: {
                allowBlank: false
            }
        }, {
            text: '工作流名称',
            align: 'left',
            dataIndex: 'flowName',
            flex: 1,
            sortable: true,
            editor: {
                allowBlank: false
            }
        }, {
            text: '启动时间',
            align: 'left',
            dataIndex: 'startTime',
            flex: 1,
            sortable: true,
            editor: {
                allowBlank: false
            }
        }, {
            text: '结束时间',
            align: 'left',
            dataIndex: 'endTime',
            flex: 1,
            sortable: true,
            editor: {
                allowBlank: false
            }
        }, {
            text: '状态',
            align: 'left',
            dataIndex: 'actState',
            flex: 1,
            sortable: true,
            editor: {
                allowBlank: false
            }

        }],
    });
    var showActMonitorInfoWin = null;
    if (showActMonitorInfoWin == null || !showActMonitorInfoWin.isVisible()) {
        showActMonitorInfoWin = Ext.create('Ext.window.Window', {
            title: 'actMonitor信息展示',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            padding: '20',
            width: contentPanel.getWidth() - 50,
            height: contentPanel.getHeight(),
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            layout: 'fit',
            items: [showFlowGrid1]
        });
    }
    showActMonitorInfoWin.show();
}

function showTopo(projectName,actName,queryDate){
    console.log(projectName+":"+actName+":"+queryDate);
    //跳转topo依赖展示图
    var url = 'newTopoScreenDisplayGanttPic.do?projectName='+projectName+'&firstFlowName='+actName+'&queryDate=' + queryDate + '&refreshTime=600';
    console.log('跳转URL:', url);
    window.open(url, '_blank'); // 在新标签页中打开
}





function doubleCheckBatch(type){
    var doubleCheckBatchWin = null;
    var flowids='';
    var taskids='';
    var isHidden = false;
    // var el = document.getElementsByTagName('input');
    // var len = el.length;
    var grid = Ext.getCmp('actListGrid')
    var selectedRecords = grid.getSelectionModel().getSelection();
    for (var i = 0 ; i < selectedRecords.length; i++) {
        var selectedRecord = selectedRecords[i];
        var taskid = selectedRecord.raw.flowID;
        taskids = taskids + taskid + ",";
    }
    // for (var i = 0; i < len; i++) {
    // 	if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
    // 			&& (el[i].disabled == false)) {
    // 		if (el[i].checked && el[i].value != '') {
    // 			taskids = taskids + el[i].value + ",";
    // 			el[i].disabled = true;
    // 		}
    // 	}
    // }
    if (taskids=="") {
        Ext.MessageBox.alert("提示", "请选择要操作的作业记录");
        return;
    }
//	 if(type==1 || type==2){
//	 //重试和略过
//		 var actJson = getSelectedActKeyInfoList(1);
//		 	if(actJson=='false'){
//				Ext.MessageBox.alert("提示", "请选择状态为异常或者挂起的活动记录");
//				return;
//		 	}else{
//		 		 flowids= actJson.split("#@#")[0];
//				 taskids=actJson.split("#@#")[1];
//		 	}
    /** 审核人列表model* */
    Ext.define ('AuditorModel',
        {
            extend : 'Ext.data.Model',
            fields : [
                {
                    name : 'loginName',
                    type : 'string'
                },
                {
                    name : 'fullName',
                    type : 'string'
                }
            ]
        });
    var auditorStore = Ext.create ('Ext.data.Store',
        {
            autoLoad : true,
            model : 'AuditorModel',
            proxy :
                {
                    type : 'ajax',
                    url : 'getAuditorListForExecmonitor.do',
                    reader :
                        {
                            type : 'json',
                            root : 'dataList'
                        }
                }
        });
    //审核人列表combo
    var auditorComBox = Ext.create ('Ext.form.ComboBox',
        {
            padding : '0 0 0 5',
            editable : false,
            fieldLabel : "审核人",
            labelWidth : 50,
            store : auditorStore,
            queryMode : 'local',
            width : 250,
            displayField : 'fullName',
            valueField : 'loginName' ,
            listeners :
                {
                    beforequery : function (e)
                    {
                        var combo = e.combo;
                        if (!e.forceAll)
                        {
                            var value = e.query;
                            combo.store.filterBy (function (record, id)
                            {
                                var text = record.get (combo.displayField);
                                return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
                            });
                            combo.expand ();
                            return false;
                        }
                    }
                }
        });
    /** 双人复核发起审核按钮* */
    var doubleCheckButton = Ext.create ("Ext.Button",
        {
            cls : 'Common_Btn',
            textAlign : 'center',
            text : "发起审核",
            handler : doubleCheckFunction
        });
    // 双人复核发起审核按钮
    function doubleCheckFunction(btn){
        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm ("确认发起审核", "是否确认发起审核?", function (id)
        {
            if (id == 'yes')
                doubleCheckIns (type);
        });
    }
    function doubleCheckIns(type){
        var auditorValue = auditorComBox.getValue ();
        if (trim (auditorValue) == '' || (null == auditorValue))
        {
            Ext.MessageBox.alert ("提示", "请选择审核人");
            return false;
        }
        Ext.Ajax.request (
            {
                url : 'execActmonitorDoublecheck.do',
                method : 'POST',
                params :
                    {
                        type:type,
                        flowids:taskids,
                        taskids : taskids,
                        auditUser:auditorValue

                    },
                success : function (response, options)
                {
                    var success = Ext.decode (response.responseText).success;
                    var message = Ext.decode (response.responseText).message;
                    if (success==true)
                    {
                        Ext.Msg.alert ('提示', message,function(){
                            for (var i = 0; i < len; i++) {
                                if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
                                    && (el[i].disabled == false)) {
                                    if (el[i].checked && el[i].value != '') {
                                        el[i].disabled = true;
                                    }
                                }
                            }
                            if(doubleCheckBatchWin != null){
                                doubleCheckBatchWin.close();
                            }
                        });
                    }
                    else
                    {
                        Ext.MessageBox.show (
                            {
                                width : 300,
                                title : "提交失败",
                                msg : message,
                                buttonText :
                                    {
                                        yes : '确定'
                                    },
                                buttons : Ext.Msg.YES
                            });
                    }
                },
                failure : function(result, request) {//alert(result);
                    secureFilterRs(result,"操作失败！");

                }

            });
    }
    Ext.define('doubleCheckBatchModel', {
        extend : 'Ext.data.Model',
        fields : [  {
            name : 'DATADATE',
            type : 'string'
        }, {
            name : 'PRJNAME',
            type : 'string'
        }, {
            name : 'ACTNAME',
            type : 'string'
        }, {
            name : 'SYSTEM',
            type : 'string'
        }, {
            name : 'STATE',
            type : 'string'
        }, {
            name : 'NODEID',
            type : 'string'
        }, {
            name : 'DESC',
            type : 'string'
        }, {
            name : 'MAINPRJNAME',
            type : 'string'
        }  ]
    });

    var doubleCheckBatchStore = Ext.create('Ext.data.Store', {
        autoLoad : true,
        autoDestroy : true,
        model : 'doubleCheckBatchModel',
        pageSize : 100,
        proxy : {
            type : 'ajax',
            url : 'queryDoubleCheckBatch.do',
            reader : {
                type : 'json',
                root : 'dataList',
                totalProperty : 'total'
            }
        }
    });
    doubleCheckBatchStore.on('beforeload', function(s) {
        var params = s.getProxy().extraParams;
        Ext.apply(params, {
            flowids:taskids,
            type : type
        });
    });

    var doubleCheckBatchGrid  = Ext.create('Ext.grid.Panel', {
        cls:'customize_panel_back',
        ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        padding : panel_margin,
        border:false,
        region : 'center',
        columnLines : true,
        store : doubleCheckBatchStore,
        columns : [ {
            text : '序号',
            align : 'left',
            xtype : 'rownumberer',
            width : '5%'
        },
            {
                text : '数据日期',
                align : 'left',
                dataIndex : 'DATADATE',
                width: '10%',
                sortable : true,
                hidden :type == 6 || type == 7
            }, {
                text : '工程名',
                align : 'left',
                dataIndex : 'MAINPRJNAME',
                width:  '20%',
                hidden :!isHidden||type == 5,
                sortable : true,
                editor : {
                    allowBlank : false
                }
            }, {
                text : '所属分类',
                align : 'left',
                dataIndex : 'SYSTEM',
                width: isHidden?'16%':'12%',
                sortable : true
            }, {
                text : '系统名称',
                align : 'left',
                dataIndex : 'PRJNAME',
                width: isHidden?'16%':'12%',
                sortable : true
            }, {
                text : '作业名称',
                align : 'left',
                dataIndex : 'ACTNAME',
                width: isHidden?'30%':'15%',
                sortable : true
            }, {
                text : '作业状态',
                align : 'left',
                dataIndex : 'STATE',
                width: '10%',
                sortable : true,
                hidden :isHidden
            }, {
                text : 'NODEID',
                align : 'left',
                dataIndex : 'NODEID',
                flex : 1,
                width:'10%',
                sortable : true,
                hidden :isHidden
            }, {
                text : '作业描述',
                align : 'left',
                dataIndex : 'DESC',
                width:'10%',
                flex : 1,
                sortable : true,
                hidden :isHidden
            }]

    });

    var dTitle = '';
    if(type==1){
        dTitle = '重试操作复核';
    }else if(type==2){
        dTitle = '跳过操作复核';
    }
    if (doubleCheckBatchWin == null || !doubleCheckBatchWin.isVisible()) {
        doubleCheckBatchWin = Ext.create('Ext.window.Window', {
            title : dTitle,
            modal : true,
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,

            width : contentPanel.getWidth() /2+200,
            height : contentPanel.getHeight()/2+200,
            draggable : false,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'border',
            items : [doubleCheckBatchGrid ],
            dockedItems : [
                {
                    dock : 'bottom',
                    xtype : 'toolbar',
                    border : false,
                    items : [
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'tbfill'
                        },auditorComBox,doubleCheckButton
                    ]
                }
            ]
        });
    }
    doubleCheckBatchWin.show();
}


function batchOperationTask(oper) {

    var tasks = '';
    var grid = Ext.getCmp('actListGrid')
    var selectedRecords = grid.getSelectionModel().getSelection();
    for (var i = 0 ; i < selectedRecords.length; i++) {
        var selectedRecord = selectedRecords[i];
        var taskid = selectedRecord.raw.flowID;
        tasks = tasks + taskid + ",";
    }
    batchOper(tasks, oper);
}


/*
 *
 * 批量操作
 *
 *
 */
function batchOper(taskidlist, oper) {
    /*批量略过不可包含日启校验 - start*/
    var grid = Ext.getCmp('actListGrid')
    var selectedRecords = grid.getSelectionModel().getSelection();
    // 存放行序号
    var rowArray = [];
    var messageText = '';
    for (var i = 0 ; i < selectedRecords.length; i++) {
        var selectedRecord = selectedRecords[i];
        // 是否日启字段属性
        var isDayStart = selectedRecord.raw.dayStart;
        var index = grid.store.indexOf(selectedRecord)
        var rownumber = index+1;
        if (isDayStart) {
            rowArray.push(rownumber);
        }
    }
    if (rowArray.length > 0 && oper == '2') {
        for (var i = 0; i < rowArray.length; i++) {
            messageText += rowArray[i];
            if (i < rowArray.length - 1) {
                messageText += '、';
            }
        }
        Ext.MessageBox.show({
            title : "提示",
            msg : messageText + " 是日启动工程,禁止略过",
            buttonText : {
                yes : '确定'
            },
            buttons : Ext.Msg.YES
        });
        return;
    }

    /*批量略过不可包含日启校验 - end*/

    if (taskidlist == "") {
        Ext.Msg.alert('提示', "请选择要处理的异常作业！");
        return;
    }

    var batchUrl = '';

    if(bhPerSwitch)
    {
        batchUrl = 'batchOperBh.do';
    }else
    {
        batchUrl = 'queryHomeData.do?action=batchOper';
    }
    //alter(batchUrl);
    Ext.MessageBox.confirm('提示', '确定要处理选中的异常作业吗?', callBack);
    function callBack(id) {
        if (id == 'yes') {
            Ext.Ajax.request({
                // url : 'homeAction.do?action=batchOper',
                url : batchUrl,
                method : 'POST',
                params : {
                    errorTaskIdList : taskidlist,
                    oper : oper
                },
                success : function(response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.MessageBox.show({
                            title : "提示",
                            msg : message,
                            buttonText : {
                                yes : '确定'
                            },
                            buttons : Ext.Msg.YES
                        });
                        var obj = document.getElementById('taskItemAll');
                        obj.checked = false;
                        reloadGroup();
                    } else {
                        Ext.MessageBox.show({
                            title : "提示",
                            msg : message,
                            buttonText : {
                                yes : '确定'
                            },
                            buttons : Ext.Msg.YES
                        });
                    }

                },
                failure : function(response, opts) {
                    Ext.Msg.alert('提示', "批量操作请求未成功,请重新操作");
                }
            });
        } else {

            var el = document.getElementsByTagName('input');
            var len = el.length;
            var tasks = '';
            for (var i = 0; i < len; i++) {
                if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
                    && (el[i].disabled == true)) {
                    if (el[i].checked && el[i].value != '') {
                        tasks = tasks + el[i].value + ",";
                        el[i].disabled = false;
                    }
                }
            }
            return;
        }
    }

}