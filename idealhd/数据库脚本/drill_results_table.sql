-- 演练结果表
CREATE TABLE drill_results (
    id VARCHAR(50) PRIMARY KEY COMMENT '主键ID',
    project_name VARCHAR(100) NOT NULL COMMENT '工程名称',
    activity_name VARCHAR(100) NOT NULL COMMENT '活动名称',
    topo_level VARCHAR(20) NOT NULL COMMENT 'topo显示层数',
    data_date DATE NOT NULL COMMENT '数据日期',
    save_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '保存时间',
    remark TEXT COMMENT '备注信息',
    
    -- 核心数据字段
    original_xml LONGTEXT NOT NULL COMMENT '完整XML数据',
    drill_config LONGTEXT NOT NULL COMMENT '演练配置JSON字符串',
    
    -- 统计字段
    node_count INT DEFAULT 0 COMMENT '节点数量',
    exception_count INT DEFAULT 0 COMMENT '异常节点数量',
    alarm_count INT DEFAULT 0 COMMENT '告警节点数量',
    
    -- 审计字段
    created_by VARCHAR(50) COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_project_activity (project_name, activity_name),
    INDEX idx_data_date (data_date),
    INDEX idx_save_time (save_time),
    
    -- 唯一约束（同一工程、活动、层数、日期、时间的组合应该是唯一的）
    UNIQUE KEY uk_drill (project_name, activity_name, topo_level, data_date, save_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='演练结果表';

-- 插入示例数据
INSERT INTO drill_results (
    id, project_name, activity_name, topo_level, data_date, 
    remark, original_xml, drill_config, 
    node_count, exception_count, alarm_count, created_by
) VALUES (
    'drill_example_001', 
    '示例工程1', 
    '示例活动1', 
    '2', 
    '2024-01-01',
    '这是一个示例演练结果',
    '<mxGraphModel><root><mxCell id="0"/><mxCell id="1" parent="0"/></root></mxGraphModel>',
    '{"exceptions":{"node1":{"delayTime":30,"reason":"测试异常"}},"calculatedResults":{"node1":{"nodeId":"node1","nodeName":"测试节点","originalStartTime":"09:00:00","originalEndTime":"10:00:00","newStartTime":"09:00:00","newEndTime":"10:30:00","delayTime":30,"isTimePointAlarm":true}}}',
    1, 1, 1, 'admin'
);

INSERT INTO drill_results (
    id, project_name, activity_name, topo_level, data_date, 
    remark, original_xml, drill_config, 
    node_count, exception_count, alarm_count, created_by
) VALUES (
    'drill_example_002', 
    '示例工程1', 
    '示例活动1', 
    '2', 
    '2024-01-02',
    '这是第二个示例演练结果',
    '<mxGraphModel><root><mxCell id="0"/><mxCell id="1" parent="0"/></root></mxGraphModel>',
    '{"exceptions":{},"calculatedResults":{"node1":{"nodeId":"node1","nodeName":"测试节点","originalStartTime":"09:00:00","originalEndTime":"10:00:00","newStartTime":"09:00:00","newEndTime":"10:00:00","delayTime":0,"isTimePointAlarm":false}}}',
    1, 0, 0, 'admin'
);

INSERT INTO drill_results (
    id, project_name, activity_name, topo_level, data_date, 
    remark, original_xml, drill_config, 
    node_count, exception_count, alarm_count, created_by
) VALUES (
    'drill_example_003', 
    '示例工程2', 
    '示例活动2', 
    '3', 
    '2024-01-01',
    '不同工程的演练结果',
    '<mxGraphModel><root><mxCell id="0"/><mxCell id="1" parent="0"/></root></mxGraphModel>',
    '{"exceptions":{"node2":{"delayTime":15,"reason":"轻微延迟"}},"calculatedResults":{"node2":{"nodeId":"node2","nodeName":"另一个节点","originalStartTime":"14:00:00","originalEndTime":"15:00:00","newStartTime":"14:00:00","newEndTime":"15:15:00","delayTime":15,"isTimePointAlarm":false}}}',
    1, 1, 0, 'admin'
);
