<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <Properties>

        <Property name="LOG_HOME">${sys:user.dir}/log</Property>

        <Property name="LOG_FILE1">server.log</Property>

        <Property name="LOG_FILE2">server_exp.log</Property>

        <Property name="LOG_FILE3">toposerver.log</Property>

        <Property name="LOG_FILE4">warning.log</Property>

        <Property name="LOG_FILE5">warnSysLog.log</Property>

        <Property name="LOG_FILE6">monitor.log</Property>

        <Property name="LOG_FILE7">methodRunTimeLog.log</Property>

        <Property name="LOG_MQ_ERROR_MESSAGE">mqErrorMessage.log</Property>

    </Properties>

    

    <Appenders>

   		<Console name="console_appender" target="SYSTEM_OUT">

           <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n"/>

        </Console>

        <!-- server root日志 -->

        <RollingFile  name="server" immediateFlush="false" fileName="${LOG_HOME}/${LOG_FILE1}"

                      filePattern="${LOG_HOME}/${LOG_FILE1}.%i">

            <!-- <Filters>

	      	  	过滤器：只允许级别为error的日志通过

	          	<ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>

	       	</Filters> -->

            <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

             	<!--默认打印周期为one day-->

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

        		<SizeBasedTriggeringPolicy size="100MB"/>

            </Policies>

            <!--保存日志个数为n-->

            <DefaultRolloverStrategy max="100"/>

        </RollingFile > 

        

        <RollingFile  name="server_exp" immediateFlush="false" fileName="${LOG_HOME}/${LOG_FILE2}"

                      filePattern="${LOG_HOME}/${LOG_FILE2}.%i">

            <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

        		<SizeBasedTriggeringPolicy size="100MB"/>

            </Policies>

            <DefaultRolloverStrategy max="100"/>

        </RollingFile >

        

        <RollingFile  name="toposerver" immediateFlush="false" fileName="${LOG_HOME}/${LOG_FILE3}"

                      filePattern="${LOG_HOME}/${LOG_FILE3}.%i">

            <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

        		<SizeBasedTriggeringPolicy size="100MB"/>

            </Policies>

            <DefaultRolloverStrategy max="100"/>

        </RollingFile >

        

        <RollingFile  name="warning" immediateFlush="false" fileName="${LOG_HOME}/${LOG_FILE4}"

                      filePattern="${LOG_HOME}/${LOG_FILE4}.%i">

            <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

        		<SizeBasedTriggeringPolicy size="10MB"/>

            </Policies>

            <DefaultRolloverStrategy max="10"/>

        </RollingFile >

        

        <RollingFile  name="warnsyslog" immediateFlush="false" fileName="${LOG_HOME}/${LOG_FILE5}"

                      filePattern="${LOG_HOME}/${LOG_FILE5}.%i">

            <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

        		<SizeBasedTriggeringPolicy size="10MB"/>

            </Policies>

            <DefaultRolloverStrategy max="10"/>

        </RollingFile >

        

        <RollingFile  name="monitor" immediateFlush="false" fileName="${LOG_HOME}/${LOG_FILE6}"

                      filePattern="${LOG_HOME}/${LOG_FILE6}.%i">

            <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

        		<SizeBasedTriggeringPolicy size="10MB"/>

            </Policies>

            <DefaultRolloverStrategy max="10"/>

        </RollingFile >

        

        <RollingFile  name="methodRunTimeLog" immediateFlush="false" fileName="${LOG_HOME}/${LOG_FILE7}"

                      filePattern="${LOG_HOME}/${LOG_FILE7}.%i">

            <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

                <SizeBasedTriggeringPolicy size="200MB"/>

            </Policies>

            <DefaultRolloverStrategy max="50"/>

        </RollingFile >

        <RollingFile  name="mqErrorMessage" immediateFlush="false" fileName="${LOG_HOME}/${LOG_MQ_ERROR_MESSAGE}"

                      filePattern="${LOG_HOME}/${LOG_MQ_ERROR_MESSAGE}">


        <PatternLayout>

                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %c{1}: %msg%xEx%n</pattern>

            </PatternLayout>

            <Policies>

                <!-- <TimeBasedTriggeringPolicy modulate="true" interval="1"/> -->

        		<SizeBasedTriggeringPolicy size="200MB"/>

            </Policies>

            <DefaultRolloverStrategy max="50"/>

        </RollingFile >    

    </Appenders>



   <Loggers>

		<logger name="dbSRblog" additivity="false" >

	   		<appender-ref ref="server_exp" level="DEBUG"/>

	   </logger>

	   <logger name="topoLog" additivity="false" >

	   		<AppenderRef  ref="toposerver" level="DEBUG" />

	   </logger>

	   <logger name="sysLog" additivity="false" >

	   		<AppenderRef  ref="warning" level="DEBUG" />

	   </logger>

	   <logger name="warnSysLog" additivity="false" >

	   		<AppenderRef  ref="warnsyslog" level="DEBUG" />

	   </logger>

	   <logger name="monitorsyslog" additivity="false" >

	   		<AppenderRef  ref="monitor" level="DEBUG" />

	   </logger>

	   <logger name="methodRunTimeLog" additivity="false" >

	   		<AppenderRef  ref="methodRunTimeLog" level="DEBUG" />

	   </logger>


       <logger name="mqMessage" additivity="false" >

           <AppenderRef  ref="mqErrorMessage" level="INFO" />

       </logger>

	   <!--不需要打印location信息-->

	   <root level="INFO" includeLocation="false" additivity="false" >

	   		<appender-ref ref="console_appender" level="INFO"/>

	   		<appender-ref ref="server" level="INFO"/>

       </root>

    </Loggers>


</configuration>