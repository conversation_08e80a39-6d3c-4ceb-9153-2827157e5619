#接口主url
apiurl=/aoms/scriptplatform.do
#1.获取token的url
token=/aoms/getScriptToken.do
[query]
#2.获取业务类别列表url
getBussList=getBussList
#3.获取作业列表
getScriptList=getScriptList
#4.获取作业详细信息
getScriptInfo=getScriptInfo
#5.获取agent列表
getAgentList=getAgentList
#7.获取常用任务列表
getCommonTaskList=getCommonTaskList
#9.获取作业运行流程信息
monitorFlow=monitorFlow
#10.获取作业流程下活动列表
monitorActivities=monitorActivities
#11.获取活动下实例列表
monitorActivityInstances=monitorActivityInstances
#12.获取实例日志信息
monitorActivityInstanceLog=monitorActivityInstanceLog
#13.获取常用任务详细信息
#getCommonTaskInfo=getCommonTaskInfo
#15.获取三级类别列表
getThreeTypeList=getThreeTypeList
#16.获取任务流程状态详情
monitorActivityForFlowIdLog=monitorActivityForFlowIdLog
#17.获取常用任务详细明细信息
getCommonTaskInfoWithDetail=getCommonTaskInfoWithDetail

[start]
#6.启动脚本
startScript=startScript
#8.启动常用任务
startCommonTask=startCommonTask
#14.启动常用任务支持可变参数
startCommonTaskByConfig=startCommonTaskByConfig
