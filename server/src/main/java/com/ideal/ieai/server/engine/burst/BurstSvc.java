package com.ideal.ieai.server.engine.burst;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.engine.burst.basic.BGroup;
import com.ideal.ieai.server.engine.burst.basic.BSpace;
import com.ideal.ieai.server.engine.burst.basic.BurstMap;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.actExcelToRun.ActExcelToRunData;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <ul>
 * <li>Title: BurstSvc.java</li>
 * <li>Description:分片的服务层</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2018年10月24日
 */
public class BurstSvc
{
    static Logger                       _log                     = Logger.getLogger(BurstSvc.class);
    
    //----------------------------001 ExcelModelExec轮询时调用时,检查该步骤的分片信息。是否允许该步骤运行------------------------
    //---------------------------------------------------------------------------------------------------------
    /**
     * <li>Description:：过滤分片步骤:将原本符合直接驱动的活动再次进行过滤。1.符合立即执行条件则更新该步骤的状态并调用起模板流程；2.当前步骤，未到达分片时间点</li> 
     * <AUTHOR>
     * 2018年10月24日 
     * @param execActs
     * return void
     */
    public static void filterBurstStep ( List execActs )
    {
        List removeList = new ArrayList();
        if (null != execActs)
        {
            ActExcelToRunData execAct = null;
            for (int i = 0; i < execActs.size(); i++)
            {
                execAct = (ActExcelToRunData) execActs.get(i);
                //只有应用变更 和 应用维护的获得才使用分片规则，其他模块不适用
                if (
                        ("3".equals(execAct.getSysType()) &&(execAct.getMxgraphId()==null ) )||
                        ("16".equals(execAct.getSysType()) &&(execAct.getMxgraphId()==null ) ) 
                    )
                {
                    //应用维护重新执行的活动不做分片判断
                    if(execAct.getGourpiid()==-1&&execAct.getSpaceiid()==-1&&  ("16".equals(execAct.getSysType()) &&(execAct.getMxgraphId()==null ) ))
                    {
                        continue;
                    }
                    if(ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_SUS_CHECKLIST_SWITCH, false) && execAct.isCheckFlow()){
                        continue;
                    }
                    if (!isRunningStep(execAct))
                    {
                        removeList.add(execAct);
                    }                    
                }
            }
        }
        
        
        for (int i = 0; i < removeList.size(); i++)
        {
            execActs.remove(removeList.get(i));
        }
    }

    /**
     * <li>Description:判断当前步骤是否是可运行步骤</li> 
     * <AUTHOR>
     * 2018年10月25日 
     * @param actExcelToRunData
     * @return
     * return boolean
     */
    private static boolean isRunningStep ( ActExcelToRunData actExcelToRunData )
    {
        return isRunningGourp(actExcelToRunData.getSpaceiid(),
            actExcelToRunData.getGourpiid());
    }


    /**
     * <li>Description:判断当前活动对应在 分片组中进行查找</li> 
     * <AUTHOR>
     * 2018年10月27日 
     * @param spaceiidOfAct
     * @param gourpiidOfAct
     * @return
     * return boolean
     */
    public static boolean isRunningGourp ( Long spaceiidOfAct, Long gourpiidOfAct )
    {
        boolean isRunningGroup = false;
        List<Long> runGroupList = getRunningGroupsOfSpace(spaceiidOfAct);
        if (null != runGroupList)
        {
            for (int i = 0; i < runGroupList.size(); i++)
            {
                //如果步骤中的spaceiid取空时，则直接通过校验
                if (null == spaceiidOfAct || new Long(0).equals(spaceiidOfAct)
                        || new Long(-1).equals(spaceiidOfAct))
                {
                    isRunningGroup = true;
                    break;
                } else if (gourpiidOfAct.equals(runGroupList.get(i)))
                {
                    isRunningGroup = true;
                    break;
                }

            }
        }
        
/*        if (null == runGroupList || runGroupList.size() == 0)
        {
            isRunningGroup=true;
        }*/
        return isRunningGroup;
    }

    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2018年10月24日 
     * @param spaceiid
     * @return
     * return long
     */
    public static List<Long> getRunningGroupsOfSpace ( Long spaceiid )
    {
        List<Long> runningGroup = new ArrayList<Long>();
        BSpace bSpace = BurstMap.get(spaceiid);
        if (null != bSpace)
        {
            List<BGroup> groupsList = bSpace.getGroupsList();
            if (null != groupsList)
            {
                for (int i = 0; i < groupsList.size(); i++)
                {
                    if (BGroup.BGROUP_STATE_RUNNING==groupsList.get(i).getState()||BGroup.BGROUP_STATE_FINISHED==groupsList.get(i).getState())
                    {
                        if (groupsList.get(i).getTimesliceStart() <System.currentTimeMillis())
                        {                            
                            runningGroup.add(groupsList.get(i).getIgroupiid());
                        }
                    }
                }
            }
        }
        return runningGroup;
    }


    
    //----------------------------002 启动时,保存分片信息------------------------------------------------------------
    //---------------------------------------------------------------------------------------------------------
    /**
     * <li>Description:流程启动时，分片操作；设置每个步骤所属的空间和片组;并对生成分片信息并保存</li> 
     * <AUTHOR>
     * 2018年10月24日 
     * @param burstBeginIdx     从List中截取数据时，起始索引编号
     * @param pubWorkItemsBean  从中原始对象中，获得分片信息，如 “并发数” 和"间隔时间"
     * @param steps             当成已经生成步骤的信息
     * return void
     * @throws RepositoryException 
     */
    public static void createBurstInfoAndSave ( int burstBeginIdx,
            ActExcelToRunData pubWorkItemsBean, List<ActExcelToRunData> steps,
            long itaskid, Connection conn ) throws RepositoryException
           
    {
        
        long lConcrrency = -1;
        long lInteraltime = -1;
        long goupiid = getGroupIid(conn);
        long spaceiid = getSpaceiid(conn);
        List<Long> groupiidList=new ArrayList<Long>();
        groupiidList.add(goupiid);
        
        
        String concurrency   =pubWorkItemsBean.getIconcurrency();    //并发数
        String iintervaltime =pubWorkItemsBean.getIintervaltime();   //时间间隔
        if (null != concurrency && !"".equals(concurrency))
        {
            try{                
                lConcrrency = Long.valueOf(concurrency);
            }catch(Exception e){
                e.printStackTrace();
                _log.error(e.toString());
            }
        }
        
        
        if (null!=iintervaltime && !"".equals(iintervaltime))
        {
            try
            {                
                lInteraltime=Long.valueOf(iintervaltime);
            } catch (Exception e)
            {
                e.printStackTrace();
                _log.error(e.toString());
            }
        }
        
        
        //分片算法
        long idx = 0;                           //分片变量
        if (null != steps)
        {
            for (int i = burstBeginIdx; i < steps.size(); i++)
            {
                if (lConcrrency == idx)
                {
                    idx = 0;                    //重新清零
                    goupiid = getGroupIid(conn);
                    groupiidList.add(goupiid);
                }
                idx++;
                steps.get(i).setSpaceiid(spaceiid);
                steps.get(i).setGourpiid(goupiid);
            }
        }
        saveSpaceAndGoup( spaceiid, lInteraltime,groupiidList,itaskid,conn);
    }

    private static long getSpaceiid ( Connection conn ) throws RepositoryException
    {
        long iid = 0;
        iid=IdGenerator.createIdNoConnection("IEAI_BURST_SPACE",  Constants.IEAI_SUS);

        return iid;
    }

    private static long getGroupIid ( Connection conn ) throws RepositoryException
    {
        
        long iid = 0;
        iid=IdGenerator.createIdNoConnection("IEAI_BURST_BGROUP",  Constants.IEAI_SUS);

        return iid;
    }

    
    private static void saveSpaceAndGoup(long spaceiid,long lInteraltime,List<Long> groupiidList,long itaskid,Connection conn) throws RepositoryException{
        String insertSpaceSQL="INSERT INTO IEAI_BURST_SPACE(IID,IINTERVALTIME,IGROUPCNT,ISTATE,ITASKID)VALUES(?,?,?,?,?)";
        String insertGroupSQL="INSERT INTO IEAI_BURST_BGROUP(IID,IEAI_BURST_SPACE_IID,IFRONT,ISTATE)VALUES(?,?,?,?)";
        
        PreparedStatement spaceStmt=null;
        PreparedStatement groupStmt=null;
        long ifront=-1;
        try
        {
            
            int idx=1;
            spaceStmt = conn.prepareStatement(insertSpaceSQL);
            spaceStmt.setLong(idx++, spaceiid);
            spaceStmt.setLong(idx++, lInteraltime);
            spaceStmt.setLong(idx++, Long.valueOf(groupiidList.size()));
            spaceStmt.setLong(idx++, -1);
            spaceStmt.setLong(idx++, itaskid);
            spaceStmt.executeUpdate();
            
            groupStmt =conn.prepareStatement(insertGroupSQL);
            for (int i = 0; i < groupiidList.size(); i++)
            {
                groupStmt.setLong(1, groupiidList.get(i));
                groupStmt.setLong(2, spaceiid);
                groupStmt.setLong(3, ifront);
                groupStmt.setLong(4, -1);
                groupStmt.addBatch();
                ifront =groupiidList.get(i);
            }
            groupStmt.executeBatch();
        } catch (SQLException e)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        }finally{
            DBResource.closePreparedStatement(spaceStmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePreparedStatement(groupStmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
    }
    
    
    /**
     * <li>Description:"废弃的方法，不在使用。"</li> 
     * <AUTHOR>
     * 2019年7月1日 
     * @param instanceiid
     * @param serverip
     * @param itaskid
     * @param conn
     * @throws RepositoryException
     * return void
     */
    public static void updateSpaceInstanceiidAndServerIP(long instanceiid,String serverip,long itaskid,Connection conn) throws RepositoryException{
/*        String insertSpaceSQL="UPDATE IEAI_BURST_SPACE  SET IEAI_INSTANCEINFO_IID=? , ISERVERIP=? WHERE ITASKID=?";
        PreparedStatement spaceStmt=null;
        try
        {
            
            int idx=1;
            spaceStmt = conn.prepareStatement(insertSpaceSQL);
            spaceStmt.setLong(idx++, instanceiid);
            spaceStmt.setString(idx++, serverip);
            spaceStmt.setLong(idx++, itaskid);
            spaceStmt.executeUpdate();
        } catch (SQLException e)
        {
            e.printStackTrace();
            DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        }finally{
            DBResource.closePreparedStatement(spaceStmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }*/
    }
    private static void saveBGoup(long spaceiid,long lInteraltime,List<Long> groupiidList,Connection conn) throws RepositoryException{
        String insertGroupSQL="INSERT INTO IEAI_BURST_BGROUP(IID,IEAI_BURST_SPACE_IID,IFRONT,ISTATE)VALUES(?,?,?,?)";
        
        PreparedStatement groupStmt=null;
        long ifront=-1;
        try
        {
            groupStmt =conn.prepareStatement(insertGroupSQL);
            for (int i = 0; i < groupiidList.size(); i++)
            {
                groupStmt.setLong(1, groupiidList.get(i));
                groupStmt.setLong(2, spaceiid);
                groupStmt.setLong(3, ifront);
                groupStmt.setLong(4, -1);
                groupStmt.addBatch();
                ifront =groupiidList.get(i);
            }
            groupStmt.executeBatch();
        } catch (SQLException e)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        }finally{
            DBResource.closePreparedStatement(groupStmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
    }
    
    //----------------------------003 BurstThread时刻维护BurstMap中的数据-------------------------------------------
    //---------------------------------------------------------------------------------------------------------
    /**
     * <li>Description:查询未完成状态的分片数据</li> 
     * <AUTHOR>
     * 2018年10月25日 
     * @throws RepositoryException
     * return void
     */
    public static List<BSpace> queryUnfinsedSpace() throws RepositoryException{
        Map<Long, BSpace> spaceMap = new LinkedHashMap<Long, BSpace>();
        List<BSpace> dataList=new ArrayList<BSpace>();
        String querySql="  SELECT SG.* "
               + "      FROM   "
               + "             ("
               + "               SELECT R.ISERVERIP ,RF.ISPACEIID,RF.IGOURPIID"
               + "                FROM IEAI_RUN_INSTANCE R,IEAI_RUNINFO_INSTANCE RF "
               + "              WHERE R.IID =RF.IRUNINSID"
               + "               AND r.ISERVERIP=?"
               + "               GROUP BY R.ISERVERIP ,RF.ISPACEIID,RF.IGOURPIID ,ISERVERIP"
               + "              ) R,     "
               + "              ("
               + "                 SELECT  G.IID,IEAI_BURST_SPACE_IID,G.ISTATE,S.ISTATE AS SPACE_ISTATE, G.ITIMESLICE_START,"
               + "                            G.ITIMESLICE_END,S.IINTERVALTIME,G.IFRONT,S.IGROUPCNT"
               + "                    FROM IEAI_BURST_SPACE S,IEAI_BURST_BGROUP G "
               + "                   WHERE S.IID =G.IEAI_BURST_SPACE_IID AND S.ISTATE!=2"
               + "                   ORDER BY IID ASC"
               + "               )SG   "
               + "      WHERE R.ISPACEIID =SG.IEAI_BURST_SPACE_IID"
               + "  AND R.IGOURPIID=SG.IID";
        
        

        Connection conn=null;
        PreparedStatement stmt=null;
        ResultSet rs=null;
        
        try
        {
            String ip  = "";
            conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
            try
            {
                ip  = Environment.getInstance().getServerIP();
            } catch (Exception e)
            {
               e.printStackTrace();
            }
            stmt= conn.prepareStatement(querySql);
            stmt.setString(1, ip);
            rs =stmt.executeQuery();
            long igroupiid =-1;
            long space_iid=-1;
            long frontGroupiid=-1;
            long iintervaltime=0;//时间间隔
            long groupCnt=0;
            int istate=-1;
            int s_istate=-1;

            BSpace bSpace =null;
            BGroup bGroup=null;
/*            DbOpScheduler opScheduler = new DbOpScheduler();
            List list = opScheduler.getExcelPendingActs(ip);
            Map<Long, Long> filter=list2map(list);*/
            while (rs.next())
            {
                igroupiid= rs.getLong("IID");
                space_iid= rs.getLong("IEAI_BURST_SPACE_IID");
                istate= rs.getInt("ISTATE");
                s_istate=rs.getInt("SPACE_ISTATE");
                iintervaltime=rs.getLong("IINTERVALTIME");
                frontGroupiid =rs.getLong("IFRONT");
                groupCnt =rs.getLong("IGROUPCNT");
                
/*                Long tempSpceiid= filter.get(space_iid);
                if (null==tempSpceiid)
                {
                    continue;
                }*/
                bGroup = new BGroup();
                bGroup.setIgroupiid(igroupiid);
                bGroup.setState(istate);
                bGroup.setIintervaltime(iintervaltime);
                bGroup.setFrontGroupiid(frontGroupiid);
                bGroup.setTimesliceStart(rs.getLong("ITIMESLICE_START"));
                bGroup.setTimesliceEnd(rs.getLong("ITIMESLICE_END"));
                
                bSpace=spaceMap.get(space_iid);
                if (null==bSpace)
                {
                    bSpace= new BSpace();
                    bSpace.setIid(space_iid);
                    bSpace.setIintervaltime(iintervaltime);
                    bSpace.setGroupCnt(groupCnt);
                    dataList.add(bSpace);
                    spaceMap.put(space_iid, bSpace);
                }
                bSpace.setState(s_istate);
                
                bGroup.setbSpace(bSpace);
                bSpace.getGroupsList().add(bGroup);
            }
            
        } catch (SQLException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        
        return dataList;
    }
    
    private static Map<Long,Long> list2map(List list){
        Map<Long,Long> filterMap=new HashMap<Long, Long>();
        if (null!=list)
        {
            ActExcelToRunData bean=null;
            for (int i = 0; i < list.size(); i++)
            {
                Object obj= list.get(i);
                if (null!=obj && obj instanceof ActExcelToRunData)
                {
                    bean =(ActExcelToRunData)obj;
                    bean.getSpaceiid();
                    filterMap.put(bean.getSpaceiid(), bean.getSpaceiid());
                }
            }
        }
        return filterMap;
    }
    /**
     * <li>Description:查询当前Map中数据是否是垃圾数据</li> 
     * <AUTHOR>
     * 2018年10月25日 
     * @throws RepositoryException
     * return void
     */
    public static boolean isGarbageBgroup(Long groupiid) throws RepositoryException{
        String querySql="SELECT COUNT(IID)AS CNT FROM IEAI_RUNINFO_INSTANCE_HIS RF WHERE RF.IGOURPIID=?";
        Connection conn=null;
        PreparedStatement stmt=null;
        ResultSet rs=null;
        boolean flag =false;
        
        try
        {
            conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
            stmt= conn.prepareStatement(querySql);
            stmt.setLong(1, groupiid);
            rs =stmt.executeQuery();
            long cnt=-1;
            while (rs.next())
            {
                cnt=rs.getLong("CNT");
            }
            
            if (cnt>0) //运行步骤表中已经不存在该空间的信息对应的步骤了，已经被准一至历史表中了
            {
                flag=true;
            }
        } catch (SQLException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return flag;
    }
    /**
     * <li>Description:更新当前组的状态为完成；更新后继获得的时间片的开始时间和结束时间</li> 
     * <AUTHOR>
     * 2018年10月26日 
     * @param ispaceiid 
     * @param igoupiid     片组id
     * @param timeinterval 时间间隔
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public static boolean updateGroupFinsiedAndPanddingsRun(long ispaceiid,long igoupiid,long timeinterval) throws RepositoryException{
        String updateCurGoupStateFinshed="UPDATE IEAI_BURST_BGROUP G SET G.ISTATE=? ,G.ITIMESLICE_END=? WHERE G.IID=? AND G.IEAI_BURST_SPACE_IID=?";
        String updatePandingGoupStateRun="UPDATE IEAI_BURST_BGROUP G SET G.ISTATE=?,G.ITIMESLICE_START=? WHERE G.IFRONT=? AND G.IEAI_BURST_SPACE_IID=?";
        
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            Connection conn=null;
            PreparedStatement fishedStat = null;
            PreparedStatement runStat = null;
            try
            {
                    // 数据入库
                    try
                    {
                        Date beginDate=new Date();
                        long timeSliceBegin=beginDate.getTime();
                        long timeSliceEnd =getTimeSliceEnd( beginDate , String.valueOf(timeinterval));
                        //更新当前片组状态为完成
                        conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
                        fishedStat = conn.prepareStatement(updateCurGoupStateFinshed);
                        fishedStat.setLong(1, BGroup.BGROUP_STATE_FINISHED);
                        fishedStat.setLong(2, timeSliceBegin );
                        fishedStat.setLong(3, igoupiid);
                        fishedStat.setLong(4, ispaceiid);
                        fishedStat.executeUpdate();
                        
                        
                        
                        
                        //更新后继为运行
                        int paddingGroup_State=BGroup.BGROUP_STATE_RUNNING;
                        boolean isRuninfoStepDel=isRuninfoStepDeleted(ispaceiid, igoupiid);
                        if (isRuninfoStepDel)
                        {
                            paddingGroup_State=BGroup.BGROUP_STATE_FINISHED;    //特殊情况处理，IEAI_RUNINFO_INSTANCE 记录未运行而就已经被删除，而IEAI_BURST_BGROUP还是为处理;防止分片后继卡死
                        }
                        
                        
                        runStat = conn.prepareStatement(updatePandingGoupStateRun);
                        runStat.setLong(1, paddingGroup_State);
                        runStat.setLong(2, timeSliceEnd );
                        runStat.setLong(3, igoupiid);
                        runStat.setLong(4, ispaceiid);
                        runStat.executeUpdate();
                        conn.commit();
                        flag = true;
                    } catch (SQLException e)
                    {
                        flag = false;
                        e.printStackTrace();
                        _log.error("saveAnalysisDataIntoDb is error ! " + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            } finally
            {
                DBResource.closePreparedStatement(fishedStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                DBResource.closePreparedStatement(runStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            }
        }
        return flag;
    }
    
    
    
    /**
     * <li>Description:检查运行表中的数据是否已经被删除(部署准备)</li> 
     * <AUTHOR>
     * 2018年10月25日 
     * @throws RepositoryException
     * return void
     */
    public static boolean isRuninfoStepDeleted(long ispaceiid,long igourpiid) throws RepositoryException{
        String querySql="     SELECT COUNT(IID)AS CNT FROM IEAI_RUNINFO_INSTANCE RF WHERE RF.ISPACEIID=? AND RF.IGOURPIID=?";
        Connection conn=null;
        PreparedStatement stmt=null;
        ResultSet rs=null;
        
        boolean isDelete=false;
        try
        {
            conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
            stmt= conn.prepareStatement(querySql);
            stmt.setLong(1, ispaceiid);
            stmt.setLong(2, igourpiid);
            rs =stmt.executeQuery();
            int cnt =-1;

            while (rs.next())
            {
                cnt= rs.getInt("CNT");
                if (0==cnt)
                {
                    isDelete=true;
                }
            }
            
        } catch (SQLException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        
        return isDelete;
    }
    
    public static boolean updateFirstRun(long ispaceiid,long igoupiid,long timeinterval) throws RepositoryException{
        String updateCurSpaceStateRun   ="UPDATE IEAI_BURST_SPACE S SET S.ISTATE=? WHERE S.IID=?";
        String updateFirstGroupObjStateRun="UPDATE IEAI_BURST_BGROUP G SET G.ISTATE=?,G.ITIMESLICE_START=? WHERE G.IID=?";
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            Connection conn=null;
            PreparedStatement runStat = null;
            PreparedStatement runSpaceStat = null;
            try
            {
                    // 数据入库
                    try
                    {
                        int idx=1;
                        conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
                        runSpaceStat= conn.prepareStatement(updateCurSpaceStateRun);
                        runSpaceStat.setLong(idx++, BSpace.BSPACE_STATE_RUNNING);
                        runSpaceStat.setLong(idx++, ispaceiid);
                        runSpaceStat.executeUpdate();
                        
                        int gIdx=1;
                        Date beginDate=new Date();
                        long timeSliceBegin=beginDate.getTime();
                        runStat = conn.prepareStatement(updateFirstGroupObjStateRun);
                        runStat.setLong(gIdx++, BGroup.BGROUP_STATE_RUNNING);
                        runStat.setLong(gIdx++, timeSliceBegin );
                        runStat.setLong(gIdx++, igoupiid);
                        runStat.executeUpdate();
                        conn.commit();
                        flag = true;
                    } catch (SQLException e)
                    {
                        flag = false;
                        e.printStackTrace();
                        _log.error("updateRun is error ! " + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            } finally
            {
                DBResource.closePreparedStatement(runStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                DBResource.closePreparedStatement(runSpaceStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            }
        }
        return flag;
    }
    
    
    public static boolean updateSpaceFinshed(long ispaceiid) throws RepositoryException{
        String updatePandingGoupStateRun="UPDATE IEAI_BURST_SPACE G SET G.ISTATE=? WHERE G.IID=?";
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            Connection conn=null;
            PreparedStatement finishedStat = null;
            try
            {
                    // 数据入库
                    try
                    {
                        int gIdx=1;
                        conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
                        finishedStat = conn.prepareStatement(updatePandingGoupStateRun);
                        finishedStat.setLong(gIdx++, BGroup.BGROUP_STATE_FINISHED);
                        finishedStat.setLong(gIdx++, ispaceiid);
                        finishedStat.executeUpdate();
                        conn.commit();
                        flag = true;
                    } catch (SQLException e)
                    {
                        flag = false;
                        e.printStackTrace();
                        _log.error("updateSpaceFinshed is error ! " + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            } finally
            {
                DBResource.closePreparedStatement(finishedStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            }
        }
        return flag;
    }
    
    /**
     * <li>Description:更新 Group状态为完成</li> 
     * <AUTHOR>
     * 2018年10月28日 
     * @param ispaceiid
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public static boolean updateBGroupFinshedBySpace(long ispaceiid) throws RepositoryException{
        String updatePandingGoupStateRun="UPDATE IEAI_BURST_BGROUP G SET G.ISTATE=? WHERE G.IEAI_BURST_SPACE_IID=?";
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            Connection conn=null;
            PreparedStatement finishedStat = null;
            try
            {
                    // 数据入库
                    try
                    {
                        
                        int gIdx=1;
                        conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
                        finishedStat = conn.prepareStatement(updatePandingGoupStateRun);
                        finishedStat.setLong(gIdx++, BGroup.BGROUP_STATE_FINISHED);
                        finishedStat.setLong(gIdx++, ispaceiid);
                        finishedStat.executeUpdate();
                        conn.commit();
                        flag = true;
                    } catch (SQLException e)
                    {
                        flag = false;
                        e.printStackTrace();
                        _log.error("updateBGroupFinshed is error ! " + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            } finally
            {
                DBResource.closePreparedStatement(finishedStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            }
        }
        return flag;
    }
    
    /**
     * <li>Description:更新 Group状态为完成</li> 
     * <AUTHOR>
     * 2018年10月28日 
     * @param ispaceiid
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public static boolean updateBGroupFinshedByGroup(long igroupid) throws RepositoryException{
        String updatePandingGoupStateRun="UPDATE IEAI_BURST_BGROUP G SET G.ISTATE=? WHERE G.iid=?";
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            Connection conn=null;
            PreparedStatement finishedStat = null;
            try
            {
                    // 数据入库
                    try
                    {
                        
                        int gIdx=1;
                        conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
                        finishedStat = conn.prepareStatement(updatePandingGoupStateRun);
                        finishedStat.setLong(gIdx++, BGroup.BGROUP_STATE_FINISHED);
                        finishedStat.setLong(gIdx++, igroupid);
                        finishedStat.executeUpdate();
                        conn.commit();
                        flag = true;
                    } catch (SQLException e)
                    {
                        flag = false;
                        e.printStackTrace();
                        _log.error("updateBGroupFinshed is error ! " + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            } finally
            {
                DBResource.closePreparedStatement(finishedStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            }
        }
        return flag;
    } 
    /**
     * <li>Description:获得时间片的结束</li> 
     * <AUTHOR>
     * 2018年10月26日 
     * @param timeSliceBegin 时间片的开始时间
     * @param timeinterval   时间片的时间间隔(分钟)
     * @return
     * return long
     */
    public static long getTimeSliceEnd(Date beginDate ,String timeinterval){
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(beginDate);
        rightNow.add(Calendar.SECOND, Integer.parseInt(timeinterval));
        Date endDate=rightNow.getTime();
        return endDate.getTime();
    }
    
    public static void printDate(Date begin,Date end){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-mm-dd  HH:mm:ss");
        System.out.println(sdf.format(begin));
        System.out.println(sdf.format(end));
        
    }
    
    //-------------------------004 --------------------------------------------------------------------
    
    /**
     * <li>Description:流程启动时，分片操作；设置每个步骤所属的空间和片组;并对生成分片信息并保存</li> 
     * <AUTHOR>
     * 2018年10月24日 
     * @param burstBeginIdx     从List中截取数据时，起始索引编号
     * @param pubWorkItemsBean  从中原始对象中，获得分片信息，如 “并发数” 和"间隔时间"
     * @param steps             当成已经生成步骤的信息
     * return void
     * @throws RepositoryException 
     */
    public static void createBurstInfoAndSaveByResSave ( int burstBeginIdx,
            ActExcelToRunData pubWorkItemsBean, List<ActExcelToRunData> steps,
            Connection conn ) throws RepositoryException
    {
        List<BGroup> groupsList =new ArrayList<BGroup>();   //该空间内承载组的容器（组内各个对象有依赖关系）
        long lConcrrency = -1;
        long lInteraltime = -1;
        long spaceiid = -1;
        long goupiid=1;
        if (null!=steps)
        {
            ActExcelToRunData actExcelToRunData=null;
            for (int i = 0; i < steps.size(); i++)
            {
                try
                {
                    List<Long> groupiidList=new ArrayList<Long>();
                    actExcelToRunData=steps.get(i);
                    spaceiid=actExcelToRunData.getSpaceiid();
                    BSpace bspace=queryUnfinsedSpaceByspaceiid(spaceiid);
                    try
                    {                    
                        if(actExcelToRunData.getIconcurrency()!=null)
                        {
                            lConcrrency =Long.valueOf(actExcelToRunData.getIconcurrency());  //并发数1  
                        }else
                        {
                            lConcrrency=1;      
                        }
                    } catch (Exception e)
                    {
                        e.printStackTrace();
                        lConcrrency=1;                                                   //并发数为1
                        _log.info(e.toString());
                    }
                    if (null==bspace)
                    {
                        _log.info("该步骤已经结束，不需要再插入步骤");
                    }else {
                        groupsList =bspace.getGroupsList();
                    }
                    
                    
                    //001  获得当前最后一个group对象，判断它是否是满片，如果是满片。
                    //002  如果满片，创建新片。非满片，则将该步骤插入原有片
                    
                    //分片算法
/*                    long idx = 0;                           //分片变量
                    if (null != steps)
                    {
                        if (lConcrrency == idx)
                        {
                            idx = 0;                        //重新清零
                            goupiid = getGroupIid(conn);
                            groupiidList.add(goupiid);
                        }
                        idx++;
                        steps.get(i).setSpaceiid(spaceiid);
                        steps.get(i).setGourpiid(goupiid);
                    }*/
                    lInteraltime=bspace.getIintervaltime();
                    goupiid = getGroupIid(conn);
                    actExcelToRunData.setGourpiid(goupiid);
                    groupiidList.add(goupiid);
                    saveBGoup(spaceiid, lInteraltime,groupiidList, conn);
                } catch (Exception e)
                {
                    e.printStackTrace();
                    _log.info(e.toString());
                }

                
            }
        }
    }
    
    
    /**
     * <li>Description:查询未完成状态的分片数据</li> 
     * <AUTHOR>
     * 2018年10月25日 
     * @throws RepositoryException
     * return void
     */
    public static BSpace queryUnfinsedSpaceByspaceiid(long spaceiid) throws RepositoryException{
        BSpace bSpace =null;
        Map<Long, BSpace> spaceMap = new LinkedHashMap<Long, BSpace>();
        List<BSpace> dataList=new ArrayList<BSpace>();
        String querySql=" SELECT  G.IID,IEAI_BURST_SPACE_IID,G.ISTATE,S.ISTATE AS SPACE_ISTATE, G.ITIMESLICE_START,"+
                                 "G.ITIMESLICE_END,S.IINTERVALTIME,G.IFRONT,S.IGROUPCNT"+ 
                        "  FROM IEAI_BURST_SPACE S,IEAI_BURST_BGROUP G "+
                        " WHERE S.IID =G.IEAI_BURST_SPACE_IID AND S.IID =?"+
                        " ORDER BY IID ASC";
        Connection conn=null;
        PreparedStatement stmt=null;
        ResultSet rs=null;
        
        try
        {
            conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
            stmt= conn.prepareStatement(querySql);
            stmt.setLong(1, spaceiid);
            rs =stmt.executeQuery();
            long igroupiid =-1;
            long space_iid=-1;
            long frontGroupiid=-1;
            long iintervaltime=0;//时间间隔
            long groupCnt=0;
            int istate=-1;
            int s_istate=-1;
            
            BGroup bGroup=null;
            while (rs.next())
            {
                igroupiid= rs.getLong("IID");
                space_iid= rs.getLong("IEAI_BURST_SPACE_IID");
                istate= rs.getInt("ISTATE");
                s_istate=rs.getInt("SPACE_ISTATE");
                iintervaltime=rs.getLong("IINTERVALTIME");
                frontGroupiid =rs.getLong("IFRONT");
                groupCnt =rs.getLong("IGROUPCNT");
                
                
                bGroup = new BGroup();
                bGroup.setIgroupiid(igroupiid);
                bGroup.setState(istate);
                bGroup.setIintervaltime(iintervaltime);
                bGroup.setFrontGroupiid(frontGroupiid);
                bGroup.setTimesliceStart(rs.getLong("ITIMESLICE_START"));
                bGroup.setTimesliceEnd(rs.getLong("ITIMESLICE_END"));
                
                bSpace=spaceMap.get(space_iid);
                if (null==bSpace)
                {
                    bSpace= new BSpace();
                    bSpace.setIid(space_iid);
                    bSpace.setIintervaltime(iintervaltime);
                    bSpace.setGroupCnt(groupCnt);
                    dataList.add(bSpace);
                    spaceMap.put(space_iid, bSpace);
                }
                bSpace.setState(s_istate);
                
                bGroup.setbSpace(bSpace);
                bSpace.getGroupsList().add(bGroup);
            }
            
        } catch (SQLException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return bSpace;
    }
    
    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2018年11月17日 
     * @param spaceiid
     * @return
     * return List<Long>
     * @throws RepositoryException 
     */
    public static List<Long> getSameICONNERBySpaceIid(long spaceiid) throws RepositoryException{
        List<Long> dataList=new ArrayList<Long>();
        String querySql=
            "    SELECT DISTINCT(A.ISPACEIID) as ISPACEIID"+
            "    FROM IEAI_RUNINFO_INSTANCE A,"+
            "         (SELECT RF.ICONNER ,RF.ISPACEIID,RF.IRUNINSID,RF.IPKGNAME"+
            "        FROM IEAI_RUNINFO_INSTANCE RF WHERE RF.ISPACEIID=? "+
            "        GROUP BY RF.ICONNER,RF.ISPACEIID,IRUNINSID,IPKGNAME)B"+
            " WHERE A.ICONNER =B.ICONNER"+
            "      AND A.IRUNINSID  =B.IRUNINSID"+
            "      AND A.IPKGNAME   =B.IPKGNAME  ";
        Connection conn=null;
        PreparedStatement stmt=null;
        ResultSet rs=null;
        
        try
        {
            conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
            stmt= conn.prepareStatement(querySql);
            stmt.setLong(1, spaceiid);
            rs =stmt.executeQuery();
            long curSpaceiid=0;
            while (rs.next())
            {
                curSpaceiid=rs.getLong("ISPACEIID");
                dataList.add(curSpaceiid);
            }
            
        } catch (SQLException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return dataList;
    }
    
       /**
     * <li>Description:查询可准许运行  步骤的spaceiid.查法，最小的,可运行步骤的并发序号的iid</li> 
     * <AUTHOR>
     * 2018年11月20日 
     * @return
     * @throws RepositoryException
     * return List<Long>
     */
    public static  List<Long> getEachRunInstanceFirstRunSpace() throws RepositoryException{
       List<Long> dataList=new ArrayList<Long>();
       String querySql=
               "    SELECT Z1.ISPACEIID FROM "
               +"       IEAI_RUNINFO_INSTANCE Z1, "
               +"                   (SELECT RF.IRUNINSID , MIN(RF.ICONNER) AS ICONNER" 
               +"                      FROM IEAI_RUNINFO_INSTANCE RF " 
               +"                      WHERE RF.ISTATE!=2 AND RF.ISTATE!=3 AND RF.ISTATE!=21 " 
               +"                       GROUP BY RF.IRUNINSID )A"
               +"     WHERE Z1.IRUNINSID=A.IRUNINSID AND A.ICONNER=Z1.ICONNER"
               +"    GROUP BY Z1.ISPACEIID  ";
       Connection conn=null;
       PreparedStatement stmt=null;
       ResultSet rs=null;
       
       try
       {
           conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
           stmt= conn.prepareStatement(querySql);
           rs =stmt.executeQuery();
           long curSpaceiid=0;
           while (rs.next())
           {
               curSpaceiid=rs.getLong("ISPACEIID");
               dataList.add(curSpaceiid);
           }
           
       } catch (SQLException e)
       {
           throw new RepositoryException(ServerError.ERR_DB_QUERY);
       }finally{
           DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
           DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
       }
       return dataList;
   }

    

    /**
     * <li>Description:更新无效数据</li> 
     * <AUTHOR>
     * 2020年9月3日 
     * @throws RepositoryException
     * return void
     */
    public static void updateValidateGroup() throws RepositoryException{
        String spaceRunSql ="";
        String pandingRunSql ="";
        String finshedSql ="";
//        String testSql ="";
        
        if (DBManager.Orcl_Faimily())
        {
            spaceRunSql = "   UPDATE IEAI_BURST_SPACE S SET S.ISTATE=0  WHERE ISTATE=-1 AND S.IINTERVALTIME!=-1 AND IID IN"
                    + "(SELECT G.IEAI_BURST_SPACE_IID "
                    + "          FROM IEAI_BURST_BGROUP G  "
                    + "         LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
                    + "                                          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "         WHERE G.ISTATE = -1 "
                    + "          AND RF.IID IS NULL)";
            
             pandingRunSql = "   update IEAI_BURST_BGROUP  G SET G.ISTATE=0 WHERE G.Ifront IN(SELECT g.iid "
                    + "          FROM IEAI_BURST_BGROUP G  "
                    + "         LEFT JOIN ieai_runinfo_instance RF ON G.IID = RF.IGOURPIID "
                    + "                                          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "         WHERE G.ISTATE = -1 "
                    + "          AND RF.IID IS NULL)";
            
            
             finshedSql = "        UPDATE IEAI_BURST_BGROUP  G SET G.ISTATE=2 WHERE G.IID IN(SELECT G.IID "
                    + "        FROM IEAI_BURST_BGROUP G "
                    + "        LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
                    + "          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "        WHERE G.ISTATE = -1 " + "AND RF.IID IS NULL)";
        } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
        {
         
            spaceRunSql = "   UPDATE IEAI_BURST_SPACE S SET S.ISTATE=0  WHERE ISTATE=-1 AND S.IINTERVALTIME!=-1 AND IID IN"
                    + "(SELECT G.IEAI_BURST_SPACE_IID "
                    + "          FROM IEAI_BURST_BGROUP G  "
                    + "         LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
                    + "                                          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "         WHERE G.ISTATE = -1 "
                    + "          AND RF.IID IS NULL)";
            
             pandingRunSql = "   update IEAI_BURST_BGROUP  G SET G.ISTATE=0 WHERE G.Ifront IN(SELECT g.iid "
                    + "          FROM IEAI_BURST_BGROUP G  "
                    + "         LEFT JOIN ieai_runinfo_instance RF ON G.IID = RF.IGOURPIID "
                    + "                                          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "         WHERE G.ISTATE = -1 "
                    + "          AND RF.IID IS NULL)";
            
            
             finshedSql = "        UPDATE IEAI_BURST_BGROUP  G SET G.ISTATE=2 WHERE G.IID IN(SELECT G.IID "
                    + "        FROM IEAI_BURST_BGROUP G "
                    + "        LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
                    + "          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "        WHERE G.ISTATE = -1 " + "AND RF.IID IS NULL)";
        } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
        {
//            spaceRunSql = "   UPDATE IEAI_BURST_SPACE S SET S.ISTATE=0  WHERE ISTATE=-1 AND S.IINTERVALTIME!=-1 AND IID IN"
//                    + "(SELECT G.IEAI_BURST_SPACE_IID "
//                    + "          FROM IEAI_BURST_BGROUP G  "
//                    + "         LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
//                    + "                                          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
//                    + "         WHERE G.ISTATE = -1 "
//                    + "          AND RF.IID IS NULL)";
//            
//            pandingRunSql = "   UPDATE "
//                   + "   IEAI_BURST_BGROUP W  "
//                   + "   INNER JOIN "
//                   + "   (SELECT "
//                   + "      g.iid " 
//                   + "    FROM "
//                   + "    IEAI_BURST_BGROUP G  " 
//                   + "   LEFT JOIN ieai_runinfo_instance RF  " 
//                   + "          ON G.IID = RF.IGOURPIID AND  "
//                   + "           G.IEAI_BURST_SPACE_IID =RF.ISPACEIID  " 
//                   + "   WHERE  "
//                   + "          G.ISTATE = -1 AND  "
//                   + "   RF.IID IS NULL) S  "
//                   + "   SET  "
//                   + "      W.ISTATE=0  " 
//                   + "   WHERE  "
//                   + "       W.Ifront =S.IID  ";
//            
//
//            
//            finshedSql = "    UPDATE IEAI_BURST_BGROUP  G"
//                    + "         INNER JOIN (SELECT G.IID "
//                    + "                         FROM IEAI_BURST_BGROUP G "
//                    + "                        LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
//                    + "                            AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
//                    + "                          WHERE G.ISTATE = -1 "
//                    + "                             AND RF.IID IS NULL) S"
//                    + "        SET G.ISTATE=2 " + "       WHERE G.IID ";
            
            spaceRunSql = "   UPDATE IEAI_BURST_SPACE S SET S.ISTATE=0  WHERE ISTATE=-1 AND S.IINTERVALTIME!=-1 AND IID IN"
                    + "(SELECT G.IEAI_BURST_SPACE_IID "
                    + "          FROM IEAI_BURST_BGROUP G  "
                    + "         LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
                    + "                                          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "         WHERE G.ISTATE = -1 "
                    + "          AND RF.IID IS NULL)";
            
             pandingRunSql = "   update IEAI_BURST_BGROUP  G SET G.ISTATE=0 WHERE G.Ifront IN( select A.iid from (SELECT g.iid "
                    + "          FROM IEAI_BURST_BGROUP G  "
                    + "         LEFT JOIN ieai_runinfo_instance RF ON G.IID = RF.IGOURPIID "
                    + "                                          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "         WHERE G.ISTATE = -1 "
                    + "          AND RF.IID IS NULL) A)";
            
            
             finshedSql = "        UPDATE IEAI_BURST_BGROUP  G SET G.ISTATE=2 WHERE G.IID IN(select A.iid from (SELECT G.IID "
                    + "        FROM IEAI_BURST_BGROUP G "
                    + "        LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
                    + "          AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
                    + "        WHERE G.ISTATE = -1 " + "AND RF.IID IS NULL) A)";
//            testSql =  "       SELECT G.IID ,RF.IID as iid2"
//                    + "                         FROM IEAI_BURST_BGROUP G "
//                    + "                        LEFT JOIN IEAI_RUNINFO_INSTANCE RF ON G.IID = RF.IGOURPIID "
//                    + "                            AND G.IEAI_BURST_SPACE_IID =RF.ISPACEIID "
//                    + "                          WHERE G.ISTATE = -1  AND RF.IID IS NULL";
        }
        

        Connection conn=null;
        PreparedStatement spaceStmt=null;
        PreparedStatement runStmt=null;
        PreparedStatement stmt=null;
        
        try
        {
            conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,Constants.IEAI_SUS);
            
            spaceStmt= conn.prepareStatement(spaceRunSql);
            spaceStmt.executeUpdate();
            
            runStmt= conn.prepareStatement(pandingRunSql);
            runStmt.executeUpdate();
            
//            List<ResultBean> rblist=SQLUtil.executeSqlReturnListParam(conn, testSql, new ArrayList<Object>(), new ResultBean());
//            for(ResultBean rb:rblist)
//            {
//                _log.info("test id:"+rb.getIid()+" | iid2:"+rb.getIid2());
//            }
            
            stmt= conn.prepareStatement(finshedSql);
            stmt.executeUpdate();
            
            conn.commit();
        } catch (SQLException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePreparedStatement(spaceStmt,  Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePreparedStatement(runStmt,  Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePSConn(conn, stmt,  Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
    }
}
