package com.ideal.ieai.server.jobscheduling.repository.actmonitor;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.shellcmd.URLReplace;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.flowstart.WorkflowColumnGenerator;
import com.ideal.ieai.server.repository.hd.shellinfo.ShellInfoBean;
import com.ideal.ieai.server.repository.hd.shellinfo.ShellInfoManager;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;

/**
 * 
 * <ul>
 * <li>Title: ActMonitorManager.java</li>
 * <li>Description: 作业调度首页监控数据准备</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2016年9月21日
 */

public class ActMonitorManager
{

    private static final Logger log = Logger.getLogger(ActMonitorManager.class);
    
    private static final String METHODSTR = "method :"; 
    
    private static final String DATEFORMAT = "yyyy-MM-dd HH:mm:ss";

     private static ActMonitorManager intance = new ActMonitorManager();

     public static ActMonitorManager getInstance ()
    {
        if (intance == null)
        {
            intance = new ActMonitorManager();
        }
        return intance;
    }
    
    public Map getPrjName ( ActBean actBean ) throws RepositoryException
    {
        Map map = new HashMap();
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;

        String queryPrjNameSQL = "";

       
            queryPrjNameSQL = "SELECT DISTINCT IID,IPRJ.INAME,IUPPERID,IPRJ.ILATESTID FROM IEAI_PROJECT IPRJ,( \n"
                   +" SELECT PRJ.INAME, IPROID \n"
                   +"     FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP ,IEAI_PROJECT PRJ \n"
                   +"     WHERE UR.IROLEID=R.IID  \n"
                   +"     AND R.IID=SP.IROLEID  \n"
                   +"     AND UR.IUSERID=?  \n"
                   +"     AND SP.IPERMISSION=1  \n"
                   +"     AND SP.IPROID=PRJ.IID \n"
                   +"     AND (PRJ.PROTYPE=? OR PRJ.PROTYPE=?) \n"
                   +"  ) QX \n"
                   +" WHERE IPRJ.IPKGCONTENTID<>0  AND IPRJ.ILATESTID=IPRJ.IID \n"
                    + " AND (QX.INAME=IPRJ.INAME OR -QX.IPROID=IPRJ.PROTYPE) ORDER BY IPRJ.INAME \n";
            
            String jzQueryPrjNameSQL = "SELECT DISTINCT IID,IPRJ.INAME,IUPPERID,IPRJ.ILATESTID FROM IEAI_PROJECT IPRJ,( \n"
                    +" SELECT PRJ.INAME,IPROID \n"
                    +"      FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP ,IEAI_PROJECT PRJ \n"
                    +"      WHERE UR.IROLEID=R.IID  \n"
                    +"      AND R.IID=SP.IROLEID  \n"
                    +"      AND UR.IUSERID=?  \n"
                    +"      AND SP.IPERMISSION=1  \n"
                    +"      AND SP.IPROID=PRJ.IID \n"
                    +"      AND (PRJ.PROTYPE=? OR PRJ.PROTYPE=?) \n"
                    +" ) QX \n"
                    +" WHERE IPRJ.IPKGCONTENTID<>0  AND IPRJ.ILATESTID=IPRJ.IID \n"
                     + " AND (QX.INAME=IPRJ.INAME OR -QX.IPROID=IPRJ.PROTYPE) AND IPRJ.ISYSNAME = ? ORDER BY IPRJ.INAME \n";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getPrjName", log, actBean.getDbType());
                    if(ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch()){
                        actStat = conn.prepareStatement(jzQueryPrjNameSQL);
                        actStat.setString(1, actBean.getUserId());
                        actStat.setInt(2, actBean.getDbType());
                        actStat.setInt(3, -actBean.getDbType());
                        actStat.setString(4, actBean.getSysName());
                    }else{
                        actStat = conn.prepareStatement(queryPrjNameSQL);
                        actStat.setString(1, actBean.getUserId());
                        actStat.setInt(2, actBean.getDbType());
                        actStat.setInt(3, -actBean.getDbType());
                    }
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        Map emUTMap = new HashMap();
                        emUTMap.put("iprjId", actRS.getLong("IID"));
                        emUTMap.put("iprjName", actRS.getString("INAME"));
                        emUTMap.put("iupperId", actRS.getString("IUPPERID"));
                        list.add(emUTMap);
                    }
                    map.put("dataList", list);
                } catch (SQLException e)
                {
                    log.error("getPrjName method of StartFlowManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getPrjName", log);
                }
                break;
            } catch (RepositoryException ex)
            {
                log.error("getPrjName method of StartFlowManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    /**
     *
     * @Title: getGroname
     * @Description: 业务系统名称查询
     * @param
     * @return
     * @throws Exception
     * @author: Administrator
     * @throws RepositoryException
     * @date:   2021年9月26日 下午7:45:13
     */
    public List<ActBean> getGroName () throws RepositoryException
    {

        List res = new ArrayList();
        String sql1 = "SELECT DISTINCT GRO,SYSTEMNAME,TASKNAME,LASTSYSNAME,LASTTASKNAME FROM IEAI_TOPOINFO ";
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement actStat = null;
            ResultSet actRS = null;
            Connection con = null;
            try
            {
                con = DBResource.getConnection("getGroName", log, Constants.IEAI_HEALTH_INSPECTION);
                actStat = con.prepareStatement(sql1);
                actRS = actStat.executeQuery();
                while (actRS.next())
                {
                    ActBean model = new ActBean();
                    model.setGro(actRS.getString("GRO"));
                    model.setSystemname(actRS.getString("SYSTEMNAME"));
                    model.setLastsysname(actRS.getString("LASTSYSNAME"));
                    model.setTaskname(actRS.getString("TASKNAME"));
                    model.setLasttaskname(actRS.getString("LASTTASKNAME"));
                    res.add(model);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } catch (SQLException e)
            {
                log.error("getGroName is error ! " + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConn(con, actRS, actStat, "getGroName", log);
            }
        }
        return res;
    }

    /**
     *
     * @Title: getGroname
     * @Description: 业务系统名称查询
     * @param
     * @return
     * @throws Exception
     * @author: Administrator
     * @throws RepositoryException
     * @date:   2021年9月26日 下午7:45:13
     */
    public List<ActBean> getLastSysName (String gro) throws RepositoryException
    {
        List<ActBean> res = new ArrayList();
        String sql1 = "SELECT DISTINCT GRO,SYSTEMNAME,TASKNAME,LASTSYSNAME,LASTTASKNAME FROM IEAI_TOPOINFO WHERE GRO=? ";
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement actStat = null;
            ResultSet actRS = null;
            Connection con = null;
            try
            {
                con = DBResource.getConnection("getGroName", log, Constants.IEAI_HEALTH_INSPECTION);
                actStat = con.prepareStatement(sql1);
                actStat.setString(1,gro);
                actRS = actStat.executeQuery();
                while (actRS.next())
                {
                    ActBean model = new ActBean();
                    model.setGro(actRS.getString("GRO"));
                    model.setSystemname(actRS.getString("SYSTEMNAME"));
                    model.setLastsysname(actRS.getString("LASTSYSNAME"));
                    model.setTaskname(actRS.getString("TASKNAME"));
                    model.setLasttaskname(actRS.getString("LASTTASKNAME"));
                    res.add(model);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } catch (SQLException e)
            {
                log.error("getGroName is error ! " + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConn(con, actRS, actStat, "getGroName", log);
            }
        }
        return res;
    }
    /**
     * @throws ServerException 
     * @Title: listActMonitor
     * @Description: 作业调度首页，活动监控页面查询list
     * @param: @param userid
     * @param: @param filter
     * @param: @param iprjlatestid
     * @param: @param prjName
     * @param: @return
     * @param: @throws RepositoryException
     * @return: Map<String,Object>
     * @throws
     */
    public Map<String, Object> listActMonitor(Long userid, FilterActBean filter, String prjName, String state, String sysName, String flowName)
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean isAbnormalSwitch = ServerEnv.getServerEnv().getAbnormal();
        boolean isTimeSetAlarm = ServerEnv.getServerEnv().getTimeSetAlarm();
        boolean isActmonitorStart = ServerEnv.getServerEnv().getActmonitorStart();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                CallableStatement call = null;
                Connection con = null;
               
               
                try
                {
                    Long total = 0L;
                    Long optid = 0L;
                    con = DBResource.getConnection(method, log, Constants.IEAI_IEAI);
                    
                    if (JudgeDB.IEAI_DB_TYPE != 3)
                    {
                            
                        call = con.prepareCall("{call PROC_ACTLIST_PAGE(?,?,?,?,?,?)}");
                        call.setLong(1, userid);
                        call.setLong(2, filter.getStart());
                        call.setLong(3, filter.getPagesize());
                        call.setString(4,  getPrjName(prjName));
                        call.registerOutParameter(5, java.sql.Types.NUMERIC);
                        call.registerOutParameter(6, java.sql.Types.NUMERIC);
                        call.execute();
                    } else
                    {
                        call = con.prepareCall("{call PROC_ACTLIST_PAGE(?,?,?,?,?)}");
                        call.setLong(1, userid);
                        call.setLong(2, filter.getStart());
                        call.setLong(3, filter.getPagesize());
                        call.registerOutParameter(4, java.sql.Types.NUMERIC);
                        call.registerOutParameter(5, java.sql.Types.NUMERIC);
                        call.execute();
                    }
                    StringBuilder sqlCount = new StringBuilder();
                     sqlCount.append("SELECT COUNT(*) COUNT FROM (");


//                     String sql = getActMonitorSql(prjName, "", isTimeSetAlarm,newTextField);
                    String sql = getActMonitorSql(prjName, sysName, isTimeSetAlarm, state, flowName);

                    if (JudgeDB.IEAI_DB_TYPE != 3)
                    {
                        optid = call.getLong(6);
                        total = call.getLong(5);
                    } else
                    {
                       
                        optid = call.getLong(5);
                        total = call.getLong(4);
                  
                        /**
                        * 当选中项目名称时,重新计算total总数;如果iprjlatestid != 0,则说明带有查询参数
                        */
                        String endSql = " ) C ";
                        sqlCount.append(sql + endSql);
                        total = getActMonitorTotal(con, sqlCount.toString(), optid,total);
                    }

                    QueryRunner qr = new QueryRunner();
                    Object[] qrproptid = new Object[0];
                    if (JudgeDB.IEAI_DB_TYPE != 3){
                         qrproptid = new Object[] { optid  };
                    }else{
                         qrproptid = new Object[] { optid ,filter.getStart(),filter.getPagesize() };
                    }

                    log.info("活动监控查询sql：" + sql);

                    List<ActBean> datalist = qr.query(con, sql, new BeanListHandler<ActBean>(ActBean.class), qrproptid);

                    List<ActBean> resultList = getActBeanList(datalist, isAbnormalSwitch, isTimeSetAlarm,
                        isActmonitorStart, con);
                    retMap.put("datalist", resultList);
                    retMap.put("total", total);
                } catch (SQLException e)
                {
                    log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                     
                    try
                    {
                        con.rollback();
                    } catch (SQLException e)
                    {
                        log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                    }
                    DBResource.closeCallableStatement(call, method, log);
                    DBResource.closeConnection(con, method, log);
                }
                break;
            } catch (RepositoryException ex)
            {
                log.error(method + " method of " + this.getClass().getSimpleName() + ".class RepositoryException:"
                        + ex.getMessage());
                 
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                
            }
        }

        return retMap;
    }

    /**
     * @throws
     * @Title: listGroActMonitor
     * @Description: 作业调度首页，活动监控页面查询list
     * @param: @param userid
     * @param: @param filter
     * @param: @param iprjlatestid
     * @param: @param prjName
     * @param: @return
     * @param: @throws RepositoryException
     * @return: Map<String,Object>
     * @throws
     */
    public Map<String, Object> listGroActMonitor(Long userid, FilterActBean filter, String prjNames,String prjName,String sysName)
            throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean isAbnormalSwitch = ServerEnv.getServerEnv().getAbnormal();
        boolean isTimeSetAlarm = ServerEnv.getServerEnv().getTimeSetAlarm();
        boolean isActmonitorStart = ServerEnv.getServerEnv().getActmonitorStart();

        for (int i = 0; i < 10; i++) {
            try {
                CallableStatement call = null;
                Connection con = null;


                try {
                    Long total = 0L;
                    Long optid = 0L;
                    con = DBResource.getConnection(method, log, Constants.IEAI_IEAI);

                    if (DBManager.Orcl_Faimily()) {

                        call = con.prepareCall("{call PROC_GROACTLIST_PAGE(?,?,?,?,?,?,?)}");
                        call.setLong(1, userid);
                        call.setLong(2, filter.getStart());
                        call.setLong(3, filter.getPagesize());
                        call.setString(4, getPrjName(prjName));
                        call.registerOutParameter(5, java.sql.Types.NUMERIC);
                        call.registerOutParameter(6, java.sql.Types.NUMERIC);
                        call.setString(7, getPrjName(sysName));
                        call.execute();
                    } else if(JudgeDB.IEAI_DB_TYPE == 2){
                        call = con.prepareCall("{call PROC_GROACTLIST_PAGE(?,?,?,?,?,?,?)}");
                        call.setLong(1, userid);
                        call.setLong(2, filter.getStart());
                        call.setLong(3, filter.getPagesize());
                        call.setString(4, getPrjName(prjName));
                        call.registerOutParameter(5, java.sql.Types.NUMERIC);
                        call.registerOutParameter(6, java.sql.Types.NUMERIC);
                        call.setString(7, getPrjName(prjNames));
                        call.execute();
                    }else {
                        call = con.prepareCall("{call PROC_GROACTLIST_PAGE(?,?,?,?,?)}");
                        call.setLong(1, userid);
                        call.setLong(2, filter.getStart());
                        call.setLong(3, filter.getPagesize());
                        call.registerOutParameter(4, java.sql.Types.NUMERIC);
                        call.registerOutParameter(5, java.sql.Types.NUMERIC);
                        call.execute();
                    }
                    StringBuilder sqlCount = new StringBuilder();
                    sqlCount.append("SELECT COUNT(*) COUNT FROM (");

                    String sql = getGroActMonitorSql(prjName,prjNames, isTimeSetAlarm);

                    if (JudgeDB.IEAI_DB_TYPE != 3) {
                        optid = call.getLong(6);
                        total = call.getLong(5);
                    } else {

                        optid = call.getLong(5);
                        total = call.getLong(4);

                        /**
                         * 当选中项目名称时,重新计算total总数;如果iprjlatestid != 0,则说明带有查询参数
                         */
                        String endSql = " ) C ";
                        sqlCount.append(sql + endSql);
                        total = getActMonitorTotal(con, sqlCount.toString(), optid, total);
                    }

                    QueryRunner qr = new QueryRunner();
                    Object[] qrproptid = new Object[]{optid};

                    List<ActBean> datalist = qr.query(con, sql, new BeanListHandler<ActBean>(ActBean.class), qrproptid);

                    List<ActBean> resultList = getActBeanList(datalist, isAbnormalSwitch, isTimeSetAlarm,
                            isActmonitorStart, con);

                    retMap.put("datalist", resultList);
                    retMap.put("total", total);
                } catch (SQLException e) {
                    log.error(METHODSTR + Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {

                    try {
                        con.rollback();
                    } catch (SQLException e) {
                        log.error(METHODSTR + Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    }
                    DBResource.closeCallableStatement(call, method, log);
                    DBResource.closeConnection(con, method, log);
                }
                break;
            } catch (RepositoryException ex) {
                log.error(method + " method of " + this.getClass().getSimpleName() + ".class RepositoryException:"
                        + ex.getMessage());

                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);

            }
        }

        return retMap;
        }

    
    
    /**
     * 
     * <li>Description:山东新活动监控</li> 
     * <AUTHOR>
     * 2019年11月13日 
     * @param userid
     * @param filter
     * @param prjName
     * @param sysName
     * @return
     * @throws RepositoryException
     * return Map<String,Object>
     */
    public Map<String, Object> listActSDMonitor ( Long userid, FilterActBean filter, String prjName,long  flowid )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean isAbnormalSwitch = ServerEnv.getServerEnv().getAbnormal();
        boolean isTimeSetAlarm = ServerEnv.getServerEnv().getTimeSetAlarm();
        boolean isActmonitorStart = ServerEnv.getServerEnv().getActmonitorStart();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                CallableStatement call = null;
                Connection con = null;
               
               
                try
                {
                    Long total = 0L;
                    Long optid = 0L;
                    con = DBResource.getConnection(method, log, Constants.IEAI_IEAI);
                    if (JudgeDB.IEAI_DB_TYPE != 3){
                        
                        if (flowid == 0)
                        {
                            
                            call = con.prepareCall("{call PROC_ACTLIST_SDPAGE(?,?,?,?,?,?)}");
                            call.setLong(1, userid);
                            call.setLong(2, filter.getStart());
                            call.setLong(3, filter.getPagesize());
                            call.setString(4,  getPrjName(prjName));
                            call.registerOutParameter(5, java.sql.Types.NUMERIC);
                            call.registerOutParameter(6, java.sql.Types.NUMERIC);
                            call.execute();
                        } else
                        {
                            call = con.prepareCall("{call PROC_ACTLIST_SDPAGEBYID(?,?,?,?,?,?,?)}");
                            call.setLong(1, userid);
                            call.setLong(2, filter.getStart());
                            call.setLong(3, filter.getPagesize());
                            call.setString(4,  getPrjName(prjName));
                            call.registerOutParameter(5, java.sql.Types.NUMERIC);
                            call.registerOutParameter(6, java.sql.Types.NUMERIC);
                            call.setLong(7, flowid);
                            call.execute();
                        }
                    }else{
                        
                        if (flowid == 0)
                        {
                            
                            call = con.prepareCall("{call PROC_ACTLIST_SDPAGE(?,?,?,?,?)}");
                            call.setLong(1, userid);
                            call.setLong(2, filter.getStart());
                            call.setLong(3, filter.getPagesize());
                            call.registerOutParameter(4, java.sql.Types.NUMERIC);
                            call.registerOutParameter(5, java.sql.Types.NUMERIC);
                            call.execute();
                        } else
                        {
                            call = con.prepareCall("{call PROC_ACTLIST_SDPAGEBYID(?,?,?,?,?,?)}");
                            call.setLong(1, userid);
                            call.setLong(2, filter.getStart());
                            call.setLong(3, filter.getPagesize());
                            call.registerOutParameter(4, java.sql.Types.NUMERIC);
                            call.registerOutParameter(5, java.sql.Types.NUMERIC);
                            call.setLong(6, flowid);
                            call.execute();
                        }
                        
                    }
                    StringBuilder sqlCount = new StringBuilder();
                     sqlCount.append("SELECT COUNT(*) COUNT FROM (");

                    String sql = getActMonitorSql(prjName, "", isTimeSetAlarm, "", "");

                    if (JudgeDB.IEAI_DB_TYPE != 3)
                    {
                        optid = call.getLong(6);
                        total = call.getLong(5);
                    } else
                    {
                       
                        optid = call.getLong(5);
                        total = call.getLong(4);
                  
                        /**
                        * 当选中项目名称时,重新计算total总数;如果iprjlatestid != 0,则说明带有查询参数
                        */
                        String endSql = " ) C ";
                        sqlCount.append(sql + endSql);
                        total = getActMonitorTotal(con, sqlCount.toString(), optid,total);
                    }

                    QueryRunner qr = new QueryRunner();
                    Object[] qrproptid = new Object[] { optid };
                    if (JudgeDB.IEAI_DB_TYPE != 3){
                        qrproptid = new Object[] { optid  };
                    }else{
                        qrproptid = new Object[] { optid ,filter.getStart(),filter.getPagesize() };
                    }
                    
                    List<ActBean> datalist = qr.query(con, sql, new BeanListHandler<ActBean>(ActBean.class), qrproptid);

                    List<ActBean> resultList = getActBeanList(datalist, isAbnormalSwitch, isTimeSetAlarm,
                        isActmonitorStart, con);
                    retMap.put("datalist", resultList);
                    retMap.put("total", total);
                } catch (SQLException e)
                {
                    log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                     
                    try
                    {
                        con.rollback();
                    } catch (SQLException e)
                    {
                        log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                    }
                    DBResource.closeCallableStatement(call, method, log);
                    DBResource.closeConnection(con, method, log);
                }
                break;
            } catch (RepositoryException ex)
            {
                log.error(method + " method of " + this.getClass().getSimpleName() + ".class RepositoryException:"
                        + ex.getMessage());
                 
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                
            }
        }

        return retMap;
    }
    
    private String getPrjName(String prjName){
        
       return  "".equals(prjName) ? null : prjName;
    }
    
    public List<Long> getCallFlowId(long mainFlowId) throws RepositoryException{
        
       
        Connection conn = null;
        List<Long> callFlowIds = new ArrayList<Long>();
        for(int i = 0; i < 10; i++){
            try
            {
                conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, Constants.IEAI_IEAI);
                callFlowIds = getCallFlowIdSub(conn, mainFlowId);
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        
        return callFlowIds;
    }
    
    
    public Long getUpFlowId(long callFlowId) throws RepositoryException{
        
        
        Connection conn = null;
        Long mainFlowId = 0L;
        for(int i = 0; i < 10; i++){
            try
            {
                conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, Constants.IEAI_IEAI);
                mainFlowId = getUpFlowIdSub(conn, callFlowId);
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }finally {
                DBResource.closeConnection(conn,Thread.currentThread().getStackTrace()[1].getMethodName(),log);
                break;
            }
        }
        
        return mainFlowId;
    }
    
    
    public String getPrjNameByFlowId(long flowId) throws RepositoryException{
        
        String prjName = "";
        Connection conn = null;
        for(int i = 0; i < 10; i++){
            try
            {
                conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, Constants.IEAI_IEAI);
                prjName =  getPrjNameByFlowIdSub( flowId,conn);
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }finally {
                DBResource.closeConnection(conn,"getPrjNameByFlowId",log);
                break;
            }
        }
        return prjName;
    }
    
    private String getPrjNameByFlowIdSub(long flowId,Connection conn){
        
        PreparedStatement ps = null;
        ResultSet rs = null;
        String prjName = "";
        String sql = "SELECT T.IPROJECTNAME FROM IEAI_WORKFLOWINSTANCE T WHERE T.IFLOWID = ? ";
        List<Long> callFlowIds = new ArrayList<Long>();
        try{
            
            ps = conn.prepareStatement(sql);
            ps.setLong(1, flowId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                // 计算带有查询条件的count总数
                prjName = rs.getString("IPROJECTNAME");
            }
        }catch (SQLException e) {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }finally{
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        
        return prjName;
    }
    
    private List<Long> getCallFlowIdSub(Connection conn,long mainFlowId){
        ResultSet rs = null;
        PreparedStatement ps = null;
        String sql = "SELECT C.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO C WHERE C.IMAINFLOWID = ? ";
        List<Long> callFlowIds = new ArrayList<Long>();
        try{
            
            ps = conn.prepareStatement(sql);
            ps.setLong(1, mainFlowId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                // 计算带有查询条件的count总数
                callFlowIds.add(rs.getLong(1));
            }
        }catch (SQLException e) {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }finally{
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return callFlowIds;
    }
    
    private Long getUpFlowIdSub(Connection conn,long callFlowId){
        ResultSet rs = null;
        PreparedStatement ps = null;
        String sql = "SELECT C.IUPID FROM IEAI_CALLWORKFLOW_INFO C WHERE C.ICALLFLOWID = ? ";
        Long mainFlowId = 0L;
        try{
            
            ps = conn.prepareStatement(sql);
            ps.setLong(1, callFlowId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                // 计算带有查询条件的count总数
                mainFlowId = rs.getLong(1);
            }
        }catch (SQLException e) {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }finally{
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return mainFlowId;
    }
    
    
    private long getActMonitorTotal(Connection con ,String sqlCount,long optid,long total){
        
        PreparedStatement ps = null;
        ResultSet rs = null;
        sqlCount = sqlCount.replace("limit ?,?"," ");

        try{
            ps = con.prepareStatement(sqlCount);
            ps.setLong(1, optid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                // 计算带有查询条件的count总数
                total = rs.getLong("COUNT");
            }
        }catch (SQLException e) {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }finally{
            
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        
        return total;
    }

    private String getActMonitorSql(String prjName, String sysName, boolean isTimeSetAlarm, String state, String flowName)
    {
        
        String sql = "SELECT * FROM (";
        if (isTimeSetAlarm)
        {
            sql = sql
                    + "SELECT T1.* FROM (SELECT T.ACT_ID AS ACTIID,T.ACT_NAME AS ACTNAME,T.FLOW_ID AS FLOWID,W.IPROJECTNAME AS PRJNAME,W.IFLOWNAME AS FLOWNAME, T.FLOW_INSTANCE_NAME AS FLOWINSNAME,T.ACT_STATES AS ACTSTATE,T.TASK_ID AS TASKID,T.STARTUSER_NAME AS STARTUSER, ACT_DESCRIPTION AS ACTDES, T.ACT_DEF_NAME  AS ACTDEFNAME,T.ACT_ERRORTASKID AS ERRORTASKID,T.EXECACT_IREXECREQUESTID AS REQID,FUN_GET_DATE_STRING(T.BEGINEXCTIME,8,'YYYY-MM-DD HH24:MI:SS') AS STARTTIME,T.SHELLNAME,T.SHELLPATH,T.AGENTIP,T.ACT_DEF_NAME,W.ISYSTEM as SYSNAME FROM TMP_FIRST_PAGE_LIST T ,IEAI_WORKFLOWINSTANCE W WHERE T.OPT_ID = ? AND W.IFLOWID = T.FLOW_ID  AND ACT_STATES != 'Disable'";
        } else
        {
            sql = sql
                    + "SELECT T1.* FROM (SELECT T.ACT_ID AS ACTIID,T.ACT_NAME AS ACTNAME,T.FLOW_ID AS FLOWID,W.IPROJECTNAME AS PRJNAME,W.IFLOWNAME AS FLOWNAME, T.FLOW_INSTANCE_NAME AS FLOWINSNAME,T.ACT_STATES AS ACTSTATE,T.TASK_ID AS TASKID,T.STARTUSER_NAME AS STARTUSER, ACT_DESCRIPTION AS ACTDES, T.ACT_DEF_NAME  AS ACTDEFNAME,T.ACT_ERRORTASKID AS ERRORTASKID,T.EXECACT_IREXECREQUESTID AS REQID,FUN_GET_DATE_STRING(T.BEGINEXCTIME,8,'YYYY-MM-DD HH24:MI:SS') AS STARTTIME,T.SHELLNAME,T.SHELLPATH,T.AGENTIP,T.ACT_DEF_NAME,W.ISYSTEM as SYSNAME FROM TMP_FIRST_PAGE_LIST T ,IEAI_WORKFLOWINSTANCE W WHERE T.OPT_ID = ? AND W.IFLOWID = T.FLOW_ID  AND ACT_STATES != 'Disable'";
        }
        if (StringUtils.isNotBlank(prjName))
        {
            sql = sql + " AND (W.IPROJECTNAME like '%" + prjName + "%' or T.ACT_NAME like '%"  + prjName + "%' or  T.FLOW_INSTANCE_NAME like  '%"  + prjName + "%'  or  T.AGENTIP like '%"  + prjName + "%' or  ACT_DESCRIPTION like '%"  + prjName + "%') ";
        }
        if (StringUtils.isNotBlank(flowName)) {
            sql = sql + " AND W.IFLOWNAME like '%" + flowName + "%'";
        }

        if(StringUtils.isNotBlank(sysName)){
            sql = sql + " AND W.ISYSTEM LIKE '%"+sysName+"%' ";
        }

        if(StringUtils.isNotBlank(state)) {
            Map<String, String> stateMap = new HashMap<>();
            stateMap.put("失败", "Fail");
            stateMap.put("就绪", "Ready");
            stateMap.put("异常处理中", "ManualRunning");
            stateMap.put("连接失败处理中", "ManualDisconnect");
            stateMap.put("运行连接失败", "Disconnect");
            stateMap.put("超时", "Timeout");
            stateMap.put("排队", "QueueUp");
            stateMap.put("暂停", "Paused");
            stateMap.put("挂起", "HangUp");
            stateMap.put("失败:业务异常", "Fail:Business");

            if("运行".equals(state)) {
                sql = sql + " AND (ACT_STATES = 'Running' OR ACT_STATES = 'Continue') ";
            } else if(stateMap.containsKey(state)) {
                String stateStr = stateMap.get(state);
                // 修复原代码中的逻辑错误：错误地使用了sysName变量
                sql = sql + " AND ACT_STATES = '" + stateStr + "' ";
            }
        }

        if (DBManager.Orcl_Faimily())
        {  // oracle
            if (isTimeSetAlarm)
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) T1 "
                        // + "LEFT JOIN ieai_act_timeoutconfig T2 ON t1.PRJNAME=t2.isystem_name AND
                        // t1.flowname=t2.imain_flow AND t1.ACTNAME=t2.itask_name "
                        // + "LEFT JOIN (SELECT * FROM (SELECT *,ROWNUM rn FROM
                        // ieai_act_timeoutconfig s WHERE
                        // s.isystem_name=t1.PRJNAME,s.imain_flow=t1.flowname,s.itask_name=t1.ACTNAME
                        // ORDER BY iid DESC) WHERE rn=1) t4 ON t1.PRJNAME=t4.isystem_name AND
                        // t.flowname=t4.imain_flow AND t1.ACTNAME=t4.itask_name "
                        // + "LEFT JOIN ieai_actruntime_avgrecord t3 ON t1.prjname=t3.iprjname AND
                        // t1.flowname=t3.iflowname AND t1.actname=t3.iactname where t3.iexpecttime
                        // IS NULL"
                        + ")sss ";
            } else
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) T1 "
                        + ")sss ";
            }

        } else if (JudgeDB.IEAI_DB_TYPE == 2)  // db2
        {
            if (isTimeSetAlarm)
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6','Continue', '7','Running', '8', 'QueueUp', '9')) T1 "
                        // + "LEFT JOIN ieai_act_timeoutconfig T2 ON t1.PRJNAME=t2.isystem_name AND
                        // t1.flowname=t2.imain_flow AND t1.ACTNAME=t2.itask_name "
                        // + "LEFT JOIN ieai_actruntime_avgrecord t3 ON t1.prjname=t3.iprjname AND
                        // t1.flowname=t3.iflowname AND t1.actname=t3.iactname where t3.iexpecttime
                        // IS NULL"
                        + ")sss ";
            } else
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6','Continue', '7','Running', '8', 'QueueUp', '9')) T1 "
                        + ")sss ";
            }
        } else
        {  // Mysql
            if (isTimeSetAlarm)
            {
                sql = sql
                        + " ORDER BY FIELD(T.ACT_STATES,'Fail', 'Fail:Business' ,'ManualDisconnect','Disconnect','Timeout','Ready','Running','Continue', 'QueueUp')) T1 "
                        // + "LEFT JOIN ieai_act_timeoutconfig T2 ON t1.PRJNAME=t2.isystem_name AND
                        // t1.flowname=t2.imain_flow AND t1.ACTNAME=t2.itask_name "
                        // + "LEFT JOIN ieai_actruntime_avgrecord t3 ON t1.prjname=t3.iprjname AND
                        // t1.flowname=t3.iflowname AND t1.actname=t3.iactname where t3.iexpecttime
                        // IS NULL "
                        + ")sss limit ?,?";
            } else
            {
                sql = sql
                        + " ORDER BY FIELD(T.ACT_STATES,'Fail', 'Fail:Business' ,'ManualDisconnect','Disconnect','Timeout','Ready','Running','Continue', 'QueueUp')) T1 "
                        + ")sss limit ?,?";

            }
            // 计算 mysql 分页最后一页显示条数不足时，取总数的余数为最后一页数量
        }
        
        return sql;
        
    }
    private String getGroActMonitorSql ( String prjName, String prjNames,boolean isTimeSetAlarm )
    {

        String sql = "SELECT * FROM (";
        if (isTimeSetAlarm)
        {
            sql = sql
                    + "SELECT T1.* FROM (SELECT T.ACT_ID AS ACTIID,T.ACT_NAME AS ACTNAME,T.FLOW_ID AS FLOWID,W.IPROJECTNAME AS PRJNAME,W.IFLOWNAME AS FLOWNAME, T.FLOW_INSTANCE_NAME AS FLOWINSNAME,T.ACT_STATES AS ACTSTATE,T.TASK_ID AS TASKID,T.STARTUSER_NAME AS STARTUSER, ACT_DESCRIPTION AS ACTDES, T.ACT_DEF_NAME  AS ACTDEFNAME,T.ACT_ERRORTASKID AS ERRORTASKID,T.EXECACT_IREXECREQUESTID AS REQID,FUN_GET_DATE_STRING(T.BEGINEXCTIME,8,'YYYY-MM-DD HH24:MI:SS') AS STARTTIME,T.SHELLNAME,T.SHELLPATH,T.AGENTIP FROM TMP_FIRST_PAGE_LIST T ,IEAI_WORKFLOWINSTANCE W WHERE T.OPT_ID = ? AND W.IFLOWID = T.FLOW_ID";
        } else
        {
            sql = sql
                    + "SELECT T1.* FROM (SELECT T.ACT_ID AS ACTIID,T.ACT_NAME AS ACTNAME,T.FLOW_ID AS FLOWID,W.IPROJECTNAME AS PRJNAME,W.IFLOWNAME AS FLOWNAME, T.FLOW_INSTANCE_NAME AS FLOWINSNAME,T.ACT_STATES AS ACTSTATE,T.TASK_ID AS TASKID,T.STARTUSER_NAME AS STARTUSER, ACT_DESCRIPTION AS ACTDES, T.ACT_DEF_NAME  AS ACTDEFNAME,T.ACT_ERRORTASKID AS ERRORTASKID,T.EXECACT_IREXECREQUESTID AS REQID,FUN_GET_DATE_STRING(T.BEGINEXCTIME,8,'YYYY-MM-DD HH24:MI:SS') AS STARTTIME,T.SHELLNAME,T.SHELLPATH,T.AGENTIP FROM TMP_FIRST_PAGE_LIST T ,IEAI_WORKFLOWINSTANCE W WHERE T.OPT_ID = ? AND W.IFLOWID = T.FLOW_ID";
        }
        if (StringUtils.isNotBlank(prjName))
        {
            sql = sql +  " AND W.IPROJECTNAME like '%" + prjName + "%'"+" AND W.IPROJECTNAME in " +"(" +prjNames + ")" ;
        }else {
            sql = sql + " AND W.IPROJECTNAME in " +"(" +prjNames + ")" ;
        }

        if (DBManager.Orcl_Faimily())
        {  // oracle
            if (isTimeSetAlarm)
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) T1 "
                        // + "LEFT JOIN ieai_act_timeoutconfig T2 ON t1.PRJNAME=t2.isystem_name AND
                        // t1.flowname=t2.imain_flow AND t1.ACTNAME=t2.itask_name "
                        // + "LEFT JOIN (SELECT * FROM (SELECT *,ROWNUM rn FROM
                        // ieai_act_timeoutconfig s WHERE
                        // s.isystem_name=t1.PRJNAME,s.imain_flow=t1.flowname,s.itask_name=t1.ACTNAME
                        // ORDER BY iid DESC) WHERE rn=1) t4 ON t1.PRJNAME=t4.isystem_name AND
                        // t.flowname=t4.imain_flow AND t1.ACTNAME=t4.itask_name "
                        // + "LEFT JOIN ieai_actruntime_avgrecord t3 ON t1.prjname=t3.iprjname AND
                        // t1.flowname=t3.iflowname AND t1.actname=t3.iactname where t3.iexpecttime
                        // IS NULL"
                        + ")sss ";
            } else
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) T1 "
                        + ")sss ";
            }

        } else if (JudgeDB.IEAI_DB_TYPE == 2)  // db2
        {
            if (isTimeSetAlarm)
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6','Continue', '7','Running', '8', 'QueueUp', '9')) T1 "
                        // + "LEFT JOIN ieai_act_timeoutconfig T2 ON t1.PRJNAME=t2.isystem_name AND
                        // t1.flowname=t2.imain_flow AND t1.ACTNAME=t2.itask_name "
                        // + "LEFT JOIN ieai_actruntime_avgrecord t3 ON t1.prjname=t3.iprjname AND
                        // t1.flowname=t3.iflowname AND t1.actname=t3.iactname where t3.iexpecttime
                        // IS NULL"
                        + ")sss ";
            } else
            {
                sql = sql
                        + " ORDER BY decode(T.ACT_STATES, 'Fail', '1', 'Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6','Continue', '7','Running', '8', 'QueueUp', '9')) T1 "
                        + ")sss ";
            }
        } else
        {  // Mysql
            if (isTimeSetAlarm)
            {
                sql = sql
                        + " ORDER BY FIELD(T.ACT_STATES,'Fail', 'Fail:Business' ,'ManualDisconnect','Disconnect','Timeout','Ready','Running','Continue', 'QueueUp')) T1 "
                        // + "LEFT JOIN ieai_act_timeoutconfig T2 ON t1.PRJNAME=t2.isystem_name AND
                        // t1.flowname=t2.imain_flow AND t1.ACTNAME=t2.itask_name "
                        // + "LEFT JOIN ieai_actruntime_avgrecord t3 ON t1.prjname=t3.iprjname AND
                        // t1.flowname=t3.iflowname AND t1.actname=t3.iactname where t3.iexpecttime
                        // IS NULL "
                        + ")sss ";
            } else
            {
                sql = sql
                        + " ORDER BY FIELD(T.ACT_STATES,'Fail', 'Fail:Business' ,'ManualDisconnect','Disconnect','Timeout','Ready','Running','Continue', 'QueueUp')) T1 "
                        + ")sss ";

            }
            // 计算 mysql 分页最后一页显示条数不足时，取总数的余数为最后一页数量
        }

        return sql;

    }
    
    /**   
     * @Title: getTimeConfigInfo   
     * @Description: 获得超时配置信息  
     * @param bean
     * @param con      
     * @author: txl 
     * @date:   2020-8-1 16:32:03   
     */
    public void getTimeConfigInfo ( ActBean bean, Connection con )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT T2.ITHRESHOLD,T2.ITASK_TIMEOUTNUM  from ieai_act_timeoutconfig  t2 where isystem_name=? and imain_flow=? and itask_name=? and  (itimeout_month=''or itimeout_month IS NULL) AND (itimeout_week IS NULL or itimeout_week='') AND (itimeout_day IS NULL or itimeout_day='') ";

        for (int i = 0, len = 10; i < len; i++)
        {
            try
            {
                ps = con.prepareStatement(sql);
                ps.setString(1, bean.getPrjName());
                ps.setString(2, bean.getFlowName());
                ps.setString(3, bean.getActName());
                rs = ps.executeQuery();
                while (rs.next())
                {
                    // bean.setIavgtime(rs.getLong("IAVGTIME"));
                    bean.setIthreshold(rs.getLong("ITHRESHOLD"));
                    bean.setItask_timeoutnum(rs.getLong("ITASK_TIMEOUTNUM"));
                }
                break;
            } catch (SQLException e)
            {
                log.error(method + " error!", e);
            } catch (Exception e)
            {
                log.error(method + " error!", e);
            } finally
            {
                DBResource.closePSRS(rs, ps, method, log);
            }

        }
    }


    public void getTimeConfigInfoZYFD ( ActBean bean, Connection con )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT T2.ITHRESHOLD,T2.ITASK_TIMEOUTNUM,T2.IAVGNUM from ieai_act_timeoutconfig  t2 where isystem_name=? and imain_flow=? and itask_name=? and  (itimeout_month=''or itimeout_month IS NULL) AND (itimeout_week IS NULL or itimeout_week='') AND (itimeout_day IS NULL or itimeout_day='') ";

        for (int i = 0, len = 10; i < len; i++)
        {
            try
            {
                ps = con.prepareStatement(sql);
                ps.setString(1, bean.getPrjName());
                ps.setString(2, bean.getFlowName());
                ps.setString(3, bean.getActName());
                rs = ps.executeQuery();
                while (rs.next())
                {
                    // bean.setIavgtime(rs.getLong("IAVGTIME"));
                    bean.setIthreshold(rs.getLong("ITHRESHOLD"));
                    bean.setItask_timeoutnum(rs.getLong("ITASK_TIMEOUTNUM"));
                    bean.setiAvgNum(rs.getInt("IAVGNUM"));
                }
                break;
            } catch (SQLException e)
            {
                log.error(method + " error!", e);
            } catch (Exception e)
            {
                log.error(method + " error!", e);
            } finally
            {
                DBResource.closePSRS(rs, ps, method, log);
            }

        }
    }


    /**   
     * @Title: getavgInfo   
     * @Description: 获得平均耗时信息  
     * @param bean
     * @param con      
     * @author: txl 
     * @date:   2020-8-1 16:29:39   
     */
    public void getavgInfo ( ActBean bean, Connection con )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT IAVGTIME from ieai_actruntime_avgrecord where iprjname=? and iflowname=? and iactname=? ORDER BY iid DESC";

        for (int i = 0, len = 10; i < len; i++)
        {
            try
            {
                int resultNum=0;
                long count=0;
                ps = con.prepareStatement(sql);
                ps.setString(1, bean.getPrjName());
                ps.setString(2, bean.getFlowName());
                ps.setString(3, bean.getActName());
                rs = ps.executeQuery();
                while (rs.next())
                {
                    resultNum++;
                    count+= rs.getLong("IAVGTIME");
                    if(resultNum==bean.getiAvgNum()){
                        break;
                    }

                }
                if(resultNum>0){
                    bean.setIavgtime(count/resultNum);
                }else {
                    bean.setIavgtime(0);
                }
                break;
            } catch (SQLException e)
            {
                log.error(method + " error!", e);
            } catch (Exception e)
            {
                log.error(method + " error!", e);
            } finally
            {
                DBResource.closePSRS(rs, ps, method, log);
            }

        }
    }
    
    
    /**   
     * @Title: getActBeanList   
     * @Description: 获取背景颜色和图案   
     * @param datalist
     * @param isAbnormalSwitch
     * @param isTimeSetAlarm
     * @param isActmonitorStart
     * @param con
     * @return
     * @throws RepositoryException      
     * @author: txl 
     * @date:   2020-7-28 9:56:59   
     */
    private List<ActBean> getActBeanList ( List<ActBean> datalist, boolean isAbnormalSwitch, boolean isTimeSetAlarm,
            boolean isActmonitorStart, Connection con ) throws RepositoryException
    {

        int index = 0;

        List<ActBean> resultList = new ArrayList<ActBean>();
        List<ActBean> usertaskList = new ArrayList<ActBean>();
        boolean bhTimeSetSwitch = Environment.getInstance().getBooleanConfig(Environment.BH_TIME_SET_COLOR_SWITCH, false);

        for (int j = 0; j < datalist.size(); j++)
        {
            ActBean bean = datalist.get(j);
            // 当活动为禁用状态时，活动设置不可见
            if (!StringUtils.equals(Constants.ACT_STATE_DISABLE, bean.getActState()))
            {
                boolean isMain = Engine.getInstance().isMainFlow(bean.getPrjName(), bean.getFlowName());
                if (isMain)
                {
                    // 判断流程是否为日启动流程
                    boolean isExcelDayStartUpload = WorkflowManager.getInstance()
                            .checkPrjIsExcelDayStartUpload(bean.getPrjName());
                    bean.setPic(WorkflowColumnGenerator.getPicMainLowStr(bean.getFlowID(), bean.getPrjName(),
                        bean.getFlowName(), isExcelDayStartUpload));
                } else
                {
                    bean.setPic(WorkflowColumnGenerator.getPicStr(index, bean.getFlowID(), false));
                }
                //组织查看topo依赖,只有shellcmd类型活动才能跳转topo查看依赖
                if("ShellCmd".equalsIgnoreCase(bean.getActDefName()) || "ShellCmd".equalsIgnoreCase(bean.getActDef())){
                    bean.setTopoPic(generateTopoPic(bean));
                }
                
                // 判断活动开始时间是否为1970年，如果包含1970，则将活动开始时间置为空
                if (StringUtils.contains(bean.getStartTime(), "1970"))
                {
                    bean.setStartTime("");
                }
                setActBeanSystemSwitch(con, bean);
                
                //判断渤海开关，true则走渤海新逻辑，false则走原来逻辑
                if(bhTimeSetSwitch)
                {
                    setTimeSetAlarmForBh(isTimeSetAlarm, bean, con);
                }else
                {
                    setTimeSetAlarm(isTimeSetAlarm, bean, con);
                }
                
                setActBeanAbnormalSwitch(isAbnormalSwitch, isTimeSetAlarm, bean);

                index++;
                if (isActmonitorStart && StringUtils.isNotEmpty(bean.getActDefName())
                        && bean.getActDefName().equals("UserTask") && bean.getActName().equals("开始跑批"))
                {
                    bean.setShowColor("true" + ";" + "blue");
                    usertaskList.add(bean);
                } else
                {
                    resultList.add(bean);
                }
            }

        }
        if (usertaskList.isEmpty())
        {
            return resultList;
        } else
        {
            usertaskList.addAll(resultList);
            return usertaskList;
        }

    }

    private String generateTopoPic(ActBean bean) {
        if (bean.getFlowInsName().length() < 8) {
            return "";
        }
        String year = bean.getFlowInsName().substring(0,4);
        String month = bean.getFlowInsName().substring(4,6);
        String day = bean.getFlowInsName().substring(6,8);
        String dataDate = year + "-" + month + "-" + day;
        return "<a href='#' onclick=\"showTopo('"+bean.getPrjName()+"','"+bean.getActName()+"','"+dataDate+"');\" ><image  src='images/topopic.png' border='0'/></a>";
    }

    /**   
     * @Title: setTimeSetAlarm   
     * @Description: 为耗时设置背景颜色   
     * @param isTimeSetAlarm
     * @param bean      
     * @author: txl 
     * @date:   2020-7-24 11:31:46   
     */
    private void setTimeSetAlarm ( boolean isTimeSetAlarm, ActBean bean, Connection con )  throws RepositoryException
    {
        // 取得超时配置
        getTimeConfigInfoZYFD(bean, con);

        // 取得平均耗时
        getAvgTimeNew1(bean,con);
        //getavgInfo(bean, con);
        // 判断背景色
        if (isTimeSetAlarm && StringUtils.isNotEmpty(bean.getStartTime()) && bean.getIavgtime() > 0
                && bean.getItask_timeoutnum() > 0)
        {
            // long red = Long
            // .parseLong(ServerEnv.getServerEnv().getSysConfig(ServerEnv.TIMESETALARM_LEVEL_GREEN,
            // "15"));
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try
            {
                date = format.parse(bean.getStartTime());
            } catch (ParseException e)
            {
                log.error("date format error!", e);
            }
            long beginTime = date.getTime();
            long runTime = System.currentTimeMillis() - beginTime;
            // 平均耗时 毫秒
            long avgNum = bean.getIavgtime();
            // 阈值
            double timetaskTime = bean.getItask_timeoutnum() * 60000;
            // double diff = runTime - threshold;
            // 绿色
            if (runTime < avgNum)
            {
                bean.setShowColor("true" + ";" + "green");
            } else if (avgNum <= runTime && runTime < (avgNum + timetaskTime))
            {
                // 黄色
                bean.setShowColor("true" + ";" + "yellow");
            } else if (runTime > (avgNum + timetaskTime))
            {
                // 橙色
                bean.setShowColor("true" + ";" + "orange");
            }
        } else
        {
            bean.setShowColor("false");
        }
    }


    public long getAvgTimeNew1 ( ActBean bean, Connection conn ) throws RepositoryException
    {

        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "select ROUND(avg(ia.IENDTIME - ia.IBEGINEXCTIME), 0)as avgtime,iw.IPROJECTNAME,ia.IACTNAME,iw.IFLOWNAME  "
                + "from ieai_workflowinstance  iw left join  ieai_actruntime ia  on iw.IFLOWID = ia.IFLOWID "
                + " where ia.IACTTYPE='Common' and ia.ISTATE='Finished' "
                + " and iw.IPROJECTNAME =? and  iw.IFLOWNAME =?  and ia.IACTNAME= ? and iw.IFLOWINSNAME >=?  and  iw.IFLOWINSNAME < ? "
                + " and ia.IENDTIME <> 0 group by  iw.IPROJECTNAME,iw.IFLOWNAME,ia.IACTNAME ";


        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        long avgRuntime = 0;
        Date date=new Date();
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        String endTime =sdf.format(date);
        calendar.add(Calendar.DAY_OF_MONTH,Integer.parseInt("-"+bean.getiAvgNum()));
        String startTime =sdf.format(calendar.getTime());


        // Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            if(bean.getiAvgNum()<=0){
                bean.setIavgtime(0);
                return 0;
            }
            // conn = DBResource.getConnection(method, log, Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            int index = 0;
            ps.setString(++index, bean.getPrjName());
            ps.setString(++index, bean.getFlowName());
            ps.setString(++index, bean.getActName());
            ps.setLong(++index, Long.parseLong(startTime));
            ps.setLong(++index, Long.parseLong(endTime));
            rs = ps.executeQuery();
            while (rs.next())
            {
                avgRuntime= rs.getLong("avgtime");
            }

            bean.setIavgtime(avgRuntime);
        } catch (SQLException e)
        {
            log.error("Exec sql Error  ", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, method, log);
        }

        return avgRuntime;

    }



    private void setActBeanAbnormalSwitch ( boolean isAbnormalSwitch, boolean isTimeSetAlarm, ActBean bean )
            throws RepositoryException
    {
        String utError = "uterror";
        String sysError = "syserror";
        if (isAbnormalSwitch)
        {
            if (bean.getActState().equals("Fail"))
            {
                ShellInfoBean shellInfoBean = ShellInfoManager.getInstance()
                        .getFinishSehllCmdStdoutFromFlowId(bean.getFlowID(), Constants.IEAI_IEAI);
                bean.setActStateDetail(shellInfoBean.getIstate() == -5 ? utError : sysError);
            } else if (bean.getActState().equals("Ready"))
            {
                bean.setActStateDetail(utError);
            } else if (bean.getActState().equals("Timeout"))
            {
                bean.setActStateDetail(sysError);
            } else if (bean.getActState().equals("Fail:Business"))
            {
                bean.setActStateDetail(sysError);
            }else if (bean.getActState().equals("Disconnect"))
            {
                bean.setActStateDetail(sysError);
            }
            if (StringUtils.isNotEmpty(bean.getActStateDetail()))
            {
                if (!(isTimeSetAlarm && bean.getActState().equals("Timeout")))
                {
                    bean.setShowColor("true" + ";" + (bean.getActStateDetail().equals(utError) ? "yellow" : "red"));
                }
            } else
            {
                if (StringUtils.isEmpty(bean.getShowColor()))
                {
                    bean.setShowColor("false" + ";" + "white");
                }
            }

        }
    }
    
    private void setActBeanSystemSwitch(Connection con , ActBean bean){
        Calendar calendar = Calendar.getInstance();
        long now = calendar.get(Calendar.YEAR);
        
        Date date2 = new Date();
        
        SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT);
        if(ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch()){
            String yesterDayBeginNextTime = getYesterDayBeginNextTime(con,bean.getActName());
            
            setYesterDayBeginNextTime(bean, sdf, yesterDayBeginNextTime, now);
            
            bean.setBeginNextTime(bean.getStartTime());
            
            String planBeginNextTime = getPlanBeginNextTime(con,bean);
            if(null != planBeginNextTime && 
                    !"".equals(planBeginNextTime) &&
                    !"null".equals(planBeginNextTime)){
                if(StringUtils.contains(yesterDayBeginNextTime,String.valueOf(now))){
                    date2.setTime(Long.parseLong(planBeginNextTime));
                    bean.setToDayPlanBeginTime(sdf.format(date2));
                }else{
                    bean.setToDayPlanBeginTime("");
                }
            }else{
                bean.setToDayPlanBeginTime(planBeginNextTime);
            }
            
            String endTime = getEndTime(con,bean);
            String planEndNextTime = getPlanEndNextTime(con,bean,endTime);
            setPlanEndNextTime(bean, sdf, planEndNextTime, yesterDayBeginNextTime, now);
        }
    }
    
    private void setYesterDayBeginNextTime( ActBean bean, SimpleDateFormat sdf,String yesterDayBeginNextTime,long now){
        Date date1 = new Date();
        if(null != yesterDayBeginNextTime && 
                !"".equals(yesterDayBeginNextTime) &&
                !"null".equals(yesterDayBeginNextTime)){
            if(StringUtils.contains(yesterDayBeginNextTime,String.valueOf(now))){
                date1.setTime(Long.parseLong(yesterDayBeginNextTime));
                bean.setYesterdayBeginNextTime(sdf.format(date1));
            }else{
                bean.setYesterdayBeginNextTime("");
            }
        }else{
            bean.setYesterdayBeginNextTime(yesterDayBeginNextTime);
        }
    }
    
    private void setPlanEndNextTime(ActBean bean,SimpleDateFormat sdf,String planEndNextTime,String yesterDayBeginNextTime,long now){
        Date date3 = new Date();
        if(null != planEndNextTime &&
                !"".equals(planEndNextTime) &&
                !"null".equals(planEndNextTime)){
            if(StringUtils.contains(yesterDayBeginNextTime,String.valueOf(now))){
                date3.setTime(Long.parseLong(planEndNextTime));
                bean.setToDayPlanEndTime(sdf.format(date3));
            }else{
                bean.setToDayPlanEndTime("");
            }
        }else{
            bean.setToDayPlanEndTime(planEndNextTime);
        }
    }
    
    /**
     * 
     * <li>Description:获取昨日活动执行开始时间</li> 
     * <AUTHOR>
     * 2019年2月27日 
     * @param conn
     * @param map
     * @return
     * return String
     */
    public String getYesterDayBeginNextTime(Connection conn,String actName){
        long beginTime = System.currentTimeMillis()/(1000*3600*24)*(1000*3600*24)
                - TimeZone.getDefault().getRawOffset()-86400000;
        long endTime = beginTime+24*60*60*1000-1;
        String method = "getYesterDayBeginNextTime";
        String yesterDayBeginTime = "";
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT MIN(IBEGINEXCTIME) AS IBEGINEXCTIME FROM IEAI_ACTRUNTIME "
                + " WHERE IACTNAME = ? AND IBEGINEXCTIME >= ? AND IBEGINEXCTIME <= ? ";
        
        for(int i=0,len=10;i<len;i++){
            try{
                ps = conn.prepareStatement(sql);
                ps.setString(1, actName);
                ps.setLong(2, beginTime);
                ps.setLong(3, endTime);
                rs = ps.executeQuery();
                
                while(rs.next()){
                    yesterDayBeginTime = rs.getString("IBEGINEXCTIME");
                }
                
                if(null == yesterDayBeginTime || "".equals(yesterDayBeginTime) || "null".equals(yesterDayBeginTime) || "0".equals(yesterDayBeginTime)){
                    yesterDayBeginTime = "";
                }
                break;
            } catch (SQLException e)
            {
                log.error("getYesterDayBeginNextTime has SQLException : "+e.getMessage());
            } catch (Exception e)
            {
                log.error("getYesterDayBeginNextTime has Exception : "+e.getMessage());
            }finally{
                DBResource.closePSRS(rs, ps, method, log);
            }
            
        }
        return yesterDayBeginTime;
    }

    /**
     * 
     * <li>Description:获得今日活动预计执行开始时间</li> 
     * <AUTHOR>
     * 2019年2月27日 
     * @param conn
     * @param map
     * @return
     * return String
     */
    public String getPlanBeginNextTime(Connection conn,ActBean bean){
        SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT);
        long fourBeginTime = System.currentTimeMillis()/(1000*3600*24)*(1000*3600*24)
                - TimeZone.getDefault().getRawOffset()-86400000*4;//四天前0点毫秒数 2019-02-23 00:00:00
        long fourEndTime = System.currentTimeMillis()/(1000*3600*24)*(1000*3600*24)
                - TimeZone.getDefault().getRawOffset()-86400000*4+24*60*60*1000-1;//2019-02-23 23:59:59
        
        long threeBeginTime = fourBeginTime+24*60*60*1000;//2019-02-24 00:00:00
        long threeEndTime = fourEndTime+24*60*60*1000;//2019-02-24 23:59:59
        
        long twoBeginTime = fourBeginTime+24*60*60*1000*2;//2019-02-25 00:00:00
        long twoEndTime = fourEndTime+24*60*60*1000*2;//2019-02-25 23:59:59
        
        long oneBeginTime = fourBeginTime+24*60*60*1000*3;//2019-02-25 00:00:00
        long oneEndTime = fourEndTime+24*60*60*1000*3;//2019-02-25 23:59:59
        String method = "getPlanBeginNextTime";
        long fourthDayBeginTime = -1;
        long thirdDayBeginTime = -1;
        long secondDayBeginTime = -1;
        long firshDayBeginTime = -1;
        long beginActTime = -1;
        try
        {
            String beginTime = bean.getStartTime();
            if(null == beginTime || "".equals(beginTime) || "null".equals(beginTime) || "0".equals(beginTime)){
                beginActTime = 0;
            }else{
                Date time = sdf.parse(beginTime);
                beginActTime = time.getTime(); 
            }
            
        } catch (ParseException e1)
        {
            log.error("转换时间异常："+e1.getMessage());
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT A.FOURTHDAYTIME,B.THIRDDAYTIME,C.SECONDDAYTIME,D.FIRSTDAYTIME FROM "
                +"(SELECT MIN(IBEGINEXCTIME) AS FOURTHDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IBEGINEXCTIME >= ? AND IBEGINEXCTIME <= ?)A,"
                +"(SELECT MIN(IBEGINEXCTIME) AS THIRDDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IBEGINEXCTIME >= ? AND IBEGINEXCTIME <= ?)B,"
                +"(SELECT MIN(IBEGINEXCTIME) AS SECONDDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IBEGINEXCTIME >= ? AND IBEGINEXCTIME <= ?)C,"
                +"(SELECT MIN(IBEGINEXCTIME) AS FIRSTDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IBEGINEXCTIME >= ? AND IBEGINEXCTIME <= ?)D ";
        
        for(int i=0,len=10;i<len;i++){
            try{
                ps = conn.prepareStatement(sql);
                ps.setString(1, bean.getActName());
                ps.setLong(2, fourBeginTime);
                ps.setLong(3, fourEndTime);
                ps.setString(4, bean.getActName());
                ps.setLong(5, threeBeginTime);
                ps.setLong(6, threeEndTime);
                ps.setString(7, bean.getActName());
                ps.setLong(8, twoBeginTime);
                ps.setLong(9, twoEndTime);
                ps.setString(10, bean.getActName());
                ps.setLong(11, oneBeginTime);
                ps.setLong(12, oneEndTime);
                rs = ps.executeQuery();
                
                while(rs.next()){
                    fourthDayBeginTime = rs.getLong("FOURTHDAYTIME");
                    thirdDayBeginTime = rs.getLong("THIRDDAYTIME");
                    secondDayBeginTime = rs.getLong("SECONDDAYTIME");
                    firshDayBeginTime = rs.getLong("FIRSTDAYTIME");
                }
                
                
            } catch (SQLException e)
            {
                log.error("getPlanBeginNextTime has SQLException : "+e.getMessage());
            } catch (Exception e)
            {
                log.error("getPlanBeginNextTime has Exception : "+e.getMessage());
            }finally{
                DBResource.closePSRS(rs, ps, method, log);
            }
            if(i>=0){
                break;
            }
        }
        return String.valueOf((fourthDayBeginTime+thirdDayBeginTime
                +secondDayBeginTime+firshDayBeginTime
                +beginActTime)/5);
    }
    
    /**
     * 
     * <li>Description:获取今日活动预计执行结束时间</li> 
     * <AUTHOR>
     * 2019年2月28日 
     * @param conn
     * @param map
     * @return
     * return String
     */
    public String getPlanEndNextTime(Connection conn,ActBean bean,String endTimeStr){
        SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT);
        long fourBeginTime = System.currentTimeMillis()/(1000*3600*24)*(1000*3600*24)
                - TimeZone.getDefault().getRawOffset()-86400000*4;//四天前0点毫秒数 
        long fourEndTime = System.currentTimeMillis()/(1000*3600*24)*(1000*3600*24)
                - TimeZone.getDefault().getRawOffset()-86400000*4+24*60*60*1000-1;
        
        long threeBeginTime = fourBeginTime+24*60*60*1000;
        long threeEndTime = fourEndTime+24*60*60*1000;
        
        long twoBeginTime = fourBeginTime+24*60*60*1000*2;
        long twoEndTime = fourEndTime+24*60*60*1000*2;
        
        long oneBeginTime = fourBeginTime+24*60*60*1000*3;
        long oneEndTime = fourEndTime+24*60*60*1000*3;
        String method = "getPlanBeginNextTime";
        long fourthDayBeginTime = -1;
        long thirdDayBeginTime = -1;
        long secondDayBeginTime = -1;
        long firshDayBeginTime = -1;
        long endTime = -1;
        try
        {
            if(null == endTimeStr || "".equals(endTimeStr) || "null".equals(endTimeStr) || "0".equals(endTimeStr)){
                endTime = 0;
            }else if(!endTimeStr.contains("-")){
                endTime=Long.parseLong(endTimeStr);
            }else{
                Date time = sdf.parse(endTimeStr);
                endTime = time.getTime();  
            }
        } catch (ParseException e1)
        {
            log.error("转换时间异常："+e1.getMessage());
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT A.FOURTHDAYTIME,B.THIRDDAYTIME,C.SECONDDAYTIME,D.FIRSTDAYTIME FROM "
                +"(SELECT MAX(IENDTIME) AS FOURTHDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IENDTIME >= ? AND IENDTIME <= ?)A,"
                +"(SELECT MAX(IENDTIME) AS THIRDDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IENDTIME >= ? AND IENDTIME <= ?)B,"
                +"(SELECT MAX(IENDTIME) AS SECONDDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IENDTIME >= ? AND IENDTIME <= ?)C,"
                +"(SELECT MAX(IENDTIME) AS FIRSTDAYTIME FROM IEAI_ACTRUNTIME "
                +" WHERE IACTNAME = ?  AND IENDTIME >= ? AND IENDTIME <= ?)D ";
        
        for(int i=0,len=10;i<len;i++){
            try{
                ps = conn.prepareStatement(sql);
                ps.setString(1, bean.getActName());
                ps.setLong(2, fourBeginTime);
                ps.setLong(3, fourEndTime);
                ps.setString(4, bean.getActName());
                ps.setLong(5, threeBeginTime);
                ps.setLong(6, threeEndTime);
                ps.setString(7, bean.getActName());
                ps.setLong(8, twoBeginTime);
                ps.setLong(9, twoEndTime);
                ps.setString(10, bean.getActName());
                ps.setLong(11, oneBeginTime);
                ps.setLong(12, oneEndTime);
                rs = ps.executeQuery();
                
                while(rs.next()){
                    fourthDayBeginTime = rs.getLong("FOURTHDAYTIME");
                    thirdDayBeginTime = rs.getLong("THIRDDAYTIME");
                    secondDayBeginTime = rs.getLong("SECONDDAYTIME");
                    firshDayBeginTime = rs.getLong("FIRSTDAYTIME");
                }
                
            } catch (SQLException e)
            {
                log.error("getPlanBeginNextTime has SQLException : "+e.getMessage());
            } catch (Exception e)
            {
                log.error("getPlanBeginNextTime has Exception : "+e.getMessage());
            }finally{
                DBResource.closePSRS(rs, ps, method, log);
            }
            if(i>=0){
                break;
            }
        }
        return String.valueOf((fourthDayBeginTime+thirdDayBeginTime
                +secondDayBeginTime+firshDayBeginTime
                +endTime)/5);
    }
    
    /**
     * 
     * <li>Description:根据活动名称和工作流ID获取活动结束时间</li> 
     * <AUTHOR>
     * 2019年3月26日 
     * @param conn
     * @param bean
     * @return
     * return String
     */
    public String getEndTime(Connection conn,ActBean bean){
        String method = "getEndTime";
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT IENDTIME FROM IEAI_ACTRUNTIME "
                + " WHERE IACTNAME = ? AND IFLOWID = ? ";
        String endTime = "";
        for(int i=0,len=10;i<len;i++){
            try{
                ps = conn.prepareStatement(sql);
                ps.setString(1, bean.getActName());
                ps.setLong(2, bean.getFlowID());
                rs = ps.executeQuery();
                
                while(rs.next()){
                    endTime = rs.getString("IENDTIME");
                }
                
                if(null == endTime || "".equals(endTime) || "null".equals(endTime)){
                    endTime = "";
                }
                break;
            } catch (SQLException e)
            {
                log.error("getEndTime has SQLException : "+e.getMessage());
            } catch (Exception e)
            {
                log.error("getEndTime has Exception : "+e.getMessage());
            }finally{
                DBResource.closePSRS(rs, ps, method, log);
            }
            
        }
        return endTime;
    }
    
    
    /**
     * 
     * <li>Description:所属系统下拉框查询</li> 
     * <AUTHOR>
     * 2019年3月26日 
     * @return
     * return Map
     */
    public Map querySystemCombox ( ActBean actBean )
    {
        Map map = new HashMap();
        String method = "querySystemCombox()";
        String sql = "SELECT MAX(IID) AS IID,IPRJ.ISYSNAME FROM IEAI_PROJECT IPRJ,( \n"
                + " SELECT PRJ.INAME,IPROID \n"
                + "      FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP ,IEAI_PROJECT PRJ \n"
                + "      WHERE UR.IROLEID=R.IID  \n" + "      AND R.IID=SP.IROLEID  \n" + "      AND UR.IUSERID=?  \n"
                + "      AND SP.IPERMISSION=1  \n" + "      AND SP.IPROID=PRJ.IID \n"
                + "      AND (PRJ.PROTYPE=? OR PRJ.PROTYPE=?) \n" + " ) QX \n"
                + " WHERE (IPRJ.IPKGCONTENTID<>0 or IPRJ.IPKGCONTENTID is null) AND IPRJ.IFREEZED = 0 AND IPRJ.ILATESTID=IPRJ.IID \n"
                + " AND (QX.INAME=IPRJ.INAME OR -QX.IPROID=IPRJ.PROTYPE) AND IID != -1 AND IPRJ.IGROUPID = 1"
                + " GROUP BY IPRJ.ISYSNAME ORDER BY IPRJ.ISYSNAME \n";
        List list = new ArrayList();
        for(int i = 0;i<10;i++){
            Connection conn = null;
            PreparedStatement ps = null;
            ResultSet rs = null;
            try{
                conn = DBResource.getConnection(method,log,Constants.IEAI_IEAI);
                ps = conn.prepareStatement(sql);
                ps.setString(1, actBean.getUserId());
                ps.setInt(2, actBean.getDbType());
                ps.setInt(3, -actBean.getDbType());
                rs = ps.executeQuery(); 
                
                while(rs.next()){
                    Map systemInfoMap = new HashMap();
                    systemInfoMap.put("prjId", rs.getString("IID"));
                    systemInfoMap.put("sysName", rs.getString("ISYSNAME"));
                    list.add(systemInfoMap);
                }
                map.put("dataList", list);
                break;
            } catch(RepositoryException e){
                log.error(method+" has a RepositoryException ! "+e.getMessage());
            } catch (SQLException e){
                log.error("querySystemCombox has SQLException : "+e.getMessage());
            } catch(Exception e){
                log.error(method+" has a Exception ! "+e.getMessage());
            } finally{
                DBResource.closeConn(conn, rs, ps, method, log);
            }
             
        }
        return map; 
    }

    public ShellInfoBean getFinishSehllCmdStdoutFromFlowId ( long flowId, int sysType ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        ShellInfoBean bean = null;
        String sql_search = "SELECT T.IFLOWNAME ,T.ISTATE,T.ISTDOUT,T.ILASTLINE,T.IRET FROM IEAI_SHELLCMD_OUTPUT T WHERE T.IFLOWID IN  (SELECT C.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO C WHERE C.IMAINFLOWID = ?) ORDER BY T.IID ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {

                    bean = new ShellInfoBean();
                    bean.setStdout("");
                    conn = DBResource.getConnection("getFinishSehllCmdStdoutFromFlowId", log, sysType);
                    actStat = conn.prepareStatement(sql_search);
                    actStat.setLong(1, flowId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        bean.setFlowname(actRS.getString("IFLOWNAME"));
                        bean.setIstate(actRS.getLong("ISTATE"));

                        if (null == bean.getStdout() || "".equals(bean.getStdout()))
                        {
                            bean.setStdout("-----------" + bean.getFlowname() + "-------------\n"
                                    + URLReplace.getInstance().getContent(actRS.getString("ISTDOUT")));

                        } else
                        {
                            bean.setStdout(URLReplace.getInstance().getContent(bean.getStdout()) + "\n-----------"
                                    + bean.getFlowname() + "-------------\n" + actRS.getString("ISTDOUT"));
                        }

                        bean.setLastline(actRS.getString("ILASTLINE"));
                        bean.setRet(actRS.getString("IRET"));
                        // break;
                    }
                } catch (SQLException e)
                {
                    log.error("getFinishSehllCmdStdoutFromFlowId method of ShellInfoManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getFinishSehllCmdStdoutFromFlowId", log);
                }
                break;
            } catch (RepositoryException ex)
            {
                log.error("getFinishSehllCmdStdoutFromFlowId method of ShellInfoManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return bean;
    }
    
    /***
     * 
     * <li>渤海银行获取背景颜色新逻辑:</li> 
     * <AUTHOR>
     * 2022年2月17日 
     * @param setTimeSetAlarmForBh
     * @param bean
     * @param con
     * return void
     */
    private void setTimeSetAlarmForBh ( boolean isTimeSetAlarm, ActBean bean, Connection con )
    {
        // 取得超时配置
        getTimeConfigInfo(bean, con);
        // 判断背景色
        if (isTimeSetAlarm && StringUtils.isNotEmpty(bean.getStartTime()) && bean.getItask_timeoutnum() > 0)
        {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try
            {
                date = format.parse(bean.getStartTime());
            } catch (ParseException e)
            {
                log.error("date format error!", e);
            }
            long beginTime = date.getTime();
            long runTime = System.currentTimeMillis() - beginTime;
            // 阈值
            double timetaskTime = bean.getItask_timeoutnum() * 60000;
            // double diff = runTime - threshold;
            // 绿色
            if (runTime <= timetaskTime * 0.85)
            {
                bean.setShowColor("true" + ";" + "green");
            } else if (runTime >= timetaskTime * 0.85 && runTime <= timetaskTime )
            {
                // 黄色
                bean.setShowColor("true" + ";" + "yellow");
            } else if (runTime > timetaskTime)
            {
                // 红色
                bean.setShowColor("true" + ";" + "red");
            }
        } else
        {
            bean.setShowColor("false");
        }
    }
    public Map getProjectNameByFlowId ( Long flowId ) throws RepositoryException
    {
        Connection conn = null;
        Map map = new HashMap();
        String sql = "select t.iprojectname,t.iflowname  from ieai_workflowinstance t where t.iflowid=?";
        for (int i = 0;; i++)
        {
            try
            {
                conn = DBResource.getConnection("getProjectNameByFlowId", log, Constants.IEAI_IEAI);
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, flowId);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        map.put("projectname", rs.getString("iprojectname"));
                        map.put("flowname", rs.getString("iflowname"));
                        return map;
                    }
                } catch (SQLException e)
                {
                    log.error("getProjectNameByFlowId method of ActMonitorManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "getProjectNameByFlowId", log);
                }
            } catch (RepositoryException ex)
            {
                log.error("getProjectNameByFlowId method of ActMonitorManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
            return map;
        }
    }

    /**
     * 获取排队状态的活动
     * @return
     * @throws RepositoryException
     */
    public List<ActBean> getQueueUpWorkFlow () throws RepositoryException
    {
        List<ActBean> res = new ArrayList();
        String sql1 = "SELECT B.ISTARTTIME,B.IACTQUEUEUPTIME,B.IFLOWID,B.IFLOWINSNAME,B.IFLOWNAME,B.IPROJECTNAME\n" +
                "FROM IEAI_WORKFLOWINSTANCE B\n" +
                "WHERE B.ISTATUS = '0' ";
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement actStat = null;
            ResultSet actRS = null;
            Connection con = null;
            try
            {
                con = DBResource.getConnection("getQueueUpWorkFlow", log, Constants.IEAI_HEALTH_INSPECTION);
                actStat = con.prepareStatement(sql1);
                actRS = actStat.executeQuery();
                while (actRS.next())
                {
                    ActBean model = new ActBean();
                    model.setFlowID(actRS.getLong("IFLOWID"));
                    model.setFlowName(actRS.getString("IFLOWNAME"));
                    model.setFlowInsName(actRS.getString("IFLOWINSNAME"));
                    model.setPrjName(actRS.getString("IPROJECTNAME"));
                    model.setBeginexctime(actRS.getLong("IACTQUEUEUPTIME"));
                    model.setStartTime(actRS.getString("ISTARTTIME"));
                    res.add(model);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } catch (SQLException e)
            {
                log.error("getQueueUpWorkFlow is error ! " + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConn(con, actRS, actStat, "getQueueUpWorkFlow", log);
            }
        }
        return res;
    }

    public boolean getFilterActList(Long flowID) throws RepositoryException {
        boolean flag = false;
        String sql = "SELECT 1 FROM ieai_actruntime WHERE IACTDEFNAME='Delayer' AND IFLOWID=? LIMIT 1";
        String sql2 = "SELECT 1 FROM IEAI_EXCEL_CRON_JOB WHERE IMAINFLOWID=? LIMIT 1";
        PreparedStatement ps = null;
        ResultSet actRS = null;
        PreparedStatement ps2 = null;
        ResultSet actRS2 = null;
        Connection con = null;
        try {
            con = DBResource.getConnection("getFilterActList", log, Constants.IEAI_HEALTH_INSPECTION);
            ps = con.prepareStatement(sql);
            // 关键：设置flowID参数
            ps.setLong(1, flowID);
            actRS = ps.executeQuery();
            // 检查是否有结果
            if (actRS.next()) {
                flag = true; // 存在匹配记录
            }

            ps2 = con.prepareStatement(sql2);
            ps2.setLong(1, flowID);
            actRS2 = ps2.executeQuery();
            if (actRS2.next()) {
                flag = true;
            }
        } catch (SQLException e) {
            log.error("getFilterActList query failed! FlowID: " + flowID + ", Error: " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(con, actRS, ps, "getFilterActList", log);
            DBResource.closePSRS(actRS2,ps2,"getFilterActList",log);
        }

        return flag;
    }
}
