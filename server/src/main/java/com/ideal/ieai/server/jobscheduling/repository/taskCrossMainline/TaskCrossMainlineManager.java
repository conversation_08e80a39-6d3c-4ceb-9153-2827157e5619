package com.ideal.ieai.server.jobscheduling.repository.taskCrossMainline;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.ActInfoBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.util.PageParamBean;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;

/**
 * 名称:TaskCrossMainlineManager<br>
 * 描述:<br>
 * 类型:JAVA<br>
 * 最近修改时间: <br>
 *
 * <AUTHOR>
 */
public class TaskCrossMainlineManager {
    private static final Logger logger = Logger.getLogger(TaskCrossMainlineManager.class);

    private static final TaskCrossMainlineManager self = new TaskCrossMainlineManager();

    private Connection connForId = null;

    public static TaskCrossMainlineManager getInstance() {
        return self;
    }

    public Map<String, List<TaskCrossMainlineBean>> checkTaskExist(Connection conn, List<TaskCrossMainlineBean> crossMainlineBeanList) {
       /* String sql = "select count(*) from IEAI_EXCELMODEL " +
                " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ? and IACTNAME = ?";*/
        String sql = "select count(*) from IEAI_EXCELMODEL " +
                " where IMAINPRONAME=? and IMAINLINENAME = ? and IACTNAME = ?";

        //      String sqlDep = "select count(*) from IEAI_EXCELMODEL" + " where IMAINPRONAME=? and IMAINLINENAME = ? ";

        String sqlDepAct = "select count(*) from IEAI_EXCELMODEL" + " where IMAINPRONAME=? and IMAINLINENAME = ? and IACTNAME = ?";

        List<TaskCrossMainlineBean> mainErrorList = new ArrayList<>();
        List<TaskCrossMainlineBean> depErrorList = new ArrayList<>();

        crossMainlineBeanList.forEach(item -> {
            try {

                PreparedStatement sqlPre = conn.prepareStatement(sql);
                //PreparedStatement sqlDepPre = conn.prepareStatement(sqlDep);
                PreparedStatement sqlDepActPre = conn.prepareStatement(sqlDepAct);
                sqlPre.setString(1, item.getProjectName());
                sqlPre.setString(2, item.getMainLineName());
                sqlPre.setString(3, item.getActName());
                ResultSet rs = sqlPre.executeQuery();
                //ResultSet depRs = null;
                ResultSet depActRs = null;
                if (rs.next() && rs.getInt(1) == 0) {
                    mainErrorList.add(item);
                }
                //判断是依赖整个主线，还是依赖单个作业
//                if(StringUtils.isBlank(item.getDependActName())){
//                    // todo 检验放行
//                    sqlDepPre.setString(1, item.getDependProjectName());
//                    sqlDepPre.setString(2, item.getDependMainLineName());
////                    sqlPre.setString(1, item.getDependProjectName());
////                    sqlPre.setString(2, item.getDependMainLineName());
////                    sqlPre.setString(3, item.getDependActName());
//                    depRs = sqlDepPre.executeQuery();
//                    if (depRs.next() && depRs.getInt(1) == 0) {
//                        depErrorList.add(item);
//                    }
//                }else{
                sqlDepActPre.setString(1, item.getDependProjectName());
                sqlDepActPre.setString(2, item.getDependMainLineName());
                sqlDepActPre.setString(3, item.getDependActName());
                depActRs = sqlDepActPre.executeQuery();
                if (depActRs.next() && depActRs.getInt(1) == 0) {
                    depErrorList.add(item);
//                    }
                }
                DBResource.closePSRS(depActRs, sqlDepActPre, "getTaskCrossMainlineInfo", logger);
//                DBResource.closePSRS(depRs, sqlDepPre, "getTaskCrossMainlineInfo", logger);
//                DBResource.closeResultSet(rs, "getTaskCrossMainlineInfo", logger);
//                DBResource.closeResultSet(depRs, "getTaskCrossMainlineInfo", logger);
            } catch (SQLException e) {
                e.printStackTrace();
                logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
            }
        });
        Map<String, List<TaskCrossMainlineBean>> result = new HashMap<>(2);
        if (!CollectionUtils.isEmpty(mainErrorList)) {
            result.put("mainErrorList", mainErrorList);
        }
        if (!CollectionUtils.isEmpty(depErrorList)) {
            result.put("depErrorList", depErrorList);
        }
        return result;
    }

    public List<TaskCrossMainlineBean> save(Connection conn, List<TaskCrossMainlineBean> crossMainlineBeanList, String userName) {
        List<TaskCrossMainlineBean> list = new ArrayList();
        String sql = "INSERT INTO IEAI_CROSS_MAIN_LINE(IID,IMAINPRONAME,IMAINLINENAME,IACTNAME,IDEPMAINPRONAME," +
                "IDEPMAINLINENAME,IDEPACTNAME,IUSERNAME) VALUES (?,?,?,?,?,?,?,?)";

        try
        {
            connForId = DBResource.getConnection("save", logger, Constants.IEAI_IEAI_BASIC);
            safeUpdate("save", conn, (ps) -> {

                try {
                    ps = conn.prepareStatement(sql);
                } catch (SQLException sqlException) {
                    sqlException.printStackTrace();
                    logger.error(sqlException);
                }
                PreparedStatement finalPs = ps;
                crossMainlineBeanList.forEach(item -> {
                    try {
                        TaskCrossMainlineBean tmbean = new TaskCrossMainlineBean();
                        long mainId = IdGenerator.createIdForExecActBig("IEAI_CROSS_MAIN_LINE", crossMainlineBeanList.size(), connForId);
                        finalPs.setLong(1, mainId);
                        finalPs.setString(2, item.getProjectName());
                        finalPs.setString(3, item.getMainLineName());
                        finalPs.setString(4, item.getActName());
                        finalPs.setString(5, item.getDependProjectName());
                        finalPs.setString(6, item.getDependMainLineName());
                        finalPs.setString(7, item.getDependActName());
                        finalPs.setString(8, userName);
                        finalPs.executeUpdate();
                        //存储到list中，调用线程，修改连线
                        tmbean.setActName(item.getActName());
                        tmbean.setDependActName(item.getDependActName());
                        list.add(tmbean);
                    } catch (SQLException | RepositoryException e) {
                        e.printStackTrace();
                        logger.error(e);
                    }
                });
                DBResource.closePreparedStatement(finalPs, "getTaskCrossMainlineInfo", logger);
                return true;
            });
        } catch (RepositoryException e)
        {
            logger.error("save is error",e);
        } finally{
            DBResource.closeConnection(connForId, "save", logger);
        }
        return list;

    }

    private Boolean safeUpdate(String method, Connection conn, Function<PreparedStatement, Boolean> func) {
        PreparedStatement ps = null;
        return func.apply(ps);
    }

    /**
     * @param conn
     * @param taskCrossMainlineBean
     * @param limit
     * @param start
     * @return
     * @Title: getTaskCrossMainlineInfo
     * @Description: TODO(查询跨主线信息)
     * @author: bo_yang
     * @date: 2021年5月12日 下午1:38:04
     */
    public Map<String, Object> getTaskCrossMainlineInfo(Connection conn, TaskCrossMainlineBean taskCrossMainlineBean,
                                                        int limit, int start) {
        String sql = "select * from IEAI_CROSS_MAIN_LINE ICML where 1=1 ";
        if (StringUtils.isNotEmpty(taskCrossMainlineBean.getProjectName())) {
            sql += "and ICML.IMAINPRONAME LIKE " + "'%" + taskCrossMainlineBean.getProjectName().trim() + "%' ";
        }
        if (StringUtils.isNotEmpty(taskCrossMainlineBean.getMainLineName())) {
            sql += "and ICML.IMAINLINENAME LIKE " + "'%" + taskCrossMainlineBean.getMainLineName().trim() + "%' ";
        }
        if (StringUtils.isNotEmpty(taskCrossMainlineBean.getActName())) {
            sql += "and ICML.IACTNAME LIKE " + "'%" + taskCrossMainlineBean.getActName().trim() + "%' ";
        }
        if (StringUtils.isNotEmpty(taskCrossMainlineBean.getDependProjectName())) {
            sql += "and ICML.IDEPMAINPRONAME LIKE " + "'%" + taskCrossMainlineBean.getDependProjectName().trim()
                    + "%' ";
        }
        if (StringUtils.isNotEmpty(taskCrossMainlineBean.getDependActName())) {
            sql += "and ICML.IDEPACTNAME LIKE " + "'%" + taskCrossMainlineBean.getDependActName().trim() + "%' ";
        }
        if (StringUtils.isNotEmpty(taskCrossMainlineBean.getDependMainLineName())) {
            sql += "and ICML.IDEPMAINLINENAME LIKE " + "'%" + taskCrossMainlineBean.getDependMainLineName().trim()
                    + "%' ";
        }
        PageParamBean pageParamBean = PageParamBean.getInstance().getParamPageBean(sql, "ORDER BY  IMAINPRONAME DESC",
                start, limit);
        List<Map<String, Object>> resultList = new ArrayList<>();

        Map<String, Object> resultMap = new HashMap<>();
        ResultSet rs = null;
        PreparedStatement sqlPre = null;
        PreparedStatement totalPs;
        ResultSet totalRset;

        try {

            sqlPre = conn.prepareStatement(pageParamBean.getSql());
            sqlPre.setInt(1, pageParamBean.getPagePara1());
            sqlPre.setInt(2, pageParamBean.getPagePara2());
            rs = sqlPre.executeQuery();
            while (rs.next()) {
                Map<String, Object> map = new HashMap<>();
                map.put("projectName", rs.getString("IMAINPRONAME"));
                map.put("mainLineName", rs.getString("IMAINLINENAME"));
                map.put("actName", rs.getString("IACTNAME"));
                map.put("dependProjectName", rs.getString("IDEPMAINPRONAME"));
                map.put("dependMainLineName", rs.getString("IDEPMAINLINENAME"));
                map.put("dependActName", rs.getString("IDEPACTNAME"));
                map.put("iid", rs.getString("iid"));
                resultList.add(map);
            }

            totalPs = conn.prepareStatement(pageParamBean.toCount(sql));
            totalRset = totalPs.executeQuery();
            while (totalRset.next()) {
                resultMap.put("total", totalRset.getInt(1));
            }
        } catch (SQLException e) {
            logger.error("获取当前所有跨主线依赖活动错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getTaskCrossMainlineInfo", logger);
        }
        resultMap.put("dataList", resultList);

        return resultMap;
    }

    /**
     * 获取当前跨主线依赖库中存储的excelModel匹配数据
     *
     * @param conn 数据库连接
     * @return 有跨主线依赖的excelModel列表
     */
    public List<TaskCrossMainlineBean> getAllCrossMainlineList(Connection conn) {
        String sql = "select IMAINPRONAME, IMAINLINENAME, IACTNAME, IDEPMAINLINENAME, IDEPMAINPRONAME, IDEPACTNAME from IEAI_CROSS_MAIN_LINE ";
        List<TaskCrossMainlineBean> resultList = new ArrayList<>();
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        try {
            sqlPre = conn.prepareStatement(sql);
            rs = sqlPre.executeQuery();
            while (rs.next()) {
                TaskCrossMainlineBean taskCrossMainlineBean = new TaskCrossMainlineBean();
                taskCrossMainlineBean.setProjectName(rs.getString("IMAINPRONAME"));
                taskCrossMainlineBean.setMainLineName(rs.getString("IMAINLINENAME"));
                taskCrossMainlineBean.setActName(rs.getString("IACTNAME"));
                taskCrossMainlineBean.setDependProjectName(rs.getString("IDEPMAINPRONAME"));
                taskCrossMainlineBean.setDependMainLineName(rs.getString("IDEPMAINLINENAME"));
                taskCrossMainlineBean.setDependActName(rs.getString("IDEPACTNAME"));
                resultList.add(taskCrossMainlineBean);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("获取当前所有跨主线依赖活动错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getAllActExcelModel", logger);
        }
        return resultList;
    }


    public void getActExcelModel(Connection conn, ExcelModelBean condition) {
      /*  String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ? and IACTNAME = ?";*/
        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IMAINPRONAME=? and  IMAINLINENAME = ? and IACTNAME = ?";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setString(1, condition.getiMainProName());
            sqlPre.setString(2, condition.getiMainlineName());
            sqlPre.setString(3, condition.getiActName());
            rs = sqlPre.executeQuery();

            int index = 0;
            while (rs.next()) {
                condition.setiSelfOperationId(rs.getLong("IOPERATIONID"));
                condition.setiChildProjectName(rs.getString("ICHILDPRONAME"));
                index++;
            }
            if (index > 1) {
                logger.error("跨主线依赖存在异常数据请查验");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getAllActExcelModel", logger);
        }
    }

    public void getActExcelModelCopy(Connection conn, ExcelModelBean condition) {
        /*  String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                  + " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ? and IACTNAME = ?";*/
        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL_COPY "
                + " where IMAINPRONAME=? and  IMAINLINENAME = ? and IACTNAME = ?";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setString(1, condition.getiMainProName());
            sqlPre.setString(2, condition.getiMainlineName());
            sqlPre.setString(3, condition.getiActName());
            rs = sqlPre.executeQuery();

            int index = 0;
            while (rs.next()) {
                condition.setiSelfOperationId(rs.getLong("IOPERATIONID"));
                condition.setiChildProjectName(rs.getString("ICHILDPRONAME"));
                index++;
            }
            if (index > 1) {
                logger.error("跨主线依赖存在异常数据请查验");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getAllActExcelModel", logger);
        }
    }

    public void getActExcelModelNew(Connection conn, ExcelModelBean condition) {
        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ? and IACTNAME = ?";
        long iOperationId = 0;
        ResultSet rs = null;
        PreparedStatement sqlPre = null;
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setString(1, condition.getiMainProName());
            sqlPre.setString(2, condition.getiMainlineName());
            sqlPre.setString(3, condition.getiActName());
            rs = sqlPre.executeQuery();


            int index = 0;
            while (rs.next()) {
                condition.setiOperationId(rs.getLong("IOPERATIONID"));
                condition.setiChildProjectName(rs.getString("ICHILDPRONAME"));
                index++;
            }
            if (index > 1) {
                logger.error("跨主线依赖存在异常数据请查验");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getAllActExcelModel", logger);
        }


    }

    public long getIoperaTionId(Connection conn, ExcelModelBean condition) {
/*        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ? and IACTNAME = ?";*/
        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where  IMAINPRONAME=? and  IMAINLINENAME = ? and IACTNAME = ?";
        long iOperationId = 0;
        ResultSet rs = null;
        PreparedStatement sqlPre = null;
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setString(1, condition.getiMainProName());
            sqlPre.setString(2, condition.getiMainlineName());
            sqlPre.setString(3, condition.getiActName());
            rs = sqlPre.executeQuery();


            while (rs.next()) {
                iOperationId = rs.getLong("IOPERATIONID");

            }

        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getAllActExcelModel", logger);
        }
        return iOperationId;
    }

    public long getIoperaTionIdCopy(Connection conn, ExcelModelBean condition) {
        /*        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                        + " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ? and IACTNAME = ?";*/
        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL_COPY "
                + " where  IMAINPRONAME=? and  IMAINLINENAME = ? and IACTNAME = ?";
        long iOperationId = 0;
        ResultSet rs = null;
        PreparedStatement sqlPre = null;
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setString(1, condition.getiMainProName());
            sqlPre.setString(2, condition.getiMainlineName());
            sqlPre.setString(3, condition.getiActName());
            rs = sqlPre.executeQuery();


            while (rs.next()) {
                iOperationId = rs.getLong("IOPERATIONID");

            }

        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getIoperaTionIdCopy", logger);
        }
        return iOperationId;
    }

    public List<ExcelModelBean> getAllActExcelModel(Connection conn, String mainlineName, String mainProName)
            throws RepositoryException {
      /*  String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ?";*/
        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IMAINPRONAME=? and IMAINLINENAME = ?";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        List<ExcelModelBean> excelModelBeanList = new ArrayList<ExcelModelBean>();
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setString(1, mainProName);
            sqlPre.setString(2, mainlineName);
            rs = sqlPre.executeQuery();
            while (rs.next()) {
                ExcelModelBean excelModelBean = new ExcelModelBean();
                excelModelBean.setiChildProjectName(rs.getString("ICHILDPRONAME"));
                excelModelBean.setiActName(rs.getString("IACTNAME"));
                excelModelBean.setiMainlineName(rs.getString("IMAINLINENAME"));
                excelModelBean.setiOperationId(rs.getLong("IOPERATIONID"));
                excelModelBean.setiMainProName(rs.getString("IMAINPRONAME"));
                excelModelBeanList.add(excelModelBean);
            }

        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "getAllActExcelModel", logger);
        }
        return excelModelBeanList;
    }


    public List<ExcelModelBean> getActExcelModel(Connection conn, String mainlineName, String mainProName,String actName)
            throws RepositoryException {
      /*  String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IPRJUUID in (select IUUID from IEAI_PROJECT where iid = ILATESTID and INAME = ?)  and IMAINLINENAME = ?";*/
        String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,ICHILDPRONAME,IACTNAME from IEAI_EXCELMODEL "
                + " where IMAINPRONAME=? and IMAINLINENAME = ? and IACTNAME = ?";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        List<ExcelModelBean> excelModelBeanList = new ArrayList<>();
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setString(1, mainProName);
            sqlPre.setString(2, mainlineName);
            sqlPre.setString(3, actName);
            rs = sqlPre.executeQuery();
            while (rs.next()) {
                ExcelModelBean excelModelBean = new ExcelModelBean();
                excelModelBean.setiChildProjectName(rs.getString("ICHILDPRONAME"));
                excelModelBean.setiActName(rs.getString("IACTNAME"));
                excelModelBean.setiMainlineName(rs.getString("IMAINLINENAME"));
                excelModelBean.setiOperationId(rs.getLong("IOPERATIONID"));
                excelModelBean.setiMainProName(rs.getString("IMAINPRONAME"));
                excelModelBeanList.add(excelModelBean);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("验证跨主线依赖所依赖任务是否存在sql执行错误", e);
        } finally {
            DBResource.closeConn(conn,rs,sqlPre,"getActExcelModel", logger);
        }
        return excelModelBeanList;
    }

    /**
     * @param conn
     * @param excelModelBeanList
     * @return
     * @Title: deletePreAndSuccForCrossMainline
     * @Description: TODO(删除跨主线依赖)
     * @author: bo_yang
     * @date: 2021年5月13日 下午4:39:45
     */
    public Boolean deletePreAndSuccForCrossMainline(Connection conn, ExcelModelBean succExcelModelBean,
                                                    ExcelModelBean preExcelModelBean) {
        String sql_delete_actsucc = "DELETE from IEAI_ACTSUCC where  IOPERATIONID=? AND ISUCCACTNAME=? AND IPROJECTNAME=? AND IMAINLINENAME=? AND ISELFOPERATIONID=?";
        String sql_delete_actpre = "DELETE from IEAI_ACTPRE where  IOPERATIONID=? AND IPREACTNAME =? AND IPROJECTNAME=? AND IMAINLINENAME=? AND ISELFOPERATIONID=?";

        String sql_delete_actsucc_copy = "DELETE from IEAI_ACTSUCC_COPY where  IOPERATIONID=? AND ISUCCACTNAME=? AND IPROJECTNAME=? AND IMAINLINENAME=? AND ISELFOPERATIONID=?";
        String sql_delete_actpre_copy = "DELETE from IEAI_ACTPRE_COPY where  IOPERATIONID=? AND IPREACTNAME =? AND IPROJECTNAME=? AND IMAINLINENAME=? AND ISELFOPERATIONID=?";

        Boolean flag = true;
        PreparedStatement actStat_actsucc = null;
        PreparedStatement actStat_actpre = null;
        PreparedStatement actStat_actsucc_copy = null;
        PreparedStatement actStat_actpre_copy = null;


        try {

            actStat_actsucc = conn.prepareStatement(sql_delete_actsucc);
            actStat_actsucc.setLong(1, succExcelModelBean.getiOperationId());
            actStat_actsucc.setString(2, succExcelModelBean.getiActName());
            actStat_actsucc.setString(3, succExcelModelBean.getiMainProName());
            actStat_actsucc.setString(4, succExcelModelBean.getiMainlineName());
            actStat_actsucc.setLong(5, succExcelModelBean.getiSelfOperationId());

            logger.info("删除后继正式表："+succExcelModelBean.toString());

            actStat_actpre = conn.prepareStatement(sql_delete_actpre);
            actStat_actpre.setLong(1, preExcelModelBean.getiOperationId());
            actStat_actpre.setString(2, preExcelModelBean.getiActName());
            actStat_actpre.setString(3, preExcelModelBean.getiMainProName());
            actStat_actpre.setString(4, preExcelModelBean.getiMainlineName());
            actStat_actpre.setLong(5, preExcelModelBean.getiSelfOperationId());

            logger.info("删除前继正式表："+preExcelModelBean.toString());

            actStat_actsucc_copy = conn.prepareStatement(sql_delete_actsucc_copy);
            actStat_actsucc_copy.setLong(1, succExcelModelBean.getiOperationId());
            actStat_actsucc_copy.setString(2, succExcelModelBean.getiActName());
            actStat_actsucc_copy.setString(3, succExcelModelBean.getiMainProName());
            actStat_actsucc_copy.setString(4, succExcelModelBean.getiMainlineName());
            actStat_actsucc_copy.setLong(5, succExcelModelBean.getiSelfOperationId());

            logger.info("删除后继copy表："+succExcelModelBean.toString());

            actStat_actpre_copy = conn.prepareStatement(sql_delete_actpre_copy);
            actStat_actpre_copy.setLong(1, preExcelModelBean.getiOperationId());
            actStat_actpre_copy.setString(2, preExcelModelBean.getiActName());
            actStat_actpre_copy.setString(3, preExcelModelBean.getiMainProName());
            actStat_actpre_copy.setString(4, preExcelModelBean.getiMainlineName());
            actStat_actpre_copy.setLong(5, preExcelModelBean.getiSelfOperationId());

            logger.info("删除前继copy表："+preExcelModelBean.toString());

            actStat_actsucc.execute();
            actStat_actpre.execute();
            actStat_actsucc_copy.execute();
            actStat_actpre_copy.execute();
            //conn.commit();
            flag = true;
        } catch (SQLException e) {
            logger.error("获取当前所有跨主线依赖活动错误", e);
            flag = false;
        } finally {
            DBResource.closePreparedStatement(actStat_actsucc, "delateTaskCrossMainlineInfo", logger);
            DBResource.closePreparedStatement(actStat_actpre, "delateTaskCrossMainlineInfo", logger);
            DBResource.closePreparedStatement(actStat_actsucc_copy, "delateTaskCrossMainlineInfo", logger);
            DBResource.closePreparedStatement(actStat_actpre_copy, "delateTaskCrossMainlineInfo", logger);
        }

        return flag;
    }

    public Boolean delateTaskCrossMainlineInfo(Connection conn, TaskCrossMainlineBean taskCrossMainlineBean, Map map) {
        String sql_delete_taskCrossMainline = "DELETE from IEAI_CROSS_MAIN_LINE  where ";

        sql_delete_taskCrossMainline += " IMAINPRONAME= '" + taskCrossMainlineBean.getProjectName().trim() + "'";
        sql_delete_taskCrossMainline += "and IMAINLINENAME='" + taskCrossMainlineBean.getMainLineName().trim()
                + "'";
        sql_delete_taskCrossMainline += "and IACTNAME='" + taskCrossMainlineBean.getActName().trim() + "'";
        sql_delete_taskCrossMainline += "and IDEPMAINPRONAME='"
                + taskCrossMainlineBean.getDependProjectName().trim() + "'";
        if (null!=taskCrossMainlineBean.getDependActName()) {
            sql_delete_taskCrossMainline += "and IDEPACTNAME='" + taskCrossMainlineBean.getDependActName().trim()
                    + "'";
        }
        sql_delete_taskCrossMainline += "and IDEPMAINLINENAME='"
                + taskCrossMainlineBean.getDependMainLineName().trim() + "'";

        Boolean flag = true;
        PreparedStatement actStat = null;
        for (int i = 0; i < 10; i++) {
            try {
                actStat = conn.prepareStatement(sql_delete_taskCrossMainline);
                int updatesucc = actStat.executeUpdate();
                if(updatesucc == 0){
                    map.put("msg", "删除失败,"+ taskCrossMainlineBean.getProjectName()+"-"+taskCrossMainlineBean.getActName()+" 依赖   "+
                            taskCrossMainlineBean.getDependProjectName()+"-"+taskCrossMainlineBean.getDependActName()+" 关系不存在！");
                    flag = false;
                    throw new RuntimeException("删除跨主线依赖失败");
                }else{
                    flag = true;
                    break;
                }
                //conn.commit();
            } catch (SQLException e) {
                logger.error("删除跨主线依赖错误", e);
                flag = false;
            } finally {
                DBResource.closePreparedStatement(actStat, "delateTaskCrossMainlineInfoById", logger);
            }
        }
        return flag;
    }

    public Boolean delateTaskCrossMainlineInfoByIds(Connection conn, String taskCrossMainlineBeanIds) {
        String sql_delete_taskCrossMainline = "DELETE from IEAI_CROSS_MAIN_LINE where IID in ("
                + taskCrossMainlineBeanIds + ") ";
        Boolean flag = true;
        PreparedStatement actStat = null;
        for (int i = 0; i < 10; i++) {
            try {
                actStat = conn.prepareStatement(sql_delete_taskCrossMainline);
                actStat.executeUpdate();
                conn.commit();
                flag = true;
            } catch (SQLException e) {
                logger.error("删除跨主线依赖错误", e);
                flag = false;
            } finally {
                DBResource.closePreparedStatement(actStat, "delateTaskCrossMainlineInfoById", logger);
            }
        }
        return flag;
    }

    public List<TaskCrossMainlineBean> selectTaskCrossMainlineInfoByIds(Connection conn,
                                                                        String taskCrossMainlineBeanIds) {
        String sql_select_taskCrossMainline = "select * from IEAI_CROSS_MAIN_LINE ICML where ICML.IID in ("
                + taskCrossMainlineBeanIds + ") ";
        PreparedStatement actStat = null;
        ResultSet rs = null;
        List<TaskCrossMainlineBean> taskCrossMainlineBeanList = new ArrayList<>();
        //for (int i = 0; i < 10; i++) {
        try {
            actStat = conn.prepareStatement(sql_select_taskCrossMainline);
            rs = actStat.executeQuery();
            while (rs.next()) {
                TaskCrossMainlineBean taskCrossMainlineBean = new TaskCrossMainlineBean();
                taskCrossMainlineBean.setActName(rs.getString("IACTNAME"));
                taskCrossMainlineBean.setDependActName(rs.getString("IDEPACTNAME"));
                taskCrossMainlineBean.setDependMainLineName(rs.getString("IDEPMAINLINENAME"));
                taskCrossMainlineBean.setMainLineName(rs.getString("IMAINLINENAME"));
                taskCrossMainlineBean.setProjectName(rs.getString("IMAINPRONAME"));
                taskCrossMainlineBean.setDependProjectName(rs.getString("IDEPMAINPRONAME"));
                taskCrossMainlineBean.setIid(Long.parseLong(rs.getString("iid")));
                taskCrossMainlineBeanList.add(taskCrossMainlineBean);
            }
        } catch (SQLException e) {
            logger.error("查询跨主线依赖错误", e);
        } finally {
            DBResource.closePSRS(rs, actStat, "selectTaskCrossMainlineInfoByIds", logger);
        }
        //}
        return taskCrossMainlineBeanList;
    }

    public void saveActPre(Connection conn, List<ExcelModelBean> pre) {
        String sql = "INSERT INTO IEAI_ACTPRE(IOPERATIONID, IPREACTNAME, ICHILDPROJECTNAME, IPROJECTNAME, IMAINLINENAME, ISELFOPERATIONID) VALUES (?,?,?,?,?,?)";
        safeUpdate("saveActPre", conn, (sqlPre) -> {
            Boolean execute = false;
            try {
                sqlPre = conn.prepareStatement(sql);
                for (ExcelModelBean excelModelBean : pre) {
                    Boolean CC = TaskCrossMainlineManager.getInstance().booleanActPre(conn, excelModelBean,"");
                    if (!CC) {

                        sqlPre.setLong(1, excelModelBean.getiOperationId());
                        sqlPre.setString(2, excelModelBean.getiActName());
                        sqlPre.setString(3, excelModelBean.getiChildProjectName());
                        sqlPre.setString(4, excelModelBean.getiMainProName());
                        sqlPre.setString(5, excelModelBean.getiMainlineName());
                        sqlPre.setLong(6, excelModelBean.getiSelfOperationId());
                        sqlPre.addBatch();
                        logger.info("saveActPre:IEAI_ACTPRE:IPREACTNAME/" + excelModelBean.getiActName());
                        execute = true;
                    }
                }
                if (execute) {
                    sqlPre.executeBatch();
                }
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
                try {
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, sqlException, Thread.currentThread().getStackTrace()[1].getMethodName(), logger);
                } catch (RepositoryException e) {
                    e.printStackTrace();
                }
                return false;
            } finally {
                if (execute) {
                    DBResource.closePreparedStatement(sqlPre, "selectTaskCrossMainlineInfoByIds", logger);
                }
            }

            return true;
        });
    }

    public void saveActPreCopy(Connection conn, List<ExcelModelBean> pre) {
        String sql = "INSERT INTO IEAI_ACTPRE_COPY(IOPERATIONID, IPREACTNAME, ICHILDPROJECTNAME, IPROJECTNAME, IMAINLINENAME, ISELFOPERATIONID) VALUES (?,?,?,?,?,?)";
        safeUpdate("saveActPreCopy", conn, (sqlPre) -> {
            Boolean execute = false;
            try {
                sqlPre = conn.prepareStatement(sql);
                for (ExcelModelBean excelModelBean : pre) {
                    Boolean CC = TaskCrossMainlineManager.getInstance().booleanActPre(conn, excelModelBean,"copy");
                    if (!CC) {

                        sqlPre.setLong(1, excelModelBean.getiOperationId());
                        sqlPre.setString(2, excelModelBean.getiActName());
                        sqlPre.setString(3, excelModelBean.getiChildProjectName());
                        sqlPre.setString(4, excelModelBean.getiMainProName());
                        sqlPre.setString(5, excelModelBean.getiMainlineName());
                        sqlPre.setLong(6, excelModelBean.getiSelfOperationId());
                        sqlPre.addBatch();
                        logger.info("saveActPre:IEAI_ACTPRE_COPY:IPREACTNAME/" + excelModelBean.getiActName());
                        execute = true;
                    }
                }
                if (execute) {
                    sqlPre.executeBatch();
                }
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
                try {
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, sqlException, Thread.currentThread().getStackTrace()[1].getMethodName(), logger);
                } catch (RepositoryException e) {
                    e.printStackTrace();
                }
                return false;
            } finally {
                if (execute) {
                    DBResource.closePreparedStatement(sqlPre, "saveActPreCopy", logger);
                }
            }

            return true;
        });
    }

    public void saveActSucc(Connection conn, List<ExcelModelBean> succ) {
        String sql = "INSERT INTO IEAI_ACTSUCC(IOPERATIONID, ISUCCACTNAME, ICHILDPROJECTNAME, IPROJECTNAME, IMAINLINENAME, ISELFOPERATIONID) VALUES (?,?,?,?,?,?)";
        safeUpdate("saveActSucc", conn, (sqlPre) -> {
            Boolean execute = false;
            try {
                sqlPre = conn.prepareStatement(sql);
                for (ExcelModelBean excelModelBean : succ) {
                    Boolean CC = TaskCrossMainlineManager.getInstance().booleanActSucc(conn, excelModelBean,"");
                    if (!CC) {

                        sqlPre.setLong(1, excelModelBean.getiOperationId());
                        sqlPre.setString(2, excelModelBean.getiActName());
                        sqlPre.setString(3, excelModelBean.getiChildProjectName());
                        sqlPre.setString(4, excelModelBean.getiMainProName());
                        sqlPre.setString(5, excelModelBean.getiMainlineName());
                        sqlPre.setLong(6, excelModelBean.getiSelfOperationId());
                        sqlPre.addBatch();
                        logger.info("saveActSucc:IEAI_ACTSUCC:ISUCCACTNAME/" + excelModelBean.getiActName());
                        execute = true;
                    }

                }
                if (execute) {
                    sqlPre.executeBatch();
                }
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
                try {
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, sqlException, Thread.currentThread().getStackTrace()[1].getMethodName(), logger);
                } catch (RepositoryException e) {
                    e.printStackTrace();
                }
                return false;
            } finally {
                if (execute) {
                    DBResource.closePreparedStatement(sqlPre, "selectTaskCrossMainlineInfoByIds", logger);
                }

            }


            return true;
        });
    }

    public void saveActSuccCopy(Connection conn, List<ExcelModelBean> succ) {
        String sql = "INSERT INTO IEAI_ACTSUCC_COPY(IOPERATIONID, ISUCCACTNAME, ICHILDPROJECTNAME, IPROJECTNAME, IMAINLINENAME, ISELFOPERATIONID) VALUES (?,?,?,?,?,?)";
        safeUpdate("saveActSuccCopy", conn, (sqlPre) -> {
            Boolean execute = false;
            try {
                sqlPre = conn.prepareStatement(sql);
                for (ExcelModelBean excelModelBean : succ) {
                    Boolean CC = TaskCrossMainlineManager.getInstance().booleanActSucc(conn, excelModelBean,"copy");
                    if (!CC) {

                        sqlPre.setLong(1, excelModelBean.getiOperationId());
                        sqlPre.setString(2, excelModelBean.getiActName());
                        sqlPre.setString(3, excelModelBean.getiChildProjectName());
                        sqlPre.setString(4, excelModelBean.getiMainProName());
                        sqlPre.setString(5, excelModelBean.getiMainlineName());
                        sqlPre.setLong(6, excelModelBean.getiSelfOperationId());
                        sqlPre.addBatch();
                        logger.info("saveActSucc:IEAI_ACTSUCC_COPY:ISUCCACTNAME/" + excelModelBean.getiActName());
                        execute = true;
                    }

                }
                if (execute) {
                    sqlPre.executeBatch();
                }
            } catch (SQLException sqlException) {
                sqlException.printStackTrace();
                try {
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, sqlException, Thread.currentThread().getStackTrace()[1].getMethodName(), logger);
                } catch (RepositoryException e) {
                    e.printStackTrace();
                }
                return false;
            } finally {
                if (execute) {
                    DBResource.closePreparedStatement(sqlPre, "saveActSuccCopy", logger);
                }

            }


            return true;
        });
    }

    public void saveTaskCrossMainline(
            Map<String, ActInfoBean> mapList,int listrepFlow) throws SQLException {
        Connection conn = null;
        try {
            conn = DBResource.getConnection("saveTaskCrossMainline", logger, Constants.IEAI_IEAI_BASIC);

            List<TaskCrossMainlineBean> taskCrossMainlineBeanList = new ArrayList<>();
            taskCrossMainlineBeanList = TaskCrossMainlineManager.getInstance().getAllCrossMainlineList(conn);
            List<ExcelModelBean> actPrelistNew = new ArrayList<ExcelModelBean>();
            List<ExcelModelBean> actSucclistNew = new ArrayList<ExcelModelBean>();
            if(listrepFlow > 0){
                //运行中只操作临时表
                logger.info("工程运行中,将依赖关系保存到copy表");
                for (ActInfoBean aif : mapList.values()) {
                    for (TaskCrossMainlineBean taskCrossMainlineBean : taskCrossMainlineBeanList) {

                        if (aif.getActName().equals(taskCrossMainlineBean.getActName())
                                && aif.getMainProName().equals(taskCrossMainlineBean.getProjectName())
                                && aif.getMainline().equals(taskCrossMainlineBean.getMainLineName())
                        ) {
                            ExcelModelBean sussExcelModelBean = new ExcelModelBean();
                            ExcelModelBean preExcelModelBean = new ExcelModelBean();
                            sussExcelModelBean.setiMainlineName(taskCrossMainlineBean.getMainLineName());
                            sussExcelModelBean.setiMainProName(taskCrossMainlineBean.getProjectName());
                            sussExcelModelBean.setiActName(taskCrossMainlineBean.getActName());
                            TaskCrossMainlineManager.getInstance().getActExcelModelCopy(conn,
                                    sussExcelModelBean);

                            sussExcelModelBean.setiChildProjectName(aif.getChildProjectName());

                            preExcelModelBean.setiMainlineName(taskCrossMainlineBean.getDependMainLineName());
                            preExcelModelBean.setiActName(taskCrossMainlineBean.getDependActName());
                            preExcelModelBean.setiMainProName(taskCrossMainlineBean.getDependProjectName());
                            TaskCrossMainlineManager
                                    .getInstance().getActExcelModelCopy(conn, preExcelModelBean);

                            sussExcelModelBean.setiOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionIdCopy(conn, preExcelModelBean));
                            preExcelModelBean
                                    .setiOperationId(
                                            TaskCrossMainlineManager.getInstance().getIoperaTionIdCopy(conn, sussExcelModelBean));
                            if (sussExcelModelBean.getiOperationId() != null && sussExcelModelBean.getiOperationId() != 0
                                    && sussExcelModelBean.getiSelfOperationId() != null
                                    && sussExcelModelBean.getiSelfOperationId() != 0) {
                                actSucclistNew.add(sussExcelModelBean);
                            }

                            if (preExcelModelBean.getiOperationId() != null && preExcelModelBean.getiOperationId() != 0
                                    && preExcelModelBean.getiSelfOperationId() != null
                                    && preExcelModelBean.getiSelfOperationId() != 0) {
                                actPrelistNew.add(preExcelModelBean);
                            }
                        } else if (aif.getActName().equals(taskCrossMainlineBean.getDependActName())
                                && aif.getMainProName().equals(taskCrossMainlineBean.getDependProjectName())
                                && aif.getMainline().equals(taskCrossMainlineBean.getDependMainLineName())
                        ) {
                            ExcelModelBean sussExcelModelBean = new ExcelModelBean();
                            ExcelModelBean preExcelModelBean = new ExcelModelBean();
                            sussExcelModelBean.setiMainlineName(taskCrossMainlineBean.getMainLineName());
                            sussExcelModelBean.setiMainProName(taskCrossMainlineBean.getProjectName());
                            sussExcelModelBean.setiActName(taskCrossMainlineBean.getActName());
                            TaskCrossMainlineManager.getInstance().getActExcelModelCopy(conn,
                                    sussExcelModelBean);

                            preExcelModelBean.setiMainlineName(taskCrossMainlineBean.getDependMainLineName());
                            preExcelModelBean.setiChildProjectName(aif.getChildProjectName());
                            preExcelModelBean.setiActName(taskCrossMainlineBean.getDependActName());
                            preExcelModelBean.setiMainProName(taskCrossMainlineBean.getDependProjectName());
                            preExcelModelBean.setiSelfOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionIdCopy(conn, preExcelModelBean));
                            preExcelModelBean.setiOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionIdCopy(conn, sussExcelModelBean));

                            sussExcelModelBean.setiOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionIdCopy(conn, preExcelModelBean));

                            if (sussExcelModelBean.getiOperationId() != null && sussExcelModelBean.getiOperationId() != 0
                                    && sussExcelModelBean.getiSelfOperationId() != null
                                    && sussExcelModelBean.getiSelfOperationId() != 0) {
                                actSucclistNew.add(sussExcelModelBean);
                            }

                            if (preExcelModelBean.getiOperationId() != null && preExcelModelBean.getiOperationId() != 0
                                    && preExcelModelBean.getiSelfOperationId() != null
                                    && preExcelModelBean.getiSelfOperationId() != 0) {
                                actPrelistNew.add(preExcelModelBean);
                            }
                        }

                    }
                }
                if (actSucclistNew.size() > 0) {
                    TaskCrossMainlineManager.getInstance().saveActSuccCopy(conn, actSucclistNew);
                }
                if (actPrelistNew.size() > 0) {
                    TaskCrossMainlineManager.getInstance().saveActPreCopy(conn, actPrelistNew);
                }
            }else{
                for (ActInfoBean aif : mapList.values()) {
                    for (TaskCrossMainlineBean taskCrossMainlineBean : taskCrossMainlineBeanList) {

                        if (aif.getActName().equals(taskCrossMainlineBean.getActName())
                                && aif.getMainProName().equals(taskCrossMainlineBean.getProjectName())
                                && aif.getMainline().equals(taskCrossMainlineBean.getMainLineName())
                        ) {
                            ExcelModelBean sussExcelModelBean = new ExcelModelBean();
                            ExcelModelBean preExcelModelBean = new ExcelModelBean();
                            sussExcelModelBean.setiMainlineName(taskCrossMainlineBean.getMainLineName());
                            sussExcelModelBean.setiMainProName(taskCrossMainlineBean.getProjectName());
                            sussExcelModelBean.setiActName(taskCrossMainlineBean.getActName());
                            TaskCrossMainlineManager.getInstance().getActExcelModel(conn,
                                    sussExcelModelBean);

                            sussExcelModelBean.setiChildProjectName(aif.getChildProjectName());

                            preExcelModelBean.setiMainlineName(taskCrossMainlineBean.getDependMainLineName());
                            preExcelModelBean.setiActName(taskCrossMainlineBean.getDependActName());
                            preExcelModelBean.setiMainProName(taskCrossMainlineBean.getDependProjectName());
                            TaskCrossMainlineManager
                                    .getInstance().getActExcelModel(conn, preExcelModelBean);

                            sussExcelModelBean.setiOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionId(conn, preExcelModelBean));
                            preExcelModelBean
                                    .setiOperationId(
                                            TaskCrossMainlineManager.getInstance().getIoperaTionId(conn, sussExcelModelBean));
                            if (sussExcelModelBean.getiOperationId() != null && sussExcelModelBean.getiOperationId() != 0
                                    && sussExcelModelBean.getiSelfOperationId() != null
                                    && sussExcelModelBean.getiSelfOperationId() != 0) {
                                actSucclistNew.add(sussExcelModelBean);
                            }

                            if (preExcelModelBean.getiOperationId() != null && preExcelModelBean.getiOperationId() != 0
                                    && preExcelModelBean.getiSelfOperationId() != null
                                    && preExcelModelBean.getiSelfOperationId() != 0) {
                                actPrelistNew.add(preExcelModelBean);
                            }
                        } else if (aif.getActName().equals(taskCrossMainlineBean.getDependActName())
                                && aif.getMainProName().equals(taskCrossMainlineBean.getDependProjectName())
                                && aif.getMainline().equals(taskCrossMainlineBean.getDependMainLineName())
                        ) {
                            ExcelModelBean sussExcelModelBean = new ExcelModelBean();
                            ExcelModelBean preExcelModelBean = new ExcelModelBean();
                            sussExcelModelBean.setiMainlineName(taskCrossMainlineBean.getMainLineName());
                            sussExcelModelBean.setiMainProName(taskCrossMainlineBean.getProjectName());
                            sussExcelModelBean.setiActName(taskCrossMainlineBean.getActName());
                            TaskCrossMainlineManager.getInstance().getActExcelModel(conn,
                                    sussExcelModelBean);

                            preExcelModelBean.setiMainlineName(taskCrossMainlineBean.getDependMainLineName());
                            preExcelModelBean.setiChildProjectName(aif.getChildProjectName());
                            preExcelModelBean.setiActName(taskCrossMainlineBean.getDependActName());
                            preExcelModelBean.setiMainProName(taskCrossMainlineBean.getDependProjectName());
                            preExcelModelBean.setiSelfOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionId(conn, preExcelModelBean));
                            preExcelModelBean.setiOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionId(conn, sussExcelModelBean));

                            sussExcelModelBean.setiOperationId(
                                    TaskCrossMainlineManager.getInstance().getIoperaTionId(conn, preExcelModelBean));

                            if (sussExcelModelBean.getiOperationId() != null && sussExcelModelBean.getiOperationId() != 0
                                    && sussExcelModelBean.getiSelfOperationId() != null
                                    && sussExcelModelBean.getiSelfOperationId() != 0) {
                                actSucclistNew.add(sussExcelModelBean);
                            }

                            if (preExcelModelBean.getiOperationId() != null && preExcelModelBean.getiOperationId() != 0
                                    && preExcelModelBean.getiSelfOperationId() != null
                                    && preExcelModelBean.getiSelfOperationId() != 0) {
                                actPrelistNew.add(preExcelModelBean);
                            }
                        }

                    }

                }
                if (actSucclistNew.size() > 0) {
                    TaskCrossMainlineManager.getInstance().saveActSucc(conn, actSucclistNew);
                }
                if (actPrelistNew.size() > 0) {
                    TaskCrossMainlineManager.getInstance().saveActPre(conn, actPrelistNew);
                }
            }
            conn.commit();
        } catch (RepositoryException e) {
            // TODO Auto-generated catch block
            logger.error("saveTaskCrossMainline", e);
        } finally {
            DBResource.closeConnection(conn, "saveTaskCrossMainline", logger);
        }
    }


    public ExcelModelBean queryActSucc(Connection conn, ExcelModelBean excelModelBean) {
        String sql = "Select * from IEAI_ACTSUCC where IOPERATIONID=? and  ISUCCACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? "
                + "and IMAINLINENAME=? and ISELFOPERATIONID=? ";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        ExcelModelBean excelModelBean1 = new ExcelModelBean();
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setLong(1, excelModelBean.getiOperationId());
            sqlPre.setString(2, excelModelBean.getiActName());
            sqlPre.setString(3, excelModelBean.getiChildProjectName());
            sqlPre.setString(4, excelModelBean.getiMainProName());
            sqlPre.setString(5, excelModelBean.getiMainlineName());
            sqlPre.setLong(6, excelModelBean.getiSelfOperationId());
            sqlPre.execute();

            while (rs.next()) {

                excelModelBean1.setiOperationId(rs.getLong("IOPERATIONID"));
                excelModelBean1.setiActName(rs.getString("ISUCCACTNAME"));
                excelModelBean1.setiChildProjectName(rs.getString("ICHILDPROJECTNAME"));
                excelModelBean1.setiMainProName(rs.getString("IPROJECTNAME"));
                excelModelBean1.setiMainlineName(rs.getString("IMAINLINENAME"));
                excelModelBean1.setiSelfOperationId(rs.getLong("ISELFOPERATIONID"));

            }
        } catch (SQLException sqlException) {
            logger.error("queryActSucc", sqlException);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "queryActSucc", logger);
        }
        return excelModelBean1;
    }

    public ExcelModelBean queryActPre(Connection conn, ExcelModelBean excelModelBean) {
        String sql = "Select * from IEAI_ACTPRE where IOPERATIONID=? and  ISUCCACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? "
                + "and IMAINLINENAME=? and ISELFOPERATIONID=? ";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        ExcelModelBean excelModelBean1 = new ExcelModelBean();
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setLong(1, excelModelBean.getiOperationId());
            sqlPre.setString(2, excelModelBean.getiActName());
            sqlPre.setString(3, excelModelBean.getiChildProjectName());
            sqlPre.setString(4, excelModelBean.getiMainProName());
            sqlPre.setString(5, excelModelBean.getiMainlineName());
            sqlPre.setLong(6, excelModelBean.getiSelfOperationId());
            sqlPre.execute();
            while (rs.next()) {

                excelModelBean1.setiOperationId(rs.getLong("IOPERATIONID"));
                excelModelBean1.setiActName(rs.getString("ISUCCACTNAME"));
                excelModelBean1.setiChildProjectName(rs.getString("ICHILDPROJECTNAME"));
                excelModelBean1.setiMainProName(rs.getString("IPROJECTNAME"));
                excelModelBean1.setiMainlineName(rs.getString("IMAINLINENAME"));
                excelModelBean1.setiSelfOperationId(rs.getLong("ISELFOPERATIONID"));

            }
        } catch (SQLException sqlException) {
            logger.error("queryActPre", sqlException);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "queryActSucc", logger);
        }
        return excelModelBean1;
    }

    /**
     * 查询当前依赖是否存在
     *
     * @param conn 数据库连接
     * @return 有跨主线依赖的excelModel列表
     */
    public Boolean booleanActSucc(Connection conn, ExcelModelBean excelModelBean,String flag) {
        String tableName = "IEAI_ACTSUCC";
        if("copy".equals(flag)){
            tableName = "IEAI_ACTSUCC_COPY";
        }
        String sql = "Select count(*)  from "+tableName+" where IOPERATIONID=? and  ISUCCACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? "
                + "and IMAINLINENAME=? and ISELFOPERATIONID=? ";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        ExcelModelBean excelModelBean1 = new ExcelModelBean();
        boolean actSucc = false;
        logger.info("act:"+excelModelBean.toString());
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setLong(1, excelModelBean.getiOperationId());
            sqlPre.setString(2, excelModelBean.getiActName());
            sqlPre.setString(3, excelModelBean.getiChildProjectName());
            sqlPre.setString(4, excelModelBean.getiMainProName());
            sqlPre.setString(5, excelModelBean.getiMainlineName());
            sqlPre.setLong(6, excelModelBean.getiSelfOperationId());
            rs = sqlPre.executeQuery();

            while (rs.next()) {
                if (rs.getLong(1) > 0) {
                    actSucc = true;
                }

            }

        } catch (SQLException sqlException) {
            logger.error("booleanActSucc", sqlException);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "booleanActSucc", logger);
        }
        return actSucc;
    }

    public Boolean booleanActPre(Connection conn, ExcelModelBean excelModelBean,String flag) {
        String tableName = "IEAI_ACTPRE";
        if("copy".equals(flag)){
            tableName = "IEAI_ACTPRE_COPY";
        }
        String sql = "Select count(*)  from "+tableName+" where IOPERATIONID=? and  IPREACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? "
                + "and IMAINLINENAME=? and ISELFOPERATIONID=? ";
        PreparedStatement sqlPre = null;
        ResultSet rs = null;
        ExcelModelBean excelModelBean1 = new ExcelModelBean();
        boolean actPre = false;
        logger.info("pre:"+excelModelBean.toString());
        try {
            sqlPre = conn.prepareStatement(sql);
            sqlPre.setLong(1, excelModelBean.getiOperationId());
            sqlPre.setString(2, excelModelBean.getiActName());
            sqlPre.setString(3, excelModelBean.getiChildProjectName());
            sqlPre.setString(4, excelModelBean.getiMainProName());
            sqlPre.setString(5, excelModelBean.getiMainlineName());
            sqlPre.setLong(6, excelModelBean.getiSelfOperationId());

            rs = sqlPre.executeQuery();
            while (rs.next()) {

                if (rs.getLong(1) > 0) {
                    actPre = true;
                }

            }
        } catch (SQLException sqlException) {
            logger.error("booleanActPre", sqlException);
        } finally {
            DBResource.closePSRS(rs, sqlPre, "booleanActPre", logger);
        }
        return actPre;
    }


    /*针对没有写依赖活动名情况*/
    public List<TaskCrossMainlineBean>  assemData(Connection conn, List<TaskCrossMainlineBean> realSaveTaskCrossMainlineBeanList) {
        Iterator it = realSaveTaskCrossMainlineBeanList.iterator();
        List<TaskCrossMainlineBean> tempList = new ArrayList<>();
        while (it.hasNext()){
            TaskCrossMainlineBean taskCrossMainlineBean = (TaskCrossMainlineBean)it.next();
            if (taskCrossMainlineBean.getDependActName().equals("")) {
                String sql = "select iactname from ieai_excelmodel where IMAINPRONAME = ? and IMAINLINENAME = ? ";
                List<String> actNameList = new ArrayList<>();
                ResultSet rs = null;
                PreparedStatement pre = null;
                try {
                    pre = conn.prepareStatement(sql);
                    pre.setString(1, taskCrossMainlineBean.getDependProjectName());
                    pre.setString(2, taskCrossMainlineBean.getDependMainLineName());
                    rs = pre.executeQuery();
                    while (rs.next()) {
                        actNameList.add(rs.getString("iactname"));
                    }
                    for (int i = 0; i < actNameList.size(); i++) {
                        TaskCrossMainlineBean obj = new TaskCrossMainlineBean();
                        obj.setDependActName(actNameList.get(i));
                        obj.setDependProjectName(taskCrossMainlineBean.getDependProjectName());
                        obj.setDependMainLineName(taskCrossMainlineBean.getDependMainLineName());
                        obj.setMainLineName(taskCrossMainlineBean.getMainLineName());
                        obj.setActName(taskCrossMainlineBean.getActName());
                        obj.setProjectName(taskCrossMainlineBean.getProjectName());
                        obj.setIsDel("否");
                        ExcelModelBean preBean = new ExcelModelBean();
                        preBean.setiMainlineName(taskCrossMainlineBean.getDependMainLineName());
                        preBean.setiMainProName(taskCrossMainlineBean.getDependProjectName());
                        preBean.setiActName(actNameList.get(i));
                        obj.setPreExcelModelBean(preBean);
                        ExcelModelBean succBean = new ExcelModelBean();
                        succBean.setiMainlineName(taskCrossMainlineBean.getMainLineName());
                        succBean.setiMainProName(taskCrossMainlineBean.getProjectName());
                        succBean.setiActName(taskCrossMainlineBean.getActName());
                        obj.setSussExcelModelBean(succBean);
                        tempList.add(obj);
                    }
                    it.remove();
                } catch (Exception e) {
                    logger.error("assemData ~", e);
                    e.printStackTrace();
                }finally {
                    DBResource.closePSRS(rs, pre, "assemData", logger);
                }
            }
        }
        return  tempList;
    }


    /*针对没有写依赖活动名情况 找到最后节点*/
    public String  assemActName(Connection connection, TaskCrossMainlineBean taskCrossMainlineBean) {
        String actName = "";
        String sql = "select iactname from ieai_excelmodel where IMAINPRONAME = ? and IMAINLINENAME = ? ORDER BY IOPERATIONID desc";
        ResultSet rs = null;
        PreparedStatement pre = null;
        try {
            pre = connection.prepareStatement(sql);
            pre.setString(1, taskCrossMainlineBean.getDependProjectName());
            pre.setString(2, taskCrossMainlineBean.getDependMainLineName());
            rs = pre.executeQuery();
            while (rs.next()) {
                actName=rs.getString("iactname");
                break;
            }
        }catch (Exception e) {
            logger.error("assemData ~~", e);
            e.printStackTrace();
        }finally {
            DBResource.closePSRS(rs, pre, "assemData", logger);
        }
        return actName;
    }

    public List<Map> getCrossMainLineListByProjectName ( Connection csconn, String projectName )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map> actList = new ArrayList();
        String sqlSelf = "select IMAINPRONAME,IMAINLINENAME,IACTNAME,IDEPACTNAME,IDEPMAINPRONAME,IDEPMAINLINENAME from IEAI_CROSS_MAIN_LINE where IMAINPRONAME = ? or IDEPMAINPRONAME = ? ";
        try
        {
            ps = csconn.prepareStatement(sqlSelf);
            ps.setString(1, projectName);
            ps.setString(2, projectName);
            rs = ps.executeQuery();
            while(rs.next()){
                Map map = new HashMap();
                map.put("IMAINPRONAME", rs.getString("IMAINPRONAME"));
                map.put("IMAINLINENAME", rs.getString("IMAINLINENAME"));
                map.put("IACTNAME", rs.getString("IACTNAME"));
                map.put("IDEPMAINPRONAME", rs.getString("IDEPMAINPRONAME"));
                map.put("IDEPMAINLINENAME", rs.getString("IDEPMAINLINENAME"));
                map.put("IDEPACTNAME", rs.getString("IDEPACTNAME"));
                actList.add(map);
            }
        } catch (SQLException e)
        {
            logger.error("getCrossMainLineListByProjectName is error", e);
        } finally{
            DBResource.closePSRS(rs, ps, "getCrossMainLineListByProjectName", logger);
        }
        return actList;
    }

    public List<String> getRunningProject(List allProject) throws RepositoryException{
        List<String> runningProject = new ArrayList();

        String sql = "select count(t.iflowid) as cou from ieai_workflowinstance t where  t.istatus in (0,6,8,15,30,40,46,47)  and t.iprojectname=? ";

        for (int i = 0;i < allProject.size(); i++)
        {
            try
            {
                Connection con = DBResource.getConnection("getFlowInstanceStateByprojectName", logger ,Constants.IEAI_IEAI_BASIC );
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, String.valueOf(allProject.get(i)));
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        if(actRS.getLong("cou") != 0){
                            runningProject.add(String.valueOf(allProject.get(i)));
                        }
                    }

                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    logger.error(method + " is error at getRunningProject ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getRunningProject", logger);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return runningProject;

    }
}

