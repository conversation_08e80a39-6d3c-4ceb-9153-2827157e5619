package com.ideal.ieai.server.jobscheduling.repository.taskupload;

import com.ideal.ieai.commons.Conscommon;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.jobscheduling.repository.actmutexconfig.ActMutexConfigManager;
import com.ideal.ieai.server.jobscheduling.repository.createexcel.StartCreatePrj;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.*;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.engine.RepWorkflowInstance;
import com.ideal.ieai.server.repository.excelmodel.ActFinishedFlagNew;
import com.ideal.ieai.server.repository.excelmodel.ExcelmodelBean;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.project.ProjectSaveUtilBean;
import com.ideal.util.DateUtil;
import net.sf.hibernate.Hibernate;
import oracle.sql.BLOB;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.util.FileCopyUtils;

import java.io.*;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.*;

/**
 * <ul>
 * <li>Title: EngineRepositotyJdbc.java</li>
 * <li>Description:对excel表及依赖触发表中的信息进行维护</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 *
 * <AUTHOR>
 *
 *         2014-4-2
 */
public class UpLoadExcelManager
{

    private static final String METHODSTR = "method :";
    private static final String DESCFLOWNAME = " flowName:";
    private static final String DESCPROJECTNAME = "projectName:";
    private static final String ICHILDPROJECTNAME ="ICHILDPROJECTNAME";
    private static final String IOPERATIONID ="IOPERATIONID";
    private static final String CCOUNT ="count";
    private static final String CIDATADATE ="idatadate";
    private static final String CPROID ="proid";
    private static final String CIOLDOPERATIONID ="ioldoperationid";
    private static final String CADDBEAN ="addBean";
    private static final String CDELETEBEAN ="deleteBean";
    private static final String IMAINLINENAME ="IMAINLINENAME";
    private static final String IMAINPRONAME ="IMAINPRONAME";
    private static final String ISHELLHOUSE ="ISHELLHOUSE";
    private static final String IOUTPUTPARAM ="IOUTPUTPARAM";
    private static final String ILASTLINE ="ILASTLINE";
    private static final String IACTDESCRIPTION ="IACTDESCRIPTION";
    private static final String ISYSTEM ="ISYSTEM";
    private static final String IACTNAME ="IACTNAME";
    private static final String ICHANGEOPR ="ICHANGEOPR";
    private static final String ICHILDPRONAME ="ICHILDPRONAME";
    private static final String ISHELLABSOLUTEPATH ="ISHELLABSOLUTEPATH";
    private static final String IPROJECTNAME ="IPROJECTNAME";
    private static final String IINPUTPARAM = "IINPUTPARAM";
    private static final String IHEADTAILFLAG ="IHEADTAILFLAG";
    private static final String IOKFILEABSOLUTEPATH ="IOKFILEABSOLUTEPATH";
    private static final String IAGENTSOURCEGROUP ="IAGENTSOURCEGROUP";
    private static final String SQLTOCHARTDATEYYMMDD =" ?,?,(to_char(to_date(?, 'YYYYMMDD') - ?, 'YYYYMMDD')),";
    private static final String IEAIACTFINISHEDFLAGNEW ="IEAI_ACTFINISHED_FLAG_NEW";
    private static final String SQLSTIEAIEXCELMODELWHEMAI =" ?, ?, ?, SYSDATE, (select t.ioperationid from ieai_excelmodel t where t.imainproname=? and t.isystem=? and t.imainlinename=? and t.iactname=?))";
    private static final String SQLINSERTIEAIEXCELMODELVAL ="IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP , APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM ,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,DAYS,LOGIC,WDAYS) values(?,?,?,?,?,?,?,?,?,";
    private static final String SQLINSERTIEAIEXCELMODELVALCOPYGY ="IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP , APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM ,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,DAYS,LOGIC,WDAYS,IACTNO) values(?,?,?,?,?,?,?,?,?,?,";

    private static final String SQLINSERTIEAIEXCELMODELVALCOPYDG ="IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP , APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM ,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,DAYS,LOGIC,WDAYS,IACTPARAMS) values(?,?,?,?,?,?,?,?,?,?,";

    private static final String SQLINSERTIEAIEXCELMODELVALBH ="IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP , APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM ,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,DAYS,LOGIC,WDAYS,PERFORMUSER,VIRTUALNAME,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,IMONTH,IJOBTYPE) values(?,?,?,?,?,?,?,?,?,";

    private static final String SQLINSERTIEAIEXCELMODELVALGY ="IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP , APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM ,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,DAYS,LOGIC,WDAYS,PERFORMUSER,VIRTUALNAME,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,IMONTH,IACTNO) values(?,?,?,?,?,?,?,?,?,?,";

    private static final String SQLINSERTIEAIEXCELMODELVALDG ="IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP , APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM ,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,DAYS,LOGIC,WDAYS,PERFORMUSER,VIRTUALNAME,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,IMONTH,IACTPARAMS) values(?,?,?,?,?,?,?,?,?,?,";


    private static final String SQLSELSYSDATERTIEAIEXCELMODELWH =" ?, ?, ?, sysdate(), (select t.ioperationid from ieai_excelmodel t where t.imainproname=? and t.isystem=? and t.imainlinename=? and t.iactname=?))";

    public static  UpLoadExcelManager getInstance ()
    {
        return _instGroup;
    }

    private UpLoadExcelManager()
    {
    }

    private static  final UpLoadExcelManager     _instGroup             = new UpLoadExcelManager();
    private static  final UpLoadTempExcleManager upLoadTempExcleManager = new UpLoadTempExcleManager();

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws ServerException
     * @throws NumberFormatException
     * @throws RepositoryException
     */
    public void importExcelModelManager ( Map<String, ActInfoBean> mapList, String fileName, String del,
                                          String userName, String type, Connection conn, int basicType,Map<String,Long> allActMap )
            throws ServerException, SQLException, RepositoryException
    {

        if (del.equals("del"))
        {

            for (ActInfoBean aif : mapList.values())
            {
                ActInfoBean actInfor = selectOneExcelModelManager(aif.getProjectName(), aif.getMainline(),
                        aif.getChildProjectName(), aif.getActName(), aif.getSystem(), type, conn);
                // 删除操作
                if (aif.getDeleteFlag().equals("2") && null != actInfor)
                {
                    List<String> listBefore = aif.getBeforeActList();
                    List<String> listAfter = aif.getAfterActList();
                    // 依赖和触发都存在的情况下，删除依赖和触发
                    if (!listBefore.isEmpty() && !listAfter.isEmpty())
                    {

                        for (int i1 = 0; i1 < listBefore.size(); i1++)
                        {
                            String before = listBefore.get(i1);
                            ActInfoBean actInfoBean = mapList.get(before);
                            if (actInfoBean != null)
                            {
                                ActPreBean actPreBean = selectOneActpreManager(Long.valueOf(actInfor.getActID()),
                                        actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                        actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                if (actPreBean != null)
                                {
                                    deleteExcelActPreModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActSuccModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }

                                }
                            }
                            updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        }
                        for (int i1 = 0; i1 < listAfter.size(); i1++)
                        {
                            String after = listAfter.get(i1);
                            ActInfoBean actInfoBean = mapList.get(after);
                            if (actInfoBean != null)
                            {
                                ActSuccBean actSuccBean = selectOneActsuccManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actSuccBean != null)
                                {
                                    deleteExcelActSuccModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActPreModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        // 依赖存在的情况下，删除依赖关系
                    } else if (!listBefore.isEmpty() && listAfter.isEmpty())
                    {

                        for (int i1 = 0; i1 < listBefore.size(); i1++)
                        {
                            String before = listBefore.get(i1);
                            ActInfoBean actInfoBean = mapList.get(before);
                            if (actInfoBean != null)
                            {
                                ActPreBean actPreBean = selectOneActpreManager(Long.valueOf(actInfor.getActID()),
                                        actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                        actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                if (actPreBean != null)
                                {
                                    deleteExcelActPreModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActSuccModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        // 触发存在的情况下，删除触发关系
                    } else if (!listAfter.isEmpty() && listBefore.isEmpty())
                    {
                        for (int i1 = 0; i1 < listAfter.size(); i1++)
                        {
                            String after = listAfter.get(i1);
                            ActInfoBean actInfoBean = mapList.get(after);
                            if (actInfoBean != null)
                            {
                                ActSuccBean actSuccBean = selectOneActsuccManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actSuccBean != null)
                                {
                                    deleteExcelActSuccModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActPreModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        // 依赖和触发都不在的情况下，删除数据及依赖触发关系
                    } else
                    {
                        deletePreSuccExcelModel(actInfor.getActName(), actInfor.getProjectName(),
                                actInfor.getSystem(), type, conn);
                        updateExcelModelFlag(actInfor.getProjectName(), actInfor.getMainline(),
                                actInfor.getChildProjectName(), actInfor.getActName(), Long.valueOf(2),
                                actInfor.getActID(), actInfor.getSystem(), userName, type, conn);
                    }

                }
            }
        } else
        {
            int countlist = upLoadTempExcleManager.selectCountProNameTempExcelModelMain(fileName, conn);
            List<RepWorkflowInstance> addbeanList = new ArrayList<RepWorkflowInstance>();
            List<RepWorkflowInstance> deletebeanList = new ArrayList<RepWorkflowInstance>();
            if (countlist <= 0)
            {
                saveExcelModel(mapList, fileName, conn, basicType,allActMap);

            } else
            {
                for (ActInfoBean aif : mapList.values())
                {
                    ActInfoBean actInfor = selectOneExcelModelManager(aif.getProjectName(), aif.getMainline(),
                            aif.getChildProjectName(), aif.getActName(), aif.getSystem(), type, conn);
                    if (actInfor == null)
                    {
                        Map returnMap = saveUpdateExcelModel(aif, mapList, type, conn, basicType);

                        if (returnMap.get(CADDBEAN) != null)
                        {
                            addbeanList.add((RepWorkflowInstance) returnMap.get(CADDBEAN));
                        }
                        if (returnMap.get(CDELETEBEAN) != null)
                        {
                            deletebeanList.add((RepWorkflowInstance) returnMap.get(CDELETEBEAN));
                        }
                    } else
                    {
                        aif.setFlag("1");
                        Map returnMap = updateExcelModel(aif, mapList, actInfor.getActID(), type, conn);
                        if (returnMap.get(CADDBEAN) != null)
                        {
                            addbeanList.add((RepWorkflowInstance) returnMap.get(CADDBEAN));
                        }
                        if (returnMap.get(CDELETEBEAN) != null)
                        {
                            deletebeanList.add((RepWorkflowInstance) returnMap.get(CDELETEBEAN));
                        }
                    }
                }

                try
                {
                    if (!addbeanList.isEmpty())
                    {
                        this.saveDisableRuleForBatch(addbeanList, conn, basicType);
                    }
                    if (!deletebeanList.isEmpty())
                    {
                        this.deleteDisableRuleForBatch(deletebeanList, conn);
                    }
                } catch (RepositoryException e)
                {
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                }
            }
        }
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws ServerException
     * @throws NumberFormatException
     * @throws RepositoryException
     */
    public void importExcelModelManager ( Map<String, ActInfoBean> mapList, String fileName, String flowName,
                                          String del, String userName, String type, Connection conn, int basicType )
            throws ServerException, SQLException, RepositoryException
    {

        if (del.equals("del"))
        {

            for (ActInfoBean aif : mapList.values())
            {
                ActInfoBean actInfor = selectOneExcelModelManager(aif.getProjectName(), aif.getMainline(),
                        aif.getChildProjectName(), aif.getActName(), aif.getSystem(), type, conn);
                // 删除操作
                if (aif.getDeleteFlag().equals("2") && null != actInfor)
                {
                    List<String> listBefore = aif.getBeforeActList();
                    List<String> listAfter = aif.getAfterActList();
                    // 依赖和触发都存在的情况下，删除依赖和触发
                    if (!listBefore.isEmpty() && !listAfter.isEmpty())
                    {

                        for (int i1 = 0; i1 < listBefore.size(); i1++)
                        {
                            String before = listBefore.get(i1);
                            ActInfoBean actInfoBean = mapList.get(before);
                            if (actInfoBean != null)
                            {
                                ActPreBean actPreBean = selectOneActpreManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actPreBean != null)
                                {
                                    deleteExcelActPreModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActSuccModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }

                                }
                            }
                            updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        }
                        for (int i1 = 0; i1 < listAfter.size(); i1++)
                        {
                            String after = listAfter.get(i1);
                            ActInfoBean actInfoBean = mapList.get(after);
                            if (actInfoBean != null)
                            {
                                ActSuccBean actSuccBean = selectOneActsuccManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actSuccBean != null)
                                {
                                    deleteExcelActSuccModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActPreModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        // 依赖存在的情况下，删除依赖关系
                    } else if (!listBefore.isEmpty() && listAfter.isEmpty())
                    {

                        for (int i1 = 0; i1 < listBefore.size(); i1++)
                        {
                            String before = listBefore.get(i1);
                            ActInfoBean actInfoBean = mapList.get(before);
                            if (actInfoBean != null)
                            {
                                ActPreBean actPreBean = selectOneActpreManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actPreBean != null)
                                {
                                    deleteExcelActPreModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActSuccModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        // 触发存在的情况下，删除触发关系
                    } else if (!listAfter.isEmpty() && listBefore.isEmpty())
                    {
                        for (int i1 = 0; i1 < listAfter.size(); i1++)
                        {
                            String after = listAfter.get(i1);
                            ActInfoBean actInfoBean = mapList.get(after);
                            if (actInfoBean != null)
                            {
                                ActSuccBean actSuccBean = selectOneActsuccManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actSuccBean != null)
                                {
                                    deleteExcelActSuccModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActPreModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        // 依赖和触发都不在的情况下，删除数据及依赖触发关系
                    } else
                    {
                        deletePreSuccExcelModel(actInfor.getActName(), actInfor.getProjectName(),
                                actInfor.getSystem(), type, conn);
                        updateExcelModelFlag(actInfor.getProjectName(), actInfor.getMainline(),
                                actInfor.getChildProjectName(), actInfor.getActName(), Long.valueOf(2),
                                actInfor.getActID(), actInfor.getSystem(), userName, type, conn);
                    }

                }
            }
        } else
        {
            List<RepWorkflowInstance> addbeanList = new ArrayList<RepWorkflowInstance>();
            List<RepWorkflowInstance> deletebeanList = new ArrayList<RepWorkflowInstance>();
            for (ActInfoBean aif : mapList.values())
            {
                ActInfoBean actInfor = selectOneExcelModelManager(aif.getProjectName(), aif.getMainline(),
                        aif.getChildProjectName(), aif.getActName(), aif.getSystem(), type, conn);
                if (actInfor == null)
                {
                    if (flowName.equals(aif.getMainline()))
                    {
                        Map returnMap = saveUpdateExcelModel(aif, mapList, type, conn, basicType);

                        if (returnMap.get(CADDBEAN) != null)
                        {
                            addbeanList.add((RepWorkflowInstance) returnMap.get(CADDBEAN));
                        }
                        if (returnMap.get(CDELETEBEAN) != null)
                        {
                            deletebeanList.add((RepWorkflowInstance) returnMap.get(CDELETEBEAN));
                        }
                    }

                } else
                {
                    if (flowName.equals(aif.getMainline()))
                    {
                        aif.setFlag("1");
                        Map returnMap = updateExcelModel(aif, mapList, actInfor.getActID(), type, conn);
                        if (returnMap.get(CADDBEAN) != null)
                        {
                            addbeanList.add((RepWorkflowInstance) returnMap.get(CADDBEAN));
                        }
                        if (returnMap.get(CDELETEBEAN) != null)
                        {
                            deletebeanList.add((RepWorkflowInstance) returnMap.get(CDELETEBEAN));
                        }
                    }
                }
            }

            try
            {
                if (!addbeanList.isEmpty())
                {
                    this.saveDisableRuleForBatch(addbeanList, conn, basicType);
                }
                if (!deletebeanList.isEmpty())
                {
                    this.deleteDisableRuleForBatch(deletebeanList, conn);
                }
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
            }
        }
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2017-4-17
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws ServerException
     * @throws NumberFormatException
     * @throws RepositoryException
     */
    public void importExcelModelCopyManager ( Map<String, ActInfoBean> mapList, ProjectSaveUtilBean projectSaveUtilBean,
                                              String del, String userName, String type, Long iid, Connection conn,
                                              int basicType ,Map allActMap) throws ServerException, SQLException, RepositoryException
    {

        if (del.equals("del"))
        {

            for (ActInfoBean aif : mapList.values())
            {
                ActInfoBean actInfor = selectOneExcelModelManager(aif.getProjectName(), aif.getMainline(),
                        aif.getChildProjectName(), aif.getActName(), aif.getSystem(), type, conn);
                // 删除操作
                if (aif.getDeleteFlag().equals("2") && null != actInfor)
                {
                    List<String> listBefore = aif.getBeforeActList();
                    List<String> listAfter = aif.getAfterActList();
                    // 依赖和触发都存在的情况下，删除依赖和触发
                    if (!listBefore.isEmpty() && !listAfter.isEmpty())
                    {

                        for (int i1 = 0; i1 < listBefore.size(); i1++)
                        {
                            String before = listBefore.get(i1);
                            ActInfoBean actInfoBean = mapList.get(before);
                            if (actInfoBean != null)
                            {
                                ActPreBean actPreBean = selectOneActpreManager(Long.valueOf(actInfor.getActID()),
                                        actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                        actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                if (actPreBean != null)
                                {
                                    deleteExcelActPreModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActSuccModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }

                                }
                            }
                            updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        }
                        for (int i1 = 0; i1 < listAfter.size(); i1++)
                        {
                            String after = listAfter.get(i1);
                            ActInfoBean actInfoBean = mapList.get(after);
                            if (actInfoBean != null)
                            {
                                ActSuccBean actSuccBean = selectOneActsuccManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actSuccBean != null)
                                {
                                    deleteExcelActSuccModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActPreModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        // 依赖存在的情况下，删除依赖关系
                    } else if (!listBefore.isEmpty() && listAfter.isEmpty())
                    {

                        for (int i1 = 0; i1 < listBefore.size(); i1++)
                        {
                            String before = listBefore.get(i1);
                            ActInfoBean actInfoBean = mapList.get(before);
                            if (actInfoBean != null)
                            {
                                ActPreBean actPreBean = selectOneActpreManager(Long.valueOf(actInfor.getActID()),
                                        actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                        actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                if (actPreBean != null)
                                {
                                    deleteExcelActPreModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActSuccModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        // 触发存在的情况下，删除触发关系
                    } else if (!listAfter.isEmpty() && listBefore.isEmpty())
                    {
                        for (int i1 = 0; i1 < listAfter.size(); i1++)
                        {
                            String after = listAfter.get(i1);
                            ActInfoBean actInfoBean = mapList.get(after);
                            if (actInfoBean != null)
                            {
                                ActSuccBean actSuccBean = selectOneActsuccManager(
                                        Long.valueOf(actInfor.getActID()), actInfoBean.getProjectName(),
                                        actInfoBean.getMainline(), actInfoBean.getChildProjectName(),
                                        actInfoBean.getActName(), type, conn);
                                if (actSuccBean != null)
                                {
                                    deleteExcelActSuccModel(Long.valueOf(actInfor.getActID()),
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(), type, conn);
                                    ActInfoBean actInfor1 = selectOneExcelModelManager(
                                            actInfoBean.getProjectName(), actInfoBean.getMainline(),
                                            actInfoBean.getChildProjectName(), actInfoBean.getActName(),
                                            actInfoBean.getSystem(), type, conn);
                                    if (actInfor1 != null)
                                    {
                                        deleteExcelActPreModel(Long.valueOf(actInfor1.getActID()),
                                                actInfor.getProjectName(), actInfor.getMainline(),
                                                actInfor.getChildProjectName(), actInfor.getActName(), type, conn);
                                    }
                                }
                            }
                        }
                        updatePreSuccExcelModelFlag(Long.valueOf(actInfor.getActID()), type, conn);
                        // 依赖和触发都不在的情况下，删除数据及依赖触发关系
                    } else
                    {
                        deletePreSuccExcelModel(actInfor.getActName(), actInfor.getProjectName(),
                                actInfor.getSystem(), type, conn);
                        updateExcelModelFlag(actInfor.getProjectName(), actInfor.getMainline(),
                                actInfor.getChildProjectName(), actInfor.getActName(), Long.valueOf(2),
                                actInfor.getActID(), actInfor.getSystem(), userName, type, conn);
                    }

                }
            }
        } else
        {
            for (ActInfoBean aif : mapList.values())
            {
                ActInfoBean actInfor = selectOneExcelModelManager(aif.getProjectName(), aif.getMainline(),
                        aif.getChildProjectName(), aif.getActName(), aif.getSystem(), type, conn);
                if (actInfor == null)
                {
                    saveUpdateExcelModel(aif, projectSaveUtilBean, mapList, type, iid, conn,
                            basicType,allActMap);
                } else
                {
                    aif.setFlag("1");
                    updateExcelModel(aif, projectSaveUtilBean, mapList, actInfor.getActID(), type, iid,
                            conn, basicType);
                }
            }
        }
    }

    /**
     * <li>Description:保存ieai_topoinfo信息</li>
     * <AUTHOR>
     * 2022年9月14日
     * @param mapList
     * @param conn
     * @param username
     * @throws Exception
     * return void
     */
    public void importExcelTopoinfo (Map<String, ActInfoBean> mapList, Connection conn, String username) throws Exception
    {
        //lastsysname,lasttaskname
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String queryDate = df.format(new Date());
        String sql = "delete from ieai_topoinfo where gro = ? and systemname=?";
        String sql2 = "insert into ieai_topoinfo(iid,systemname,taskname,idate,"
                + "information,iopendatetype,iarrivedatetype,gro) values (?,?,?,?,?,?,?,?)";
        PreparedStatement ps = null;
        PreparedStatement ps2 = null;
        try {
            String gro = Environment.getInstance().getSysConfig(Environment.BH_CROSS_MAIN_TOPO,"");
            String mainline = "";
            for (ActInfoBean aif : mapList.values())
            {
                mainline = aif.getChildProjectName();
                break;
            }
            ps = conn.prepareStatement(sql);
            ps.setString(1, gro);
            ps.setString(2, mainline);
            ps.executeUpdate();
            Long mainID = IdGenerator.createIdForExecActBig("ieai_topoinfo",mapList.size(),conn);
            long startId = mainID - mapList.size() + 1;
            ps2 = conn.prepareStatement(sql2);
            for (ActInfoBean aif : mapList.values())
            {
                ps2.setLong(1, startId++);
                ps2.setString(2, aif.getChildProjectName());
                ps2.setString(3, aif.getActName());
                ps2.setString(4, queryDate);
                ps2.setString(5, username);
                ps2.setString(6, "当日");
                ps2.setString(7, "当日");
                ps2.setString(8, gro);
                ps2.addBatch();
            }
            ps2.executeBatch();
            conn.commit();
        } catch (Exception e)
        {
            log.error(method,e);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }

    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws RepositoryException
     */
    public Long saveExcelModel ( Map<String, ActInfoBean> mapList, String projectName, Connection conn, int basicType ,Map<String,Long> allActMap)
            throws  SQLException, RepositoryException
    {
        int index=0;
        Long proid = null;
        List<RepWorkflowInstance> addbeanList = new ArrayList<RepWorkflowInstance>();
        List<RepWorkflowInstance> deletebeanList = new ArrayList<RepWorkflowInstance>();
        String sql ="";
        String sqlAddExcelmodel ="";
        // (SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL)

        if(Environment.getInstance().getGYBankSwitch()){
            sql = "INSERT INTO IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + SQLINSERTIEAIEXCELMODELVALGY
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            sqlAddExcelmodel = "INSERT INTO IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + SQLINSERTIEAIEXCELMODELVALGY
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        }else if(Environment.getInstance().getDGBankSwitch()){
            sql = "INSERT INTO IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + SQLINSERTIEAIEXCELMODELVALDG
                    + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            sqlAddExcelmodel = "INSERT INTO IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + SQLINSERTIEAIEXCELMODELVALDG
                    + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        }else{
            sql = "INSERT INTO IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + SQLINSERTIEAIEXCELMODELVALBH
                    + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            sqlAddExcelmodel = "INSERT INTO IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + SQLINSERTIEAIEXCELMODELVALBH
                    + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        }

        String sqlAddActpre = "INSERT INTO IEAI_ACTPRE (IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

        String sqlAddActSucc = "INSERT INTO IEAI_ACTSUCC (IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

        String sqlAddDelope = "INSERT INTO IEAI_DELOPTIONID (IOLDOPERATIONID, IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, ISYSTEM, IPERATIONUSER, IENDTIME) VALUES (?,?,?,?,?,?,?,SYSDATE)";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlAddDelope = "INSERT INTO IEAI_DELOPTIONID (IOLDOPERATIONID, IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, ISYSTEM, IPERATIONUSER, IENDTIME) VALUES (?,?,?,?,?,?,?,sysdate())";
        }
        String sql1 = "INSERT INTO IEAI_ACTPRE_COPY(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

        String sql2 = "INSERT INTO IEAI_ACTSUCC_COPY(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

        String sql3 = "SELECT count(T1.ioldoperationid) as cou FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? ";
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        PreparedStatement ps7 = null;
        PreparedStatement ps8 = null;
        ResultSet rs = null;
        int count = 0;
        try
        {
            ps = conn.prepareStatement(sql);
            ps1 = conn.prepareStatement(sql1);
            ps2 = conn.prepareStatement(sql2);
            ps3 = conn.prepareStatement(sql3);
            ps3.setString(1, projectName);
            ps4 = conn.prepareStatement(sqlAddExcelmodel);
            ps5 = conn.prepareStatement(sqlAddActpre);
            ps6 = conn.prepareStatement(sqlAddActSucc);
            ps7 = conn.prepareStatement(sqlAddDelope);
            rs = ps3.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("cou");
            }
            for (ActInfoBean aif : mapList.values())
            {
                long id = 0;
                if(!allActMap.isEmpty()){
                    id = allActMap.get(aif.getMainProName().trim()+aif.getMainline().trim()+aif.getActName().trim())==null?0:allActMap.get(aif.getMainProName().trim()+aif.getMainline().trim()+aif.getActName().trim());
                    if(id==0){
                        id = generCountExcelModel( basicType);
                    }
                }else{
                    id = generCountExcelModel( basicType);
                }
                ps.setLong(1, id);
                ps.setString(2, aif.getProjectName());
                ps.setString(3, aif.getMainline());
                if ("总头".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 1);
                } else if ("主线头".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 2);
                } else if ("总尾".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 6);
                } else if ("主线尾".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 7);
                } else
                {
                    ps.setLong(4, 0);
                }
                ps.setString(5, aif.isAgentGroup() ? aif.getAgentGropName() : aif.getAgentInfo());
                ps.setString(6, aif.getOKFileABPath());
                ps.setString(7, "数据日期");
                ps.setString(8, aif.getChildProjectName());
                ps.setLong(9, Long.valueOf(aif.getDeleteFlag()));
                ps.setString(10, aif.getActName());
                ps.setString(11, aif.getDescribe());
                ps.setString(12, aif.getOutputPamaeter());
                ps.setString(13, aif.getShellCrust());
                ps.setString(14, aif.getShellABPath());
                if (null == aif.getOKFileABPath() || "".equals(aif.getOKFileABPath()))
                {
                    ps.setString(15, "0");
                } else
                {
                    ps.setString(15, "0,nf");
                }
                if (null == aif.getWeights() || "".equals(aif.getWeights()))
                {
                    ps.setLong(16, 1);
                } else
                {
                    ps.setLong(16, Long.valueOf(aif.getWeights()));
                }
                if (null == aif.getPriority() || "".equals(aif.getPriority()))
                {
                    ps.setLong(17, 1);
                } else
                {
                    ps.setLong(17, Long.valueOf(aif.getPriority()));
                }
                if (null == aif.getOKFileFindWeek() || "".equals(aif.getOKFileFindWeek()))
                {
                    ps.setLong(18, 0);
                } else
                {
                    ps.setLong(18, Long.valueOf(aif.getOKFileFindWeek()));
                }
                ps.setLong(19, Long.valueOf("1"));
                ps.setString(20, aif.getCheckAgentGropName());
                // apt
                ps.setString(21, aif.getAptGroupName());
                ps.setString(22, aif.getAptFileName());
                ps.setString(23, aif.getIsDB2());
                ps.setString(24, aif.getDb2IP());
                ps.setString(25, aif.getAptResGroupname());
                // apt
                ps.setString(26, aif.getSystem());

                if ("1".equals(aif.getRedo().trim()))
                {
                    ps.setString(27, "1");
                } else
                {
                    ps.setString(27, "0");
                }
                ps.setInt(28, aif.isAgentGroup() ? 0 : 1);
                ps.setString(29, aif.getDelayTime());
                ps.setString(30, aif.getBranchCondition());
                if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount())
                        && !"null".equals(aif.getReTryCount()))
                {
                    ps.setInt(31, Integer.parseInt(aif.getReTryCount()));// ,IRETRYNUM,ICALENDNAME

                } else
                {
                    ps.setInt(31, 0);
                }
                ps.setString(32, aif.getCalendName());
                if (null != aif.getReTryTime() && !"".equals(aif.getReTryTime()) && !"null".equals(aif.getReTryTime()))
                {
                    ps.setInt(33, Integer.parseInt(aif.getReTryTime()));//

                } else
                {
                    ps.setInt(33, 0);
                }
                ps.setString(34, aif.getReTryEndTime());
                ps.setString(35, aif.getSkip());
                ps.setString(36, aif.getDelayWarnning());
                ps.setString(37, aif.getDays());
                ps.setString(38, aif.getLogic());
                ps.setString(39, aif.getWdays());
                ps.setString(40, aif.getPerformUser() == null ? "" : aif.getPerformUser());
                ps.setString(41, aif.getVirtualName() == null ? "" : aif.getVirtualName());
                ps.setString(42, aif.getJoblist() == null ? "" : aif.getJoblist());
                ps.setString(43, aif.getActNo() == null ? "" : aif.getActNo());
                ps.setInt(44, "是".equals(aif.getEndTag()) ? 1: 0);
                /*  ps.setString(45, aif.getOkFileName() == null ? "" : aif.getOkFileName());*/
                ps.setInt(45, aif.getUserTask());
                ps.setString(46, aif.getMonth());
                if(Environment.getInstance().getGYBankSwitch()){
                    ps.setInt(47,Integer.parseInt(aif.getActNo()));
                }else if(Environment.getInstance().getDGBankSwitch()){
                    ps.setString(47,aif.getActParams());
                }else{
                    ps.setString(47,aif.getJobType());
                }
                ps.addBatch();

                ps4.setLong(1, id);
                ps4.setString(2, aif.getProjectName());
                ps4.setString(3, aif.getMainline());
                if ("总头".equals(aif.getSEFlag()))
                {
                    ps4.setLong(4, 1);
                } else if ("主线头".equals(aif.getSEFlag()))
                {
                    ps4.setLong(4, 2);
                } else if ("总尾".equals(aif.getSEFlag()))
                {
                    ps4.setLong(4, 6);
                } else if ("主线尾".equals(aif.getSEFlag()))
                {
                    ps4.setLong(4, 7);
                } else
                {
                    ps4.setLong(4, 0);
                }
                ps4.setString(5, aif.isAgentGroup() ? aif.getAgentGropName() : aif.getAgentInfo());
                ps4.setString(6, aif.getOKFileABPath());
                ps4.setString(7, "数据日期");
                ps4.setString(8, aif.getChildProjectName());
                ps4.setLong(9, Long.valueOf(aif.getDeleteFlag()));
                ps4.setString(10, aif.getActName());
                ps4.setString(11, aif.getDescribe());
                ps4.setString(12, aif.getOutputPamaeter());
                ps4.setString(13, aif.getShellCrust());
                ps4.setString(14, aif.getShellABPath());
                if (null == aif.getOKFileABPath() || "".equals(aif.getOKFileABPath()))
                {
                    ps4.setString(15, "0");
                } else
                {
                    ps4.setString(15, "0,nf");
                }
                if (null == aif.getWeights() || "".equals(aif.getWeights()))
                {
                    ps4.setLong(16, 1);
                } else
                {
                    ps4.setLong(16, Long.valueOf(aif.getWeights()));
                }
                if (null == aif.getPriority() || "".equals(aif.getPriority()))
                {
                    ps4.setLong(17, 1);
                } else
                {
                    ps4.setLong(17, Long.valueOf(aif.getPriority()));
                }
                if (null == aif.getOKFileFindWeek() || "".equals(aif.getOKFileFindWeek()))
                {
                    ps4.setLong(18, 0);
                } else
                {
                    ps4.setLong(18, Long.valueOf(aif.getOKFileFindWeek()));
                }
                ps4.setLong(19, Long.valueOf("1"));
                ps4.setString(20, aif.getCheckAgentGropName());
                // apt
                ps4.setString(21, aif.getAptGroupName());
                ps4.setString(22, aif.getAptFileName());
                ps4.setString(23, aif.getIsDB2());
                ps4.setString(24, aif.getDb2IP());
                ps4.setString(25, aif.getAptResGroupname());
                // apt
                ps4.setString(26, aif.getSystem());

                ps4.setString(27, aif.getRedo());
                ps4.setInt(28, aif.isAgentGroup() ? 0 : 1);
                ps4.setString(29, aif.getDelayTime());
                ps4.setString(30, aif.getBranchCondition());
                if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount())
                        && !"null".equals(aif.getReTryCount()))
                {
                    ps4.setInt(31, Integer.parseInt(aif.getReTryCount()));// ,IRETRYNUM,ICALENDNAME

                } else
                {
                    ps4.setInt(31, 0);
                }
                ps4.setString(32, aif.getCalendName());
                if (null != aif.getReTryTime() && !"".equals(aif.getReTryTime()) && !"null".equals(aif.getReTryTime()))
                {
                    ps4.setInt(33, Integer.parseInt(aif.getReTryTime()));//

                } else
                {
                    ps4.setInt(33, 0);
                }
                ps4.setString(34, aif.getReTryEndTime());
                ps4.setString(35, aif.getSkip());
                ps4.setString(36, aif.getDelayWarnning());
                ps4.setString(37, aif.getDays());
                ps4.setString(38, aif.getLogic());
                ps4.setString(39, aif.getWdays());
                ps4.setString(40, aif.getPerformUser() == null ? "" : aif.getPerformUser());
                ps4.setString(41, aif.getVirtualName() == null ? "" : aif.getVirtualName());
                ps4.setString(42, aif.getJoblist() == null ? "" : aif.getJoblist());
                ps4.setString(43, aif.getActNo() == null ? "" : aif.getActNo());
                ps4.setInt(44, "是".equals(aif.getEndTag()) ? 1: 0);
                /*	ps4.setString(45, aif.getOkFileName() == null ? "" : aif.getOkFileName());//ok文件名称*/
                ps4.setInt(45, aif.getUserTask());
                ps4.setString(46, aif.getMonth());
                if(Environment.getInstance().getGYBankSwitch()){
                    ps4.setInt(47,Integer.parseInt(aif.getActNo()));
                }else if(Environment.getInstance().getDGBankSwitch()){
                    ps4.setString(47,aif.getActParams());
                }else{
                    ps4.setString(47,aif.getJobType());
                }
                ps4.addBatch();

                List<String> listBefore = aif.getBeforeActList();

                for (int j = 0; j < listBefore.size(); j++)
                {
                    String before = listBefore.get(j);
                    ActInfoBean aifInfoBean = mapList.get(before);
                    ps1.setLong(1, id);
                    ps1.setString(2, aifInfoBean.getActName());
                    ps1.setString(3, aifInfoBean.getChildProjectName());
                    ps1.setString(4, aifInfoBean.getProjectName());
                    ps1.setString(5, aifInfoBean.getMainline());
                    ps1.addBatch();

                    ps5.setLong(1, id);
                    ps5.setString(2, aifInfoBean.getActName());
                    ps5.setString(3, aifInfoBean.getChildProjectName());
                    ps5.setString(4, aifInfoBean.getProjectName());
                    ps5.setString(5, aifInfoBean.getMainline());
                    ps5.addBatch();
                }
                List<String> listAfter = aif.getAfterActList();
                for (int j = 0; j < listAfter.size(); j++)
                {
                    String after = listAfter.get(j);
                    ActInfoBean aifInfoBean = mapList.get(after);
                    ps2.setLong(1, id);
                    ps2.setString(2, aifInfoBean.getActName());
                    ps2.setString(3, aifInfoBean.getChildProjectName());
                    ps2.setString(4, aifInfoBean.getProjectName());
                    ps2.setString(5, aifInfoBean.getMainline());
                    ps2.addBatch();

                    ps6.setLong(1, id);
                    ps6.setString(2, aifInfoBean.getActName());
                    ps6.setString(3, aifInfoBean.getChildProjectName());
                    ps6.setString(4, aifInfoBean.getProjectName());
                    ps6.setString(5, aifInfoBean.getMainline());
                    ps6.addBatch();
                }
                if (count == 0)
                {
                    ps7.setLong(1, id);
                    ps7.setString(2, aif.getProjectName());
                    ps7.setString(3, aif.getMainline());
                    ps7.setString(4, aif.getChildProjectName());
                    ps7.setString(5, aif.getActName());
                    ps7.setString(6, aif.getSystem());
                    ps7.setString(7, aif.getUserName());
                    ps7.addBatch();
                }

                proid = id;
                RepWorkflowInstance conBean = new RepWorkflowInstance();

                conBean.setProjectName(aif.getChildProjectName());
                conBean.setFlowName(aif.getActName());
                conBean.set_isystem(aif.getSystem());
                conBean.set_isetUser(aif.getUserName());

                if (null != aif.getDisableFlag() && "1".equals(aif.getDisableFlag()))
                {
                    // 新增禁用记录
                    addbeanList.add(conBean);
                } else if (null != aif.getDisableFlag() && "0".equals(aif.getDisableFlag()))
                {
                    // 删除禁用记录
                    deletebeanList.add(conBean);
                }
                if (count > 0 &&SystemConfig.isUpdateOperIdflag())
                {
                    updateOpertionId(aif, id, conn);
                }

                index++;
                if(index %1500==0){
                    ps.executeBatch();
                    ps4.executeBatch();
                    ps1.executeBatch();
                    ps5.executeBatch();
                    ps2.executeBatch();
                    ps6.executeBatch();
                    ps7.executeBatch();
                }

            }
            ps.executeBatch();
            ps4.executeBatch();
            ps1.executeBatch();
            ps5.executeBatch();
            ps2.executeBatch();
            ps6.executeBatch();
            ps7.executeBatch();
            if (!addbeanList.isEmpty())
            {
                this.saveDisableRuleForBatch(addbeanList, conn, basicType);
            }
            if (!deletebeanList.isEmpty())
            {
                this.deleteDisableRuleForBatch(deletebeanList, conn);
            }

        } catch (SQLException e)
        {
            log.error("saveExcelModel EXEC SAVE IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } catch (RepositoryException e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps5, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps6, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps7, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps8, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return proid;
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws RepositoryException
     */
    public Map saveUpdateExcelModel ( ActInfoBean aif, Map<String, ActInfoBean> mapList, String type, Connection conn,
                                      int basicType ) throws ServerException, SQLException, RepositoryException
    {
        Long proid = null;
        Map returnMap = new HashMap();

        String sql = "";

        String sql1 = "";

        String sql2 = "";
        String sqli = "";
        String sqlq = "";

        String sql5 = "";

        long id = 0;
        if ("copy".equals(type))
        {
            if(Environment.getInstance().getGYBankSwitch()){
                sql = "INSERT INTO IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,IACTNO) values(?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }else if(Environment.getInstance().getDGBankSwitch()){
                sql = "INSERT INTO IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,IACTPARAMS) values(?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }else{
                sql = "INSERT INTO IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING) values(?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }

            sql1 = "INSERT INTO IEAI_ACTPRE_COPY(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) VALUES(?,?,?,?,?)";

            sql2 = "INSERT INTO IEAI_ACTSUCC_COPY(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) VALUES(?,?,?,?,?)";

        } else
        {
            if(Environment.getInstance().getGYBankSwitch()){
                sql = "insert into IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,IACTNO) values(?,?,?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }else if(Environment.getInstance().getDGBankSwitch()){
                sql = "insert into IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,IACTPARAMS) values(?,?,?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }else{
                sql = "insert into IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING) values(?,?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }


            sql1 = "INSERT INTO IEAI_ACTPRE(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql2 = "INSERT INTO IEAI_ACTSUCC(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";
            sqli = "insert into IEAI_ACTFINISHED_FLAG_NEW (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values "
                    + " (?,?,(to_char(to_date(?, 'YYYYMMDD') - ?, 'YYYYMMDD')),"
                    + SQLSTIEAIEXCELMODELWHEMAI;
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqli = "INSERT INTO IEAI_ACTFINISHED_FLAG_NEW (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values "
                        + " (?,?,(to_char(to_date(?, 'YYYYMMDD') - ?, 'YYYYMMDD')),"
                        + SQLSELSYSDATERTIEAIEXCELMODELWH;
            }

            sqlq = "SELECT COUNT(T.IID) AS COU FROM IEAI_ACTFINISHED_FLAG_NEW T WHERE T.IPRONAME=? AND T.IFLOWNAME=? AND T.IACTNAME=? AND T.IDATADATE=(TO_CHAR(TO_DATE(?, 'YYYYMMDD') - ?, 'YYYYMMDD')) AND T.IOPERATIONID IN (SELECT T.IOPERATIONID FROM IEAI_EXCELMODEL T WHERE T.IMAINPRONAME=? AND T.ISYSTEM=? AND T.IMAINLINENAME=? AND T.IACTNAME=?)";

            sql5 = "SELECT TO_CHAR(MAX(TO_DATE(T2.IDATADATE, 'YYYYMMDD')),'YYYYMMDD') AS IDATADATE FROM IEAI_ACTFINISHED_FLAG_NEW T2 WHERE T2.IPRONAME=? AND T2.IFLOWNAME=? AND T2.IACTNAME=? ";

        }

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement presinsertws = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps1 = conn.prepareStatement(sql1);
            ps2 = conn.prepareStatement(sql2);
            if ("copy".equals(type))
            {
                id = generCountExcelModel( basicType);
            } else
            {
                id = getExcelModelIid(conn, aif);
                presinsertws = conn.prepareStatement(sqli);
                ps4 = conn.prepareStatement(sqlq);
                ps5 = conn.prepareStatement(sql5);
            }

            ps.setLong(1, id);
            ps.setString(2, aif.getProjectName());
            ps.setString(3, aif.getMainline());
            if ("总头".equals(aif.getSEFlag()))
            {
                ps.setLong(4, 1);
            } else if ("主线头".equals(aif.getSEFlag()))
            {
                ps.setLong(4, 2);
            } else if ("总尾".equals(aif.getSEFlag()))
            {
                ps.setLong(4, 6);
            } else if ("主线尾".equals(aif.getSEFlag()))
            {
                ps.setLong(4, 7);
            } else
            {
                ps.setLong(4, 0);
            }
            ps.setString(5, StringUtils.isBlank(aif.getAgentInfo()) ? aif.getAgentGropName() : aif.getAgentInfo());
            ps.setString(6, aif.getOKFileABPath());
            ps.setString(7, "数据日期");
            ps.setString(8, aif.getChildProjectName());
            ps.setLong(9, Long.valueOf(aif.getDeleteFlag()));
            ps.setString(10, aif.getActName());
            ps.setString(11, aif.getDescribe());
            ps.setString(12, aif.getOutputPamaeter());
            ps.setString(13, aif.getShellCrust());
            ps.setString(14, aif.getShellABPath());
            if (null == aif.getOKFileABPath() || "".equals(aif.getOKFileABPath()))
            {
                ps.setString(15, "0");
            } else
            {
                ps.setString(15, "0,nf");
            }
            if (null == aif.getWeights() || "".equals(aif.getWeights()))
            {
                ps.setLong(16, 1);
            } else
            {
                ps.setLong(16, Long.valueOf(aif.getWeights()));
            }
            if (null == aif.getPriority() || "".equals(aif.getPriority()))
            {
                ps.setLong(17, 1);
            } else
            {
                ps.setLong(17, Long.valueOf(aif.getPriority()));
            }
            if (null == aif.getOKFileFindWeek() || "".equals(aif.getOKFileFindWeek()))
            {
                ps.setLong(18, 0);
            } else
            {
                ps.setLong(18, Long.valueOf(aif.getOKFileFindWeek()));
            }
            ps.setLong(19, Long.valueOf("1"));
            ps.setString(20, aif.getCheckAgentGropName());
            // apt
            ps.setString(21, aif.getAptGroupName());
            ps.setString(22, aif.getAptFileName());
            ps.setString(23, aif.getIsDB2());
            ps.setString(24, aif.getDb2IP());
            ps.setString(25, aif.getAptResGroupname());
            // apt
            ps.setString(26, aif.getSystem());
            ps.setString(27, aif.getRedo());
            ps.setInt(28, aif.isAgentGroup() ? 0 : 1);
            ps.setString(29, aif.getDelayTime());
            ps.setString(30, aif.getBranchCondition());
            if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount()) && !"null".equals(aif.getReTryCount()))
            {
                ps.setInt(31, Integer.parseInt(aif.getReTryCount()));//

            } else
            {
                ps.setInt(31, 0);// ,IRETRYNUM,ICALENDNAME
            }
            ps.setString(32, aif.getCalendName());
            // ,IRETRYNUM,ICALENDNAME
            if (null != aif.getReTryTime() && !"".equals(aif.getReTryTime()) && !"null".equals(aif.getReTryTime()))
            {
                ps.setInt(33, Integer.parseInt(aif.getReTryTime()));//

            } else
            {
                ps.setInt(33, 0);
            }
            ps.setString(34, aif.getReTryEndTime());
            ps.setString(35, aif.getSkip());
            ps.setString(36, aif.getDelayWarnning());
            if(Environment.getInstance().getGYBankSwitch()){
                ps.setInt(37, Integer.parseInt(aif.getActNo()));
            }else if(Environment.getInstance().getDGBankSwitch()){
                ps.setString(37,aif.getActParams());
            }
            // ,IRETRYTIME,IRETRYENDTIME
            ps.executeUpdate();
            if (SystemConfig.isUpdateOperIdflag())
            {
                updateExcelModelOpertionId(aif, id, conn);
            }
            List<String> listBefore = aif.getBeforeActList();

            for (int j = 0; j < listBefore.size(); j++)
            {
                String before = listBefore.get(j);
                ActInfoBean aifInfoBean = mapList.get(before);
                ps1.setLong(1, id);
                ps1.setString(2, aifInfoBean.getActName());
                ps1.setString(3, aifInfoBean.getChildProjectName());
                ps1.setString(4, aifInfoBean.getProjectName());
                ps1.setString(5, aifInfoBean.getMainline());
                ps1.executeUpdate();
                if (!"copy".equals(type))
                {
                    if (!aif.getMainline().equals(aifInfoBean.getMainline()) && SystemConfig.isInsertFlag() && ps5 != null)
                    {

                        String datadate = "";
                        ps5.setString(1, aifInfoBean.getProjectName());
                        ps5.setString(2, aifInfoBean.getMainline());
                        ps5.setString(3, aifInfoBean.getActName());
                        int forcount = 0;
                        try
                        {
                            rs = ps5.executeQuery();
                            forcount = SystemConfig.getInsertData();
                            while (rs.next())
                            {
                                datadate = rs.getString(CIDATADATE);
                            }
                        } catch (Exception e)
                        {
                            log.info("saveUpdateExcelModel is get datadate is error:" + e.getMessage());
                        }

                        if (!"".equals(datadate) && null != datadate)
                        {
                            for (int ii = 0; ii <= forcount; ii++)
                            {
                                int count = 0;
                                ps4.setString(1, aifInfoBean.getProjectName());
                                ps4.setString(2, aifInfoBean.getMainline());
                                ps4.setString(3, aifInfoBean.getActName());
                                ps4.setString(4, datadate);
                                ps4.setInt(5, ii);
                                ps4.setString(6, aif.getProjectName());
                                ps4.setString(7, aif.getSystem());
                                ps4.setString(8, aif.getMainline());
                                ps4.setString(9, aif.getActName());
                                rs = ps4.executeQuery();
                                while (rs.next())
                                {
                                    count = rs.getInt("cou");
                                }
                                if (count <= 0)
                                {
                                    long actFlagId = IdGenerator.createIdNoConnection(IEAIACTFINISHEDFLAGNEW,
                                            basicType);
                                    presinsertws.setLong(1, actFlagId);
                                    presinsertws.setLong(2, ii + 1L);
                                    presinsertws.setString(3, datadate);
                                    presinsertws.setInt(4, ii);
                                    presinsertws.setString(5, aifInfoBean.getProjectName());
                                    presinsertws.setString(6, aifInfoBean.getMainline());
                                    presinsertws.setString(7, aifInfoBean.getActName());
                                    presinsertws.setString(8, aif.getProjectName());
                                    presinsertws.setString(9, aif.getSystem());
                                    presinsertws.setString(10, aif.getMainline());
                                    presinsertws.setString(11, aif.getActName());
                                    presinsertws.addBatch();
                                }
                            }
                        }
                    }

                }
                if (!"copy".equals(type) && SystemConfig.isInsertFlag() && presinsertws != null)
                {
                    presinsertws.executeBatch();
                }
            }
            List<String> listAfter = aif.getAfterActList();
            for (int j = 0; j < listAfter.size(); j++)
            {
                String after = listAfter.get(j);
                ActInfoBean aifInfoBean = mapList.get(after);
                ps2.setLong(1, id);
                ps2.setString(2, aifInfoBean.getActName());
                ps2.setString(3, aifInfoBean.getChildProjectName());
                ps2.setString(4, aifInfoBean.getProjectName());
                ps2.setString(5, aifInfoBean.getMainline());
                ps2.executeUpdate();
            }

            proid = id;
            RepWorkflowInstance conBean = new RepWorkflowInstance();

            conBean.setProjectName(aif.getChildProjectName());
            conBean.setFlowName(aif.getActName());
            conBean.set_isystem(aif.getSystem());
            conBean.set_isetUser(aif.getUserName());

            if(queryCountForConfig(aif.getChildProjectName(), aif.getActName(), aif.getSystem()) > 0)
            {
                deleteConfig(aif.getChildProjectName(), aif.getActName(), aif.getSystem());
            }

            if (null != aif.getDisableFlag() && "1".equals(aif.getDisableFlag()))
            {
                // 新增禁用记录
                returnMap.put(CADDBEAN, conBean);
            } else if (null != aif.getDisableFlag() && "0".equals(aif.getDisableFlag()))
            {
                // 删除禁用记录
                returnMap.put(CDELETEBEAN, conBean);
            }
            if (SystemConfig.isUpdateOperIdflag())
            {
                returnMap.put("id", id);
            }
            if (SystemConfig.isUpdateOperIdflag())
            {
                updatePreSuccOpertionId(aif, id, conn);
            }


        } catch (SQLException e)
        {
            log.error("saveExcelModel EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(presinsertws, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps5, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps6, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        returnMap.put(CPROID, proid);
        return returnMap;
    }

    public boolean checkDisableJobIsExists ( String prjName, String flowName, Connection con )
            throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean flag = false; // false 禁用作业 不存在 永久禁用规则
        String sqlIsExists = " SELECT" + "  COUNT(C.IID ) AS CNUM " + " FROM " + "  IEAI_TASK_DISABLE_CON C,"
                + "  IEAI_TASK_DISABLE_RULE R " + " WHERE " + "  C.IPRONAME =? AND " + "  C.IFLOWNAME=? AND "
                + "  ( R.IISAPP=0 AND " + "  R.IISONLYONE =0 AND " + "  R.IDATADATEF IS NULL AND "
                + "  R.IDATADATET IS NULL ) AND " + "  C.FIID=R.IID ";
        try
        {
            ps = con.prepareStatement(sqlIsExists);
            ps.setString(1, prjName);
            ps.setString(2, flowName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                if (rs.getInt("CNUM") > 0)
                {
                    flag = true;
                }
            }
        } catch (SQLException e)
        {
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "checkDisableJobIsExists", log);
        }
        return flag;
    }

    /**
     *
     * @Title: saveDisableRuleForBatch
     * @Description: (上传excel模板时增加禁用记录(批量))
     * @param conBeanList
     * @param con
     * @throws RepositoryException
     * @throws SQLException
     * @return void 返回类型
     * @throws @变更记录 2015-8-27 yunpeng_zhang
     */
    public void saveDisableRuleForBatch ( List<RepWorkflowInstance> conBeanList, Connection con, int basicType )
            throws RepositoryException, SQLException
    {
        long startUploadExcel = System.currentTimeMillis();
        String sqlRule = "";
        String sqlFlow = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlRule = "INSERT INTO IEAI_TASK_DISABLE_RULE(IID, IISAPP, IISONLYONE, IISINSUCC) VALUES(?,?,?,?)";
            sqlFlow = "INSERT INTO IEAI_TASK_DISABLE_CON(IID, FIID, IPRONAME, IFLOWNAME,ISYSTEM,ISETTIME,ISETUSER,IHEADFLOWNAME) VALUES(?,?, ?,?,?,date_format(now(),'%Y-%m-%d') ,?,?)";
        }else {
            sqlRule = "INSERT INTO IEAI_TASK_DISABLE_RULE(IID, IISAPP, IISONLYONE, IISINSUCC) VALUES(?,?,?,?)";
            sqlFlow = "INSERT INTO IEAI_TASK_DISABLE_CON(IID, FIID, IPRONAME, IFLOWNAME,ISYSTEM,ISETTIME,ISETUSER,IHEADFLOWNAME) VALUES(?,?, ?,?,?,to_char(sysdate,'yyyy-mm-dd hh24:mi:ss'),?,?)";
        }

        for (int i = 0; i < 10; i++)
        {
            PreparedStatement ps = null;
            PreparedStatement ps2 = null;
            try
            {
                ps = con.prepareStatement(sqlRule);
                ps2 = con.prepareStatement(sqlFlow);
                int countNum = 0;
                for (RepWorkflowInstance conBean : conBeanList)
                {

                    boolean flag = checkDisableJobIsExists(conBean.getProjectName(), conBean.getFlowName(), con);
                    if (flag)
                    {
                        continue;
                    }
                    long fid = IdGenerator.createIdNoConnection("IEAI_TASK_DISABLE_RULE", basicType);
                    ps.setLong(1, fid);
                    ps.setLong(2, 0);
                    ps.setLong(3, 0);
                    ps.setLong(4, 0);
                    ps.addBatch();

                    long iid = IdGenerator.createIdNoConnection("IEAI_TASK_DISABLE_CON", basicType);
                    ps2.setLong(1, iid);
                    ps2.setLong(2, fid);
                    ps2.setString(3, conBean.getProjectName());
                    ps2.setString(4, conBean.getFlowName());
                    ps2.setString(5, conBean.get_isystem());
                    ps2.setString(6, conBean.get_isetUser());
                    ps2.setString(7, conBean.getFlowName());
                    ps2.addBatch();
                    if (countNum > 0 && (countNum % 2000 == 0))
                    {
                        ps.executeBatch();
                        ps2.executeBatch();
                    }
                    countNum++;
                    EngineRepositotyJdbc.getInstance().saveDisableRuleUploadExcelActFinshedFlag(
                            conBean.getProjectName(), conBean.get_isystem(), conBean.getFlowName(), conBean.getFlowName(),
                            con);
                }
                ps.executeBatch();
                ps2.executeBatch();
                break;
            } catch (SQLException e)
            {
                log.error("saveDisableRuleForBatch method of UpLoadExcelManager.class SQLException  :"
                        + e.getMessage());
                DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
            } catch (RepositoryException e)
            {
                log.error("saveDisableRuleForBatch method of UpLoadExcelManager.class RepositoryException  :"
                        + e.getMessage());
                DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closePreparedStatement(ps, "saveDisableRuleForBatche", log);
                DBResource.closePreparedStatement(ps2, "saveDisableRuleForBatche", log);
            }

        }
        long endUploadExcel = System.currentTimeMillis() - startUploadExcel;
        log.info("Excel导入功能耗时流水：新增禁用规则" + conBeanList.size() + "条耗时为：" + endUploadExcel + " 毫秒");
    }

    /**
     *
     * @Title: deleteDisableRuleForBatch
     * @Description: (上传excel模板时删除禁用记录(批量))
     * @param conBeanList
     * @param con
     * @throws SQLException
     * @throws RepositoryException
     * @return void 返回类型
     * @throws @变更记录 2015-8-27 yunpeng_zhang
     */
    public void deleteDisableRuleForBatch ( List<RepWorkflowInstance> conBeanList, Connection con )
            throws SQLException, RepositoryException
    {
        long startUploadExcel = System.currentTimeMillis();
        String sqlRule = "DELETE FROM IEAI_TASK_DISABLE_RULE T WHERE T.IID IN(SELECT T2.FIID FROM IEAI_TASK_DISABLE_CON T2 WHERE T2.IPRONAME=? AND T2.IFLOWNAME=?)";
        String sqlFlow = "DELETE FROM IEAI_TASK_DISABLE_CON T2 WHERE T2.IPRONAME=? AND T2.IFLOWNAME=?";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlRule = "DELETE FROM IEAI_TASK_DISABLE_RULE WHERE IID IN(SELECT T2.FIID FROM IEAI_TASK_DISABLE_CON T2 WHERE T2.IPRONAME=? AND T2.IFLOWNAME=?)";
            sqlFlow = "DELETE FROM IEAI_TASK_DISABLE_CON WHERE IPRONAME=? AND IFLOWNAME=?";
        }
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement ps = null;
            PreparedStatement ps2 = null;
            try
            {
                ps = con.prepareStatement(sqlRule);
                ps2 = con.prepareStatement(sqlFlow);
                int countNum = 0;
                for (RepWorkflowInstance conBean : conBeanList)
                {
                    ps.setString(1, conBean.getProjectName());
                    ps.setString(2, conBean.getFlowName());
                    ps.addBatch();

                    ps2.setString(1, conBean.getProjectName());
                    ps2.setString(2, conBean.getFlowName());
                    ps2.addBatch();
                    if (countNum > 0 && (countNum % 2000 == 0))
                    {
                        ps.executeBatch();
                        ps2.executeBatch();
                    }
                    EngineRepositotyJdbc.getInstance().delUploadExcelDisableRulesFinshFlag(conBean.getProjectName(),
                            conBean.get_isystem(), conBean.getFlowName(), conBean.getFlowName(), con);
                    countNum++;
                }
                ps.executeBatch();
                ps2.executeBatch();
                break;
            } catch (SQLException e)
            {
                log.error("deleteDisableRuleForBatche method of UpLoadExcelManager.class SQLException :"
                        + e.getMessage());
                DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closePreparedStatement(ps, "deleteDisableRuleForBatche", log);
                DBResource.closePreparedStatement(ps2, "deleteDisableRuleForBatche", log);
            }

        }
        long endUploadExcel = System.currentTimeMillis() - startUploadExcel;
        log.info("Excel导入功能耗时流水：删除禁用规则" + conBeanList.size() + "条耗时为：" + endUploadExcel + " 毫秒");

    }

    /**
     * <AUTHOR>
     * @des:修改excel及依赖触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public Map updateExcelModel ( ActInfoBean aif, Map<String, ActInfoBean> mapList, String actId, String type,
                                  Connection conn ) throws  SQLException, RepositoryException
    {
        Long proid = null;

        Map returnMap = new HashMap();

        String sql3 = "";

        String sql1 = "";

        String sql2 = "";

        String sqli = "";

        String sqlq = "";

        String sql5 = "";

        if ("copy".equals(type))
        {

            if(Environment.getInstance().getGYBankSwitch()){
                sql3 = "update IEAI_EXCELMODEL_COPY set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=?,IAUTOSKIP=? ,IDELAYWARNNING=?,IACTNO=? where IOPERATIONID=?";
            }else if(Environment.getInstance().getDGBankSwitch()){
                sql3 = "update IEAI_EXCELMODEL_COPY set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=?,IAUTOSKIP=? ,IDELAYWARNNING=?,IACTPARAMS=? where IOPERATIONID=?";
            }else{
                sql3 = "update IEAI_EXCELMODEL_COPY set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=?,IAUTOSKIP=? ,IDELAYWARNNING=? where IOPERATIONID=?";
            }

            sql1 = "INSERT INTO IEAI_ACTPRE_COPY(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) VALUES(?,?,?,?,?)";

            sql2 = "INSERT INTO IEAI_ACTSUCC_COPY(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) VALUES(?,?,?,?,?)";
        } else
        {
            if(Environment.getInstance().getGYBankSwitch()){
                sql3 = "update IEAI_EXCELMODEL set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=? ,IAUTOSKIP=?,IDELAYWARNNING=?,IACTNO=? where IOPERATIONID=?";
            }else if(Environment.getInstance().getDGBankSwitch()){
                sql3 = "update IEAI_EXCELMODEL set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=? ,IAUTOSKIP=?,IDELAYWARNNING=?,IACTPARAMS=?  where IOPERATIONID=?";
            }else{
                sql3 = "update IEAI_EXCELMODEL set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=? ,IAUTOSKIP=?,IDELAYWARNNING=? where IOPERATIONID=?";
            }

            sql1 = "INSERT INTO IEAI_ACTPRE(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql2 = "INSERT INTO IEAI_ACTSUCC(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sqli = "INSERT INTO IEAI_ACTFINISHED_FLAG_NEW (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values "
                    + SQLTOCHARTDATEYYMMDD + " ?, ?, ?, SYSDATE, ?)";
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqli = "INSERT INTO IEAI_ACTFINISHED_FLAG_NEW (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) VALUES "
                        + SQLTOCHARTDATEYYMMDD + " ?, ?, ?, sysdate(), ?)";
            }
            sqlq = "select count(t.iid) as cou from ieai_actfinished_flag_new t where t.iproname=? and t.iflowname=? and t.iactname=? and t.idatadate=(to_char(to_date(?, 'YYYYMMDD') - ?, 'YYYYMMDD')) and t.ioperationid=?";

            sql5 = "SELECT TO_CHAR(MAX(TO_DATE(T2.IDATADATE, 'YYYYMMDD')),'YYYYMMDD') AS IDATADATE FROM IEAI_ACTFINISHED_FLAG_NEW T2 WHERE T2.IPRONAME=? AND T2.IFLOWNAME=? AND T2.IACTNAME=?";

        }

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement presinsertws = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql3);
            ps1 = conn.prepareStatement(sql1);
            ps2 = conn.prepareStatement(sql2);
            if (!"copy".equals(type))
            {
                presinsertws = conn.prepareStatement(sqli);
                ps4 = conn.prepareStatement(sqlq);
                ps5 = conn.prepareStatement(sql5);
            }

            ps.setString(1, aif.getProjectName());
            ps.setString(2, aif.getMainline());
            if ("总头".equals(aif.getSEFlag()))
            {
                ps.setLong(3, 1);
            } else if ("主线头".equals(aif.getSEFlag()))
            {
                ps.setLong(3, 2);
            } else if ("总尾".equals(aif.getSEFlag()))
            {
                ps.setLong(3, 6);
            } else if ("主线尾".equals(aif.getSEFlag()))
            {
                ps.setLong(3, 7);
            } else
            {
                ps.setLong(3, 0);
            }
            if (StringUtils.isNotBlank(aif.getAgentInfo()))
            {
                ps.setString(4, aif.getAgentInfo());
            } else
            {
                ps.setString(4, aif.getAgentGropName());
            }
            ps.setString(5, aif.getOKFileABPath());
            ps.setString(6, "数据日期");
            ps.setString(7, aif.getChildProjectName());
            ps.setLong(8, Long.valueOf(aif.getDeleteFlag()));
            ps.setString(9, aif.getActName());
            ps.setString(10, aif.getDescribe());
            ps.setString(11, aif.getOutputPamaeter());
            ps.setString(12, aif.getShellCrust());
            ps.setString(13, aif.getShellABPath());
            if (null == aif.getOKFileABPath() || "".equals(aif.getOKFileABPath()))
            {
                ps.setString(14, "0");
            } else
            {
                ps.setString(14, "0,nf");
            }
            if (null == aif.getWeights() || "".equals(aif.getWeights()))
            {
                ps.setLong(15, 1);
            } else
            {
                ps.setLong(15, Long.valueOf(aif.getWeights()));
            }
            if (null == aif.getPriority() || "".equals(aif.getPriority()))
            {
                ps.setLong(16, 1);
            } else
            {
                ps.setLong(16, Long.valueOf(aif.getPriority()));
            }
            if (null == aif.getOKFileFindWeek() || "".equals(aif.getOKFileFindWeek()))
            {
                ps.setLong(17, 0);
            } else
            {
                ps.setLong(17, Long.valueOf(aif.getOKFileFindWeek()));
            }
            ps.setLong(18, Long.valueOf(aif.getFlag()));
            ps.setString(19, aif.getCheckAgentGropName());
            // apt
            ps.setString(20, aif.getAptGroupName());
            ps.setString(21, aif.getAptFileName());
            ps.setString(22, aif.getIsDB2());
            ps.setString(23, aif.getDb2IP());
            ps.setString(24, aif.getAptResGroupname());
            // apt
            ps.setString(25, aif.getSystem());
            ps.setString(26, aif.getRedo());
            ps.setInt(27, aif.isAgentGroup() ? 0 : 1);
            ps.setString(28, aif.getDelayTime());
            ps.setString(29, aif.getBranchCondition());
            if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount()) && !"null".equals(aif.getReTryCount()))
            {
                ps.setInt(30, Integer.parseInt(aif.getReTryCount()));//

            } else
            {
                ps.setInt(30, 0);// ,IRETRYNUM,ICALENDNAME
            }
            ps.setString(31, aif.getCalendName());
            if (null != aif.getReTryTime() && !"".equals(aif.getReTryTime()) && !"null".equals(aif.getReTryTime()))
            {
                ps.setInt(32, Integer.parseInt(aif.getReTryTime()));//

            } else
            {
                ps.setInt(32, 0);// ,IRETRYNUM,ICALENDNAME
            }
            ps.setString(33, aif.getReTryEndTime());
            ps.setString(34, aif.getSkip());
            ps.setString(35, aif.getDelayWarnning());
            if(Environment.getInstance().getGYBankSwitch()){
                ps.setInt(36, Integer.parseInt(aif.getActNo()));
                ps.setLong(37, Long.valueOf(actId));
            }else if(Environment.getInstance().getDGBankSwitch()){
                ps.setString(36, aif.getActParams());
                ps.setLong(37, Long.valueOf(actId));
            }else{
                ps.setLong(36, Long.valueOf(actId));
            }

            ps.executeUpdate();

            List<String> listBefore = aif.getBeforeActList();
            if (!listBefore.isEmpty())
            {
                for (int j = 0; j < listBefore.size(); j++)
                {
                    String before = listBefore.get(j);
                    ActInfoBean aifInfoBean = mapList.get(before);
                    ActPreBean actPreBeans = selectOneActpreManager(Long.valueOf(actId), aifInfoBean.getProjectName(),
                            aifInfoBean.getMainline(), aifInfoBean.getChildProjectName(), aifInfoBean.getActName(), type,
                            conn);
                    if (null == actPreBeans)
                    {
                        ps1.setLong(1, Long.valueOf(actId));
                        ps1.setString(2, aifInfoBean.getActName());
                        ps1.setString(3, aifInfoBean.getChildProjectName());
                        ps1.setString(4, aifInfoBean.getProjectName());
                        ps1.setString(5, aifInfoBean.getMainline());
                        ps1.executeUpdate();

                        if (!"copy".equals(type) &&!aif.getMainline().equals(aifInfoBean.getMainline()) && SystemConfig.isInsertFlag() && ps5 != null)
                        {
                            String datadate = "";
                            ps5.setString(1, aifInfoBean.getProjectName());
                            ps5.setString(2, aifInfoBean.getMainline());
                            ps5.setString(3, aifInfoBean.getActName());
                            int forcount = 0;
                            try
                            {
                                rs = ps5.executeQuery();
                                forcount = SystemConfig.getInsertData();
                                while (rs.next())
                                {
                                    datadate = rs.getString(CIDATADATE);
                                }
                            } catch (Exception e)
                            {
                                log.info("updateExcelModel is get datadate is error:" + e.getMessage());
                            }

                            if (!"".equals(datadate) && null != datadate)
                            {

                                for (int ii = 0; ii <= forcount; ii++)
                                {
                                    int count = 0;
                                    ps4.setString(1, aifInfoBean.getProjectName());
                                    ps4.setString(2, aifInfoBean.getMainline());
                                    ps4.setString(3, aifInfoBean.getActName());
                                    ps4.setString(4, datadate);
                                    ps4.setInt(5, ii);
                                    ps4.setLong(6, Long.valueOf(actId));
                                    rs = ps4.executeQuery();
                                    while (rs.next())
                                    {
                                        count = rs.getInt("cou");
                                    }
                                    if (count <= 0)
                                    {
                                        long actFlagId = IdGenerator.createId(IEAIACTFINISHEDFLAGNEW,
                                                conn);
                                        presinsertws.setLong(1, actFlagId);
                                        presinsertws.setLong(2, ii + 1L);
                                        presinsertws.setString(3, datadate);
                                        presinsertws.setInt(4, ii);
                                        presinsertws.setString(5, aifInfoBean.getProjectName());
                                        presinsertws.setString(6, aifInfoBean.getMainline());
                                        presinsertws.setString(7, aifInfoBean.getActName());
                                        presinsertws.setLong(8, Long.valueOf(actId));
                                        presinsertws.addBatch();
                                    }
                                }
                            }
                        }


                    }
                }
                if (!"copy".equals(type) && SystemConfig.isInsertFlag() && presinsertws != null)
                {
                    presinsertws.executeBatch();
                }
            }
            List<String> listAfter = aif.getAfterActList();
            if (!listAfter.isEmpty())
            {
                for (int j = 0; j < listAfter.size(); j++)
                {
                    String after = listAfter.get(j);
                    ActInfoBean aifInfoBean = mapList.get(after);
                    ActSuccBean actSuccBean = selectOneActsuccManager(Long.valueOf(actId),
                            aifInfoBean.getProjectName(), aifInfoBean.getMainline(), aifInfoBean.getChildProjectName(),
                            aifInfoBean.getActName(), type, conn);
                    if (actSuccBean == null)
                    {
                        ps2.setLong(1, Long.valueOf(actId));
                        ps2.setString(2, aifInfoBean.getActName());
                        ps2.setString(3, aifInfoBean.getChildProjectName());
                        ps2.setString(4, aifInfoBean.getProjectName());
                        ps2.setString(5, aifInfoBean.getMainline());
                        ps2.executeUpdate();
                    }

                }
            }

            proid = new Long(actId);
            RepWorkflowInstance conBean = new RepWorkflowInstance();

            conBean.setProjectName(aif.getChildProjectName());
            conBean.setFlowName(aif.getActName());
            conBean.set_isystem(aif.getSystem());
            conBean.set_isetUser(aif.getUserName());

            if (null != aif.getDisableFlag() && "1".equals(aif.getDisableFlag()))
            {
                // 新增禁用记录
                returnMap.put(CADDBEAN, conBean);
            } else if (null != aif.getDisableFlag() && "0".equals(aif.getDisableFlag()))
            {
                // 删除禁用记录
                returnMap.put(CDELETEBEAN, conBean);
            }

        } catch (SQLException e)
        {
            log.error("updateExcelModel EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(presinsertws, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps5, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        returnMap.put(CPROID, proid);
        return returnMap;
    }

    /**
     * <AUTHOR>
     * @des:修改excel信息的状态
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void updateExcelModelFlag ( String proName, String mainLineName, String childrenName, String actName,
                                       Long flag, String operatorId, String sysName, String userName, String type, Connection conn )
            throws  SQLException, RepositoryException
    {

        String sql3 = "";

        String sql1 = "";

        String sql2 = "";

        String sql5 = "";

        String sql4 = "";

        if ("copy".equals(type))
        {
            sql3 = "update IEAI_EXCELMODEL_COPY set IFLAG=? where IMAINPRONAME=? and IMAINLINENAME=? and ICHILDPRONAME=? and IACTNAME=? and ISYSTEM=?";

            sql1 = "delete from IEAI_ACTPRE_COPY where IOPERATIONID=?";

            sql2 = "delete from IEAI_ACTSUCC_COPY where IOPERATIONID=?";

            sql5 = "select count(ioldoperationid) as cou from ieai_deloptionid  where imainproname=? and ICHILDPRONAME=? AND imainlinename=? and iactname=? and isystem=? ";

            sql4 = "INSERT INTO IEAI_DELOPTIONID (ioldoperationid, imainproname,ICHILDPRONAME, imainlinename, iactname, isystem,IPERATIONUSER,IENDTIME) values (?, ?,?,?, ?, ?,?,SYSDATE)";

            if (JudgeDB.IEAI_DB_TYPE == 3)
            {

                sql4 = "INSERT INTO IEAI_DELOPTIONID (ioldoperationid, imainproname,ICHILDPRONAME, imainlinename, iactname, isystem,IPERATIONUSER,IENDTIME) values (?, ?,?,?, ?, ?,?,sysdate())";
            }
        } else
        {
            sql3 = "update IEAI_EXCELMODEL set IFLAG=? where IMAINPRONAME=? and IMAINLINENAME=? and ICHILDPRONAME=? and IACTNAME=? and ISYSTEM=?";

            sql1 = "delete from IEAI_ACTPRE where IOPERATIONID=?";

            sql2 = "delete from IEAI_ACTSUCC where IOPERATIONID=?";

            sql5 = "select count(ioldoperationid) as cou from ieai_deloptionid  where imainproname=? and ICHILDPRONAME=? AND imainlinename=? and iactname=? and isystem=? ";

            sql4 = "INSERT INTO IEAI_DELOPTIONID (IOLDOPERATIONID, IMAINPRONAME,ICHILDPRONAME, IMAINLINENAME, IACTNAME, ISYSTEM,IPERATIONUSER,IENDTIME) VALUES (?, ?,?,?, ?, ?,?,SYSDATE)";

            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sql4 = "INSERT INTO IEAI_DELOPTIONID (IOLDOPERATIONID, IMAINPRONAME,ICHILDPRONAME, IMAINLINENAME, IACTNAME, ISYSTEM,IPERATIONUSER,IENDTIME) VALUES (?, ?,?,?, ?, ?,?,SYSDATE())";
            }
        }

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        ResultSet rs = null;
        int count = 0;
        try
        {
            ps = conn.prepareStatement(sql3);
            ps.setLong(1, flag);
            ps.setString(2, proName);
            ps.setString(3, mainLineName);
            ps.setString(4, childrenName);
            ps.setString(5, actName);
            ps.setString(6, sysName);
            ps.executeUpdate();

            ps1 = conn.prepareStatement(sql1);
            ps1.setLong(1, Long.valueOf(operatorId));
            ps1.executeUpdate();

            ps2 = conn.prepareStatement(sql2);
            ps2.setLong(1, Long.valueOf(operatorId));
            ps2.executeUpdate();
            if (SystemConfig.isUpdateOperIdflag())
            {
                ps4 = conn.prepareStatement(sql5);
                ps4.setString(1, proName);
                ps4.setString(2, childrenName);
                ps4.setString(3, mainLineName);
                ps4.setString(4, actName);
                ps4.setString(5, sysName);
                rs = ps4.executeQuery();
                while (rs.next())
                {
                    count = rs.getInt("cou");
                }
                if (count <= 0)
                {
                    ps3 = conn.prepareStatement(sql4);
                    ps3.setLong(1, Long.valueOf(operatorId));
                    ps3.setString(2, proName);
                    ps3.setString(3, childrenName);
                    ps3.setString(4, mainLineName);
                    ps3.setString(5, actName);
                    ps3.setString(6, sysName);
                    ps3.setString(7, userName);
                    ps3.executeUpdate();
                }
            }
        } catch (SQLException e)
        {
            log.error("updateExcelModelFlag EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:修改excel信息的状态
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void updateOpertionId ( ActInfoBean aif, Long operationid, Connection conn ) throws
            SQLException, RepositoryException
    {

        String sql3 = "UPDATE IEAI_EXCELMODEL T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=? ";

        String sql1 = "UPDATE IEAI_ACTPRE T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=?";

        String sql2 = "UPDATE IEAI_ACTSUCC T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=?  ";

        String sql4 = "SELECT count(T1.ioldoperationid) as cou FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?";

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        ResultSet rs = null;
        int count = 0;
        try
        {

            ps3 = conn.prepareStatement(sql4);
            ps3.setString(1, aif.getProjectName());
            ps3.setString(2, aif.getMainline());
            ps3.setString(3, aif.getActName());
            ps3.setString(4, aif.getSystem());
            ps3.setString(5, aif.getChildProjectName());
            rs = ps3.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("cou");
            }
            if (count > 0)
            {
                ps = conn.prepareStatement(sql3);
                ps.setString(1, aif.getProjectName());
                ps.setString(2, aif.getMainline());
                ps.setString(3, aif.getActName());
                ps.setString(4, aif.getSystem());
                ps.setString(5, aif.getChildProjectName());
                ps.setLong(6, operationid);
                ps.executeUpdate();

                ps1 = conn.prepareStatement(sql1);
                ps1.setString(1, aif.getProjectName());
                ps1.setString(2, aif.getMainline());
                ps1.setString(3, aif.getActName());
                ps1.setString(4, aif.getSystem());
                ps1.setString(5, aif.getChildProjectName());
                ps1.setLong(6, operationid);
                ps1.executeUpdate();

                ps2 = conn.prepareStatement(sql2);
                ps2.setString(1, aif.getProjectName());
                ps2.setString(2, aif.getMainline());
                ps2.setString(3, aif.getActName());
                ps2.setString(4, aif.getSystem());
                ps2.setString(5, aif.getChildProjectName());
                ps2.setLong(6, operationid);
                ps2.executeUpdate();
            }

        } catch (SQLException e)
        {
            log.error("updateOpertionId EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:修改excel信息的状态
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void updateExcelModelOpertionId ( ActInfoBean aif, Long operationid, Connection conn )
            throws  SQLException, RepositoryException
    {

        String sql3 = "UPDATE IEAI_EXCELMODEL T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=? ";

        String sql4 = "SELECT T1.ioldoperationid FROM IEAI_DELOPTIONID T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?";

        String sql5 = "insert into ieai_deloptionid (ioldoperationid, imainproname,ICHILDPRONAME, imainlinename, iactname, isystem,IPERATIONUSER,IENDTIME) values (?, ?,?,?, ?, ?,?,SYSDATE)";

        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql5 = "insert into ieai_deloptionid (ioldoperationid, imainproname,ICHILDPRONAME, imainlinename, iactname, isystem,IPERATIONUSER,IENDTIME) values (?, ?,?,?, ?, ?,?,sysdate())";
        }
        PreparedStatement ps = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        ResultSet rs = null;
        long oldid = 0;
        try
        {

            ps3 = conn.prepareStatement(sql4);
            ps3.setString(1, aif.getProjectName());
            ps3.setString(2, aif.getMainline());
            ps3.setString(3, aif.getActName());
            ps3.setString(4, aif.getSystem());
            ps3.setString(5, aif.getChildProjectName());
            rs = ps3.executeQuery();
            while (rs.next())
            {
                oldid = rs.getLong(CIOLDOPERATIONID);
            }
            if (oldid > 0)
            {
                if (oldid != operationid)
                {
                    ps = conn.prepareStatement(sql3);
                    ps.setString(1, aif.getProjectName());
                    ps.setString(2, aif.getMainline());
                    ps.setString(3, aif.getActName());
                    ps.setString(4, aif.getSystem());
                    ps.setString(5, aif.getChildProjectName());
                    ps.setLong(6, operationid);
                    ps.executeUpdate();
                }
            } else
            {
                ps4 = conn.prepareStatement(sql5);
                ps4.setLong(1, operationid);
                ps4.setString(2, aif.getProjectName());
                ps4.setString(3, aif.getChildProjectName());
                ps4.setString(4, aif.getMainline());
                ps4.setString(5, aif.getActName());
                ps4.setString(6, aif.getSystem());
                ps4.setString(7, aif.getUserName());
                ps4.executeUpdate();
            }

        } catch (SQLException e)
        {
            log.error("updateExcelModelOpertionId EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:修改excel信息的状态
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void updatePreSuccOpertionId ( ActInfoBean aif, Long operationid, Connection conn ) throws
            SQLException, RepositoryException
    {

        String sql1 = "UPDATE IEAI_ACTPRE T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=?";

        String sql2 = "UPDATE IEAI_ACTSUCC T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=?  ";

        String sql4 = "SELECT T1.ioldoperationid FROM IEAI_DELOPTIONID T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?";

        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        ResultSet rs = null;
        long oldid = 0;
        try
        {

            ps3 = conn.prepareStatement(sql4);
            ps3.setString(1, aif.getProjectName());
            ps3.setString(2, aif.getMainline());
            ps3.setString(3, aif.getActName());
            ps3.setString(4, aif.getSystem());
            ps3.setString(5, aif.getChildProjectName());
            rs = ps3.executeQuery();
            while (rs.next())
            {
                oldid = rs.getLong(CIOLDOPERATIONID);
            }

            if (oldid > 0 &&oldid != operationid)
            {
                ps1 = conn.prepareStatement(sql1);
                ps1.setString(1, aif.getProjectName());
                ps1.setString(2, aif.getMainline());
                ps1.setString(3, aif.getActName());
                ps1.setString(4, aif.getSystem());
                ps1.setString(5, aif.getChildProjectName());
                ps1.setLong(6, operationid);
                ps1.executeUpdate();

                ps2 = conn.prepareStatement(sql2);
                ps2.setString(1, aif.getProjectName());
                ps2.setString(2, aif.getMainline());
                ps2.setString(3, aif.getActName());
                ps2.setString(4, aif.getSystem());
                ps2.setString(5, aif.getChildProjectName());
                ps2.setLong(6, operationid);
                ps2.executeUpdate();
            }


        } catch (SQLException e)
        {
            log.error("updatePreSuccOpertionId EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:删除excel及依赖触发的信息
     * @datea:2014-4-2
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteExcelModel ( String proName, Connection conn ) throws SQLException, RepositoryException
    {
        String sql = "delete from IEAI_EXCELMODEL a where a.IMAINPRONAME=?  and a.IFLAG=2";
        String sql1 = "update IEAI_EXCELMODEL a set a.IFLAG=0 where  a.IMAINPRONAME=? ";
        String sq3 = "delete from ieai_excelmodel_copy a where a.IMAINPRONAME=?  and a.IFLAG=2";
        String sql4 = "update ieai_excelmodel_copy a set a.IFLAG=0 where  a.IMAINPRONAME=? ";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "delete from IEAI_EXCELMODEL where IMAINPRONAME=? and IFLAG=2";
            sq3 = "delete from ieai_excelmodel_copy where IMAINPRONAME=?  and IFLAG=2";
        }
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, proName);
            ps.executeUpdate();
            ps1 = conn.prepareStatement(sql1);
            ps1.setString(1, proName);
            ps1.executeUpdate();
            ps2 = conn.prepareStatement(sq3);
            ps2.setString(1, proName);
            ps2.executeUpdate();
            ps3 = conn.prepareStatement(sql4);
            ps3.setString(1, proName);
            ps3.executeUpdate();
        } catch (SQLException e)
        {
            log.error("deleteExcelModel EXEC DEL IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:删除excel及依赖触发的信息
     * @datea:2014-4-2
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteExcelModel ( String proName, String flowName, Connection conn )
            throws SQLException, RepositoryException
    {
        String sql = "delete from IEAI_EXCELMODEL where IMAINPRONAME=?  and imainlinename=? and IFLAG=2";
        String sql1 = "update IEAI_EXCELMODEL a set a.IFLAG=0 where  a.IMAINPRONAME=? and a.imainlinename=? ";
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, proName);
            ps.setString(2, flowName);
            ps.executeUpdate();
            ps1 = conn.prepareStatement(sql1);
            ps1.setString(1, proName);
            ps1.setString(2, flowName);
            ps1.executeUpdate();
        } catch (SQLException e)
        {
            log.error("deleteExcelModel EXEC flowname DEL IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:删除excel及依赖触发的信息
     * @datea:2014-4-2
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteExcelModelCopy ( String proName, Connection conn ) throws SQLException, RepositoryException
    {
        String sql3 = "delete from IEAI_EXCELMODEL_COPY where IMAINPRONAME=?  and IFLAG=2";
        String sql4 = "update IEAI_EXCELMODEL_COPY a set a.IFLAG=0 where  a.IMAINPRONAME=? ";
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        try
        {
            ps2 = conn.prepareStatement(sql3);
            ps2.setString(1, proName);
            ps2.executeUpdate();
            ps3 = conn.prepareStatement(sql4);
            ps3.setString(1, proName);
            ps3.executeUpdate();

        } catch (SQLException e)
        {
            log.error("deleteExcelModelCopy EXEC DEL IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:删除excel及依赖触发的信息
     * @datea:2014-4-2
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteExcelModelActpreSucc ( Long operatorid, Connection conn )
            throws  SQLException, RepositoryException
    {
        String sql1 = "delete from IEAI_ACTPRE where IOPERATIONID=? ";
        String sql2 = "delete from IEAI_ACTSUCC where IOPERATIONID=?";
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        try
        {
            ps = conn.prepareStatement(sql1);
            ps.setLong(1, operatorid);
            ps.executeUpdate();

            ps1 = conn.prepareStatement(sql2);
            ps1.setLong(1, operatorid);
            ps1.executeUpdate();
        } catch (SQLException e)
        {
            log.error("deleteExcelModelActpreSucc EXEC DEL IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:根据actid删除依赖表中的信息
     * @datea:2014-4-2
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteExcelActPreModel ( Long operatorid, String proName, String mainLineName, String childName,
                                         String actProName, String type, Connection conn ) throws SQLException, RepositoryException
    {

        String sql1 = "";
        if ("copy".equals(type))
        {
            sql1 = "delete from IEAI_ACTPRE_COPY where IPROJECTNAME=? and IMAINLINENAME=? and ICHILDPROJECTNAME=? and IPREACTNAME=? and IOPERATIONID=?";
        } else
        {
            sql1 = "delete from IEAI_ACTPRE where IPROJECTNAME=? and IMAINLINENAME=? and ICHILDPROJECTNAME=? and IPREACTNAME=? and IOPERATIONID=?";
        }
        PreparedStatement ps = null;
        try
        {
            ps = conn.prepareStatement(sql1);
            ps.setString(1, proName);
            ps.setString(2, mainLineName);
            ps.setString(3, childName);
            ps.setString(4, actProName);
            ps.setLong(5, operatorid);
            ps.executeUpdate();
        } catch (SQLException e)
        {
            log.error("deleteExcelActPreModel EXEC DEL IEAI_ACTPRE IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:根据actid删除触发表中的信息
     * @datea:2014-4-2
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteExcelActSuccModel ( Long operid, String proName, String mainLineName, String childName,
                                          String actProName, String type, Connection conn ) throws SQLException, RepositoryException
    {
        String sql2 = "";
        if ("copy".equals(type))
        {
            sql2 = "delete from IEAI_ACTSUCC_COPY where IPROJECTNAME=? and IMAINLINENAME=? and ICHILDPROJECTNAME=? and ISUCCACTNAME=? and IOPERATIONID=? ";
        } else
        {
            sql2 = "delete from IEAI_ACTSUCC where IPROJECTNAME=? and IMAINLINENAME=? and ICHILDPROJECTNAME=? and ISUCCACTNAME=? and IOPERATIONID=? ";
        }
        PreparedStatement ps = null;
        try
        {
            ps = conn.prepareStatement(sql2);
            ps.setString(1, proName);
            ps.setString(2, mainLineName);
            ps.setString(3, childName);
            ps.setString(4, actProName);
            ps.setLong(5, operid);
            ps.executeUpdate();

        } catch (SQLException e)
        {
            log.error("deleteExcelActSuccModel EXEC DEL IEAI_ACTSUCC IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名、子系统名、活动名查询excel的信息
     * @datea:2014-4-2
     * @param projectName
     * @param mainLineName
     * @param childrenName
     * @param actName
     * @throws RepositoryException
     */
    public ActInfoBean selectOneExcelModelManager ( String projectName, String mainLineName, String childrenName,
                                                    String actName, String sysName, String type, Connection conn ) throws  RepositoryException
    {
        ActInfoBean actInfoBean = null;
        String sql = "";
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            if ("copy".equals(type))
            {
                sql = "SELECT IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,ISYSTEM from IEAI_EXCELMODEL_COPY where IMAINPRONAME="
                        + "? and IMAINLINENAME=? and ICHILDPRONAME=? and IACTNAME=? and ISYSTEM=?";
            } else
            {
                sql = "SELECT IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,ISYSTEM from IEAI_EXCELMODEL where IMAINPRONAME="
                        + "? and IMAINLINENAME=? and ICHILDPRONAME=? and IACTNAME=? and ISYSTEM=?";
            }
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, mainLineName);
            ps.setString(3, childrenName);
            ps.setString(4, actName);
            ps.setString(5, sysName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                actInfoBean = new ActInfoBean();
                actInfoBean.setActID(String.valueOf(rset.getLong(IOPERATIONID)));
                actInfoBean.setProjectName(rset.getString(IMAINPRONAME));
                actInfoBean.setMainline(rset.getString(IMAINLINENAME));
                actInfoBean.setSEFlag(String.valueOf(rset.getString(IHEADTAILFLAG)));
                actInfoBean.setAgentGropName(rset.getString(IAGENTSOURCEGROUP));
                actInfoBean.setOKFileABPath(rset.getString(IOKFILEABSOLUTEPATH));
                actInfoBean.setInputParameter(rset.getString(IINPUTPARAM));
                actInfoBean.setChildProjectName(rset.getString(ICHILDPRONAME));
                actInfoBean.setDeleteFlag(String.valueOf(rset.getLong(ICHANGEOPR)));
                actInfoBean.setActName(rset.getString(IACTNAME));
                actInfoBean.setDescribe(rset.getString(IACTDESCRIPTION));
                actInfoBean.setOutputPamaeter(rset.getString(IOUTPUTPARAM));
                actInfoBean.setShellCrust(rset.getString(ISHELLHOUSE));
                actInfoBean.setShellABPath(rset.getString(ISHELLABSOLUTEPATH));
                actInfoBean.setLastLine(rset.getString(ILASTLINE));
                actInfoBean.setSystem(rset.getString(ISYSTEM));
            }

        } catch (SQLException e)
        {
            log.error("selectOneExcelModelManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return actInfoBean;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名、活动名查询excel的信息
     * @datea:2014-4-2
     * @param projectName
     * @param actName
     * @throws RepositoryException
     */
    public ActInfoBean selectOneExcelModelActNameManager ( String actName, Connection conn )
            throws  RepositoryException
    {
        ActInfoBean actInfoBean = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP, "
                    + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE ,ISYSTEM from IEAI_EXCELMODEL where IACTNAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, actName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                actInfoBean = new ActInfoBean();
                actInfoBean.setActID(String.valueOf(rset.getLong(IOPERATIONID)));
                actInfoBean.setProjectName(rset.getString(IMAINPRONAME));
                actInfoBean.setMainline(rset.getString(IMAINLINENAME));
                actInfoBean.setSEFlag(String.valueOf(rset.getString(IHEADTAILFLAG)));
                actInfoBean.setAgentGropName(rset.getString(IAGENTSOURCEGROUP));
                actInfoBean.setOKFileABPath(rset.getString(IOKFILEABSOLUTEPATH));
                actInfoBean.setInputParameter(rset.getString(IINPUTPARAM));
                actInfoBean.setChildProjectName(rset.getString(ICHILDPRONAME));
                actInfoBean.setDeleteFlag(String.valueOf(rset.getLong(ICHANGEOPR)));
                actInfoBean.setActName(rset.getString(IACTNAME));
                actInfoBean.setDescribe(rset.getString(IACTDESCRIPTION));
                actInfoBean.setOutputPamaeter(rset.getString(IOUTPUTPARAM));
                actInfoBean.setShellCrust(rset.getString(ISHELLHOUSE));
                actInfoBean.setShellABPath(rset.getString(ISHELLABSOLUTEPATH));
                actInfoBean.setLastLine(rset.getString(ILASTLINE));
                actInfoBean.setSystem(rset.getString(ISYSTEM));
            }

        } catch (SQLException e)
        {
            log.error("selectOneExcelModelActNameManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return actInfoBean;
    }

    /**
     *
     * @Title: selectOneExcelModelActNameManager
     * @Description: (根据工程名、活动名查询excel信息)
     * @param uploadFile
     * @return ActInfoBean
     * @throws Exception
     * @return String 返回类型
     * @throws RepositoryException
     * @throws @变更记录 2015-8-18 yunpeng_zhang
     */
    public ActInfoBean selectOneExcelModelActNameManager ( String actName, String projectName, Connection conn )
            throws  RepositoryException
    {
        ActInfoBean actInfoBean = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    // + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE ,ISYSTEM from IEAI_EXCELMODEL_COPY where IACTNAME=? AND IMAINPRONAME=? ";
                    + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE ,ISYSTEM from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? AND IACTNAME=?";

            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, actName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                actInfoBean = new ActInfoBean();
                actInfoBean.setActID(String.valueOf(rset.getLong(IOPERATIONID)));
                actInfoBean.setProjectName(rset.getString(IMAINPRONAME));
                actInfoBean.setMainline(rset.getString(IMAINLINENAME));
                actInfoBean.setSEFlag(String.valueOf(rset.getString(IHEADTAILFLAG)));
                actInfoBean.setAgentGropName(rset.getString(IAGENTSOURCEGROUP));
                actInfoBean.setOKFileABPath(rset.getString(IOKFILEABSOLUTEPATH));
                actInfoBean.setInputParameter(rset.getString(IINPUTPARAM));
                actInfoBean.setChildProjectName(rset.getString(ICHILDPRONAME));
                actInfoBean.setDeleteFlag(String.valueOf(rset.getLong(ICHANGEOPR)));
                actInfoBean.setActName(rset.getString(IACTNAME));
                actInfoBean.setDescribe(rset.getString(IACTDESCRIPTION));
                actInfoBean.setOutputPamaeter(rset.getString(IOUTPUTPARAM));
                actInfoBean.setShellCrust(rset.getString(ISHELLHOUSE));
                actInfoBean.setShellABPath(rset.getString(ISHELLABSOLUTEPATH));
                actInfoBean.setLastLine(rset.getString(ILASTLINE));
                actInfoBean.setSystem(rset.getString(ISYSTEM));
            }

        } catch (SQLException e)
        {
            log.error("selectOneExcelModelActNameManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return actInfoBean;
    }

    /**
     *
     * @Title: selectOneExcelModelActNameManager
     * @Description: (根据工程名、活动名查询excel信息)
     * @param uploadFile
     * @return ActInfoBean
     * @throws Exception
     * @return String 返回类型
     * @throws RepositoryException
     * @throws @变更记录 2015-8-18 yunpeng_zhang
     */
    public ActInfoBean selectOneExcelModelSysActNameManager ( String actName, String sysName, Connection conn )
            throws  RepositoryException
    {
        ActInfoBean actInfoBean = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE ,ISYSTEM from IEAI_EXCELMODEL_COPY where IACTNAME=? AND ISYSTEM=? ";
            ps = conn.prepareStatement(sql);
            ps.setString(1, actName);
            ps.setString(2, sysName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                actInfoBean = new ActInfoBean();
                actInfoBean.setActID(String.valueOf(rset.getLong(IOPERATIONID)));
                actInfoBean.setProjectName(rset.getString(IMAINPRONAME));
                actInfoBean.setMainline(rset.getString(IMAINLINENAME));
                actInfoBean.setSEFlag(String.valueOf(rset.getString(IHEADTAILFLAG)));
                actInfoBean.setAgentGropName(rset.getString(IAGENTSOURCEGROUP));
                actInfoBean.setOKFileABPath(rset.getString(IOKFILEABSOLUTEPATH));
                actInfoBean.setInputParameter(rset.getString(IINPUTPARAM));
                actInfoBean.setChildProjectName(rset.getString(ICHILDPRONAME));
                actInfoBean.setDeleteFlag(String.valueOf(rset.getLong(ICHANGEOPR)));
                actInfoBean.setActName(rset.getString(IACTNAME));
                actInfoBean.setDescribe(rset.getString(IACTDESCRIPTION));
                actInfoBean.setOutputPamaeter(rset.getString(IOUTPUTPARAM));
                actInfoBean.setShellCrust(rset.getString(ISHELLHOUSE));
                actInfoBean.setShellABPath(rset.getString(ISHELLABSOLUTEPATH));
                actInfoBean.setLastLine(rset.getString(ILASTLINE));
                actInfoBean.setSystem(rset.getString(ISYSTEM));
            }

        } catch (SQLException e)
        {
            log.error("selectOneExcelModelSysActNameManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return actInfoBean;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名查询excel的信息
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public int selectCountExcelModelMainLineManager ( String projectName, String mainline, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select COUNT(IOPERATIONID) count  from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? and IMAINLINENAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, mainline);
            rset = ps.executeQuery();
            while (rset.next())
            {
                count = rset.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectCountExcelModelMainLineManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名查询excel活动头标记为总头或主线头的记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public List<String> selectCountExcelModelHeadFlagManager ( String projectName, String mainline, Connection conn )
            throws  RepositoryException
    {
        List<String> listActName = new ArrayList<String>();
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IACTNAME  from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? and IMAINLINENAME=? and IHEADTAILFLAG in('1','2')";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, mainline);
            rset = ps.executeQuery();
            while (rset.next())
            {
                listActName.add(rset.getString(IACTNAME));
            }

        } catch (SQLException e)
        {
            log.error("selectCountExcelModelHeadFlagManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return listActName;
    }

    public int[] getPrevWeightAndPriority ( String projectName, String childProjectName, String actName, Connection conn )
            throws  RepositoryException
    {
        int[] res = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select * from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? and ICHILDPRONAME=? and IACTNAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, childProjectName);
            ps.setString(3, actName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                res = new int[2];
                res[0] = rset.getInt("IWEIGHTS");
                res[1] = rset.getInt("IPRIORITY");
            }

        } catch (SQLException e)
        {
            log.error("getPrevWeightAndPriority IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return res;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名查询excel表中是否与子系统名相同的记录
     * @datea:2014-4-2
     * @param projectName
     * @throws RepositoryException
     */
    public int selecExcelModelaProNameManager ( String projectName, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select COUNT(*) count  from IEAI_EXCELMODEL_COPY T where T.ICHILDPRONAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                count = rset.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selecExcelModelaProNameManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名查询excel临时表中是否与子系统名相同的记录
     * @datea:2014-4-2
     * @param projectName
     * @throws RepositoryException
     */
    public int selecTempExcelModelaProNameManager ( String projectName, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select COUNT(*) count  from TMP_IEAI_EXCELMODEL T where T.ICHILDPRONAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                count = rset.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selecTempExcelModelaProNameManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、子系统名查询excel中不同工程下子系统名名相同的记录
     * @datea:2014-4-2
     * @param projectName
     * @param childName
     * @throws RepositoryException
     */
    public int selectNotExcelModelProNameManager ( String proName, String childName, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select COUNT(IOPERATIONID) count  from IEAI_EXCELMODEL_COPY where IMAINPRONAME not in (?) and ICHILDPRONAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, proName);
            ps.setString(2, childName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                count = rset.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectNotExcelModelProNameManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据operationid、工程名、主线名、子系统名、活动名查询依赖表中的信息
     * @datea:2014-4-2
     * @param proId
     * @param projectName
     * @param mainLineName
     * @param childrenName
     * @param actName
     * @throws RepositoryException
     */
    public ActPreBean selectOneActpreManager ( Long proId, String projectName, String mainLineName,
                                               String childrenName, String actName, String type, Connection conn )
            throws  RepositoryException
    {
        ActPreBean actPreBean = null;
        String sql = "";
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            if ("copy".equals(type))
            {
                sql = "select IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME from IEAI_ACTPRE_COPY  where IOPERATIONID="
                        + "?  and IPREACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? and IMAINLINENAME=?";
            } else
            {
                sql = "select IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME from IEAI_ACTPRE  where IOPERATIONID="
                        + "?  and IPREACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? and IMAINLINENAME=?";
            }
            ps = conn.prepareStatement(sql);
            ps.setLong(1, proId);
            ps.setString(2, actName);
            ps.setString(3, childrenName);
            ps.setString(4, projectName);
            ps.setString(5, mainLineName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                actPreBean = new ActPreBean();
                actPreBean.setOperationId(rset.getLong(IOPERATIONID));
                actPreBean.setPreActName(rset.getString("IPREACTNAME"));
                actPreBean.setChildProjectName(rset.getString(ICHILDPROJECTNAME));
                actPreBean.setProjectName(rset.getString(IPROJECTNAME));
                actPreBean.setMainLineName(rset.getString(IMAINLINENAME));
            }

        } catch (SQLException e)
        {
            log.error("selectOneActpreManager EXEC select IEAI_ACTPRE IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return actPreBean;
    }

    /**
     * <AUTHOR>
     * @des:根据operationid、工程名、主线名、子系统名、活动名查询触发表中的信息
     * @datea:2014-4-2
     * @param proId
     * @param projectName
     * @param mainLineName
     * @param childrenName
     * @param actName
     * @throws RepositoryException
     */
    public ActSuccBean selectOneActsuccManager ( Long proId, String projectName, String mainLineName,
                                                 String childrenName, String actName, String type, Connection conn )
            throws  RepositoryException
    {
        ActSuccBean actSuccBean = null;
        String sql = "";
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            if ("copy".equals(type))
            {
                sql = "select IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME from ieai_actsucc_copy   where IOPERATIONID="
                        + "? and ISUCCACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? and IMAINLINENAME=?";
            } else
            {
                sql = "select IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME from ieai_actsucc   where IOPERATIONID="
                        + "? and ISUCCACTNAME=? and ICHILDPROJECTNAME=? and IPROJECTNAME=? and IMAINLINENAME=?";
            }
            ps = conn.prepareStatement(sql);
            ps.setLong(1, proId);
            ps.setString(2, actName);
            ps.setString(3, childrenName);
            ps.setString(4, projectName);
            ps.setString(5, mainLineName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                actSuccBean = new ActSuccBean();
                actSuccBean.setOperationId(rset.getLong(IOPERATIONID));
                actSuccBean.setSuccActName(rset.getString("ISUCCACTNAME"));
                actSuccBean.setChildProjectName(rset.getString(ICHILDPROJECTNAME));
                actSuccBean.setProjectName(rset.getString(IPROJECTNAME));
                actSuccBean.setMainLineName(rset.getString(IMAINLINENAME));
            }

        } catch (SQLException e)
        {
            log.error("selectOneActsuccManager EXEC select ieai_actsucc IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return actSuccBean;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名查询excel表中的信息
     * @datea:2014-4-2
     * @param proName
     * @throws RepositoryException
     */
    public List<ActInfoBean> listExcelModel ( String proName, Connection conn )
            throws  RepositoryException
    {
        List<ActInfoBean> resultList = new ArrayList<ActInfoBean>();
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IOPERATIONID,IFLAG from IEAI_EXCELMODEL where IMAINPRONAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, proName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                ActInfoBean actInfoBean = new ActInfoBean();
                actInfoBean.setActID(String.valueOf(rset.getLong(IOPERATIONID)));
                actInfoBean.setFlag(String.valueOf(rset.getLong("IFLAG")));
                resultList.add(actInfoBean);
            }

        } catch (SQLException e)
        {
            log.error("listExcelModel EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return resultList;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名、头标记为总头或主线头查询excel表中的记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public int selectCountExcelHeadFlag ( String proName, String mainline, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select count(IOPERATIONID) count from ieai_excelmodel_copy t where t.IMAINPRONAME=? and t.imainlinename=? and t.IHEADTAILFLAG in (1,2)";
            ps = conn.prepareStatement(sql);
            ps.setString(1, proName);
            ps.setString(2, mainline);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectCountExcelHeadFlag EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名、活动名、子系统名查询excel的信息
     * @datea:2014-4-2
     * @param projectName
     * @param mainlineName
     * @param actName
     * @param childName
     * @throws RepositoryException
     */
    public ActInfoBean selectOneExcelModelDelManager ( String projectName, String mainlineName, String actName,
                                                       String childName, String sysName, Connection conn ) throws  RepositoryException
    {
        ActInfoBean actInfoBean = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
           /*
            String sql = "select IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE from IEAI_EXCELMODEL_COPY where IMAINPRONAME=?  and ICHILDPRONAME=? and ISYSTEM=? and IMAINLINENAME=?  and IACTNAME=?";
*/
            String sql = "select IOPERATIONID,IMAINLINENAME,IHEADTAILFLAG,IACTNAME from IEAI_EXCELMODEL_COPY where IMAINPRONAME=?  and ICHILDPRONAME=? and ISYSTEM=? and IMAINLINENAME=?  and IACTNAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, childName);
            ps.setString(3, sysName);
            ps.setString(4, mainlineName);
            ps.setString(5, actName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                actInfoBean = new ActInfoBean();
                actInfoBean.setActID(String.valueOf(rset.getLong(IOPERATIONID)));
                actInfoBean.setMainline(rset.getString(IMAINLINENAME));
                actInfoBean.setSEFlag(String.valueOf(rset.getString(IHEADTAILFLAG)));
                actInfoBean.setActName(rset.getString(IACTNAME));
               /* actInfoBean.setActID(String.valueOf(rset.getLong(IOPERATIONID)));
                actInfoBean.setProjectName(rset.getString(IMAINPRONAME));
                actInfoBean.setMainline(rset.getString(IMAINLINENAME));
                actInfoBean.setSEFlag(String.valueOf(rset.getString(IHEADTAILFLAG)));
                actInfoBean.setAgentGropName(rset.getString(IAGENTSOURCEGROUP));
                actInfoBean.setOKFileABPath(rset.getString(IOKFILEABSOLUTEPATH));
                actInfoBean.setInputParameter(rset.getString(IINPUTPARAM));
                actInfoBean.setChildProjectName(rset.getString(ICHILDPRONAME));
                actInfoBean.setDeleteFlag(String.valueOf(rset.getLong(ICHANGEOPR)));
                actInfoBean.setActName(rset.getString(IACTNAME));
                actInfoBean.setDescribe(rset.getString(IACTDESCRIPTION));
                actInfoBean.setOutputPamaeter(rset.getString(IOUTPUTPARAM));
                actInfoBean.setShellCrust(rset.getString(ISHELLHOUSE));
                actInfoBean.setShellABPath(rset.getString(ISHELLABSOLUTEPATH));
                actInfoBean.setLastLine(rset.getString(ILASTLINE));*/
            }

        } catch (SQLException e)
        {
            log.error("selectOneExcelModelDelManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return actInfoBean;
    }

    /**
     * <AUTHOR>
     * @des:根据operationid、工程名、主线名、子系统名、活动名查询触发表中的信息
     * @datea:2014-4-2
     * @param proId
     * @param projectName
     * @param mainLineName
     * @param childrenName
     * @param actName
     * @throws RepositoryException
     */
    public List<ActSuccBean> selectOneActsuccManager ( Long proId, Connection conn )
            throws  RepositoryException
    {
        List<ActSuccBean> listActSuccBean = new ArrayList<ActSuccBean>();
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME from ieai_actsucc_copy   where IOPERATIONID="
                    + "? ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, proId);
            rset = ps.executeQuery();
            while (rset.next())
            {
                ActSuccBean actSuccBean = new ActSuccBean();
                actSuccBean.setOperationId(rset.getLong(IOPERATIONID));
                actSuccBean.setSuccActName(rset.getString("ISUCCACTNAME"));
                actSuccBean.setChildProjectName(rset.getString(ICHILDPROJECTNAME));
                actSuccBean.setProjectName(rset.getString(IPROJECTNAME));
                actSuccBean.setMainLineName(rset.getString(IMAINLINENAME));
                listActSuccBean.add(actSuccBean);
            }

        } catch (SQLException e)
        {
            log.error("selectOneActsuccManager EXEC select ieai_actsucc IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return listActSuccBean;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名、子系统名、活动名查询excel表中的operatorid，在根据operatorid、主线名查询依赖表中是否有记录
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @param childName
     * @param actName
     * @throws RepositoryException
     */
    public int selectCountActpreExcelModelBefore ( String proName, String mainName, String childName, String actName,
                                                   String mainLineName, String beanActName, Connection conn ) throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select count(*) count from IEAI_ACTPRE_COPY t1 where t1.ioperationid=( select t.ioperationid from ieai_excelmodel_copy t where t.imainproname=? and t.imainlinename=? and t.ichildproname=? and t.iactname=?) and t1.imainlinename=? and t1.ipreactname not in (?)";
            ps = conn.prepareStatement(sql);
            ps.setString(1, proName);
            ps.setString(2, mainName);
            ps.setString(3, childName);
            ps.setString(4, actName);
            ps.setString(5, mainLineName);
            ps.setString(6, beanActName);
            rs = ps.executeQuery();

            while (rs.next())
            {
                count = rs.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectListTempExcelModelGroupMainLineManager EXEC select TEMP_IEAI_EXCELMODEL IS ERR"
                    + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据ExcelId、主线名、活动名查询依赖表中是否存在本主线的活动
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @param actName
     * @throws RepositoryException
     */
    public int selectCountDelActpreExcelModel ( Long operid, String mainName, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select count(*) count from ieai_actpre_copy t where t.imainlinename=? and t.ioperationid=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, mainName);
            ps.setLong(2, operid);

            rs = ps.executeQuery();

            while (rs.next())
            {
                count = rs.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectCountDelActpreExcelModel EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名、子系统名、活动名查询excel表中的excelId,根据excleId、主线名查询依赖表中的记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public int selectCountDelActSuccExcelModel ( String proName, String mainName, String childName, String actName,
                                                 String mainLineName, Connection conn ) throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select count(*) count from ieai_actpre_copy t where"
                    + " t.ioperationid=(select t1.ioperationid from ieai_excelmodel_copy t1 where t1.imainproname=? "
                    + "and t1.imainlinename=? and t1.ichildproname=? and t1.iactname=?) " + "and t.imainlinename=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, proName);
            ps.setString(2, mainName);
            ps.setString(3, childName);
            ps.setString(4, actName);
            ps.setString(5, mainLineName);
            rs = ps.executeQuery();

            while (rs.next())
            {
                count = rs.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectCountDelActSuccExcelModel EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:查询数据库seq值
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @param actName
     * @throws RepositoryException
     */
    public long generCountExcelModel (int basicType ) throws  RepositoryException
    {
        long id = 1;
        id = IdGenerator.createIdNoConnection("IEAI_EXCELMODEL_COPY", basicType);
        return id;
    }

    /**
     * <AUTHOR>
     * @des:查询数据库插入IEAI_EXCELMODEL主键
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @param actName
     * @throws RepositoryException
     */
    public long getExcelModelIid ( Connection conn, ActInfoBean aif ) throws  RepositoryException
    {
        long id = 1;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select ioldoperationid  from ieai_deloptionid  where imainproname=? and ICHILDPRONAME=? AND imainlinename=? and iactname=? and isystem=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, aif.getProjectName());
            ps.setString(2, aif.getChildProjectName());
            ps.setString(3, aif.getMainline());
            ps.setString(4, aif.getActName());
            ps.setString(5, aif.getSystem());
            rs = ps.executeQuery();
            if (rs.next())
            {
                id = rs.getLong(CIOLDOPERATIONID);
            }

        } catch (SQLException e)
        {
            log.error("getExcelModelIid EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return id;
    }

    /**
     * <AUTHOR>
     * @des:根据excelid，工程名、子系统名、主线名、活动名查询excel表中的记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public int selectCountExcelModelBefore ( String preactname, String operatorId, Connection conn )
            throws  RepositoryException
    {
        int numm = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT COUNT(*) NUMM FROM  (SELECT '"
                    + preactname
                    + "' NAME FROM IEAI_ACTPRE_COPY) T WHERE EXISTS (SELECT 1 FROM IEAI_ACTPRE_COPY T2 WHERE T.NAME=T2.IPREACTNAME AND IOPERATIONID=?)";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.valueOf(operatorId));
            rs = ps.executeQuery();
            while (rs.next())
            {
                numm = rs.getInt("NUMM");
            }

        } catch (SQLException e)
        {
            log.error("selectCountExcelModelBefore EXEC select TEMP_IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return numm;
    }

    /**
     * <AUTHOR>
     * @des:根据excelId,工程名、子系统名、主线名、活动名查询excel表中的记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public int selectCountExcelModelMainAfter ( String succname, String operatorId, Connection conn )
            throws  RepositoryException
    {
        int numm = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT COUNT(*) NUMM FROM  (SELECT '"
                    + succname
                    + "' NAME FROM IEAI_ACTSUCC_COPY) T WHERE EXISTS (SELECT 1 FROM IEAI_ACTSUCC_COPY T2 WHERE T.NAME=T2.ISUCCACTNAME AND IOPERATIONID=?)";

            ps = conn.prepareStatement(sql);

            ps.setLong(1, Long.valueOf(operatorId));
            rs = ps.executeQuery();

            while (rs.next())
            {
                numm = rs.getInt("NUMM");
            }

        } catch (SQLException e)
        {
            log.error("selectCountExcelModelMainAfter EXEC select TEMP_IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return numm;
    }

    /**
     * <AUTHOR>
     * @des:修改excel信息的状态
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void updatePreSuccExcelModelFlag ( Long operatorid, String type, Connection conn ) throws
            SQLException, RepositoryException
    {

        String sql3 = "";
        if ("copy".equals(type))
        {
            sql3 = "update IEAI_EXCELMODEL_COPY set IFLAG=? where IOPERATIONID=?";
        } else
        {
            sql3 = "update IEAI_EXCELMODEL set IFLAG=? where IOPERATIONID=?";
        }
        PreparedStatement ps = null;
        try
        {
            ps = conn.prepareStatement(sql3);
            ps.setLong(1, 1);
            ps.setLong(2, operatorid);

            ps.executeUpdate();

        } catch (SQLException e)
        {
            log.error("updatePreSuccExcelModelFlag EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:根据活动名删除依赖触发信息
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deletePreSuccExcelModel ( String actName, String projectName, String sysName, String type,
                                          Connection conn ) throws  SQLException, RepositoryException
    {

        String sql4 = "";
        String sql5 = "";
        if ("copy".equals(type))
        {
            sql4 = "DELETE FROM IEAI_ACTSUCC_COPY WHERE ISELFOPERATIONID IN (SELECT T.IOPERATIONID FROM IEAI_EXCELMODEL_COPY T WHERE T.IMAINPRONAME=? AND T.ISYSTEM=? AND T.IACTNAME=?)";
            sql5 = "delete from ieai_actpre_copy WHERE ISELFOPERATIONID IN (SELECT T.IOPERATIONID FROM IEAI_EXCELMODEL_COPY T WHERE T.IMAINPRONAME=? AND T.ISYSTEM=? AND T.IACTNAME=?)";
        } else
        {
            sql4 = "DELETE FROM IEAI_ACTSUCC WHERE ISELFOPERATIONID IN (SELECT T.IOPERATIONID FROM IEAI_EXCELMODEL T WHERE T.IMAINPRONAME=? AND T.ISYSTEM=? AND T.IACTNAME=?)";
            sql5 = "delete from ieai_actpre WHERE ISELFOPERATIONID IN (SELECT T.IOPERATIONID FROM IEAI_EXCELMODEL T WHERE T.IMAINPRONAME=? AND T.ISYSTEM=? AND T.IACTNAME=?)";
        }
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        try
        {
            ps = conn.prepareStatement(sql4);
            ps.setString(1, projectName);
            ps.setString(2, sysName);
            ps.setString(3, actName);
            ps.executeUpdate();
            ps1 = conn.prepareStatement(sql5);
            ps1.setString(1, projectName);
            ps1.setString(2, sysName);
            ps1.setString(3, actName);
            ps1.executeUpdate();

        } catch (SQLException e)
        {
            log.error("deletePreSuccExcelModel EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }

    }

    /**
     * <AUTHOR>
     * @des:根据本工程下是否有正在运行的工作流
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public int selectCountProNameWorkFlowInsatanceManager ( String projectName, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "SELECT COUNT(*) count FROM IEAI_WORKFLOWINSTANCE TI WHERE  TI.ISTATUS IN (0,8) AND TI.IPROJECTNAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            rset = ps.executeQuery();
            while (rset.next())
            {
                count = rset.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectCountProNameWorkFlowInsatanceManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名查询excel活动记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public int selectMainLineCountExcelModelManager ( String projectName, String mainline, Connection conn )
            throws  RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select COUNT(*) count  from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? and IMAINLINENAME=? ";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, mainline);
            rset = ps.executeQuery();
            while (rset.next())
            {
                count = rset.getInt(CCOUNT);
            }

        } catch (SQLException e)
        {
            log.error("selectMainLineCountExcelModelManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名查询excel活动记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public List<ActInfoBean> selectMainLineActNameExcelModelManager ( String projectName, String mainline,
                                                                      Connection conn ) throws  RepositoryException
    {
        List<ActInfoBean> resultList = new ArrayList<ActInfoBean>();
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IACTNAME,IMAINLINENAME  from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? and IMAINLINENAME=? ";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, mainline);
            rset = ps.executeQuery();
            while (rset.next())
            {
                ActInfoBean af = new ActInfoBean();
                af.setActName(rset.getString(IACTNAME));
                af.setMainline(rset.getString(IMAINLINENAME));
                resultList.add(af);
            }

        } catch (SQLException e)
        {
            log.error("selectMainLineActNameExcelModelManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return resultList;
    }

    public int selectMainLineActNameExcelModelManagerCount ( String projectName, String mainline, Connection conn )
            throws  RepositoryException
    {
        int numm = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select COUNT(IOPERATIONID) NUMM  from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? and IMAINLINENAME=? ";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, mainline);
            rset = ps.executeQuery();
            while (rset.next())
            {
                numm = rset.getInt("NUMM");
            }

        } catch (SQLException e)
        {
            log.error("selectMainLineActNameExcelModelManager EXEC select IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return numm;
    }

    /**
     * <AUTHOR>
     * @des:根据工程名、主线名、主线标识查询excel活动记录数
     * @datea:2014-4-2
     * @param projectName
     * @param mainline
     * @throws RepositoryException
     */
    public List<ActInfoBean> selectMainLineActNameSeFlagExcelModelManager ( String projectName, String mainline,
                                                                            Connection conn ) throws  RepositoryException
    {
        List<ActInfoBean> resultList = new ArrayList<ActInfoBean>();
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select IACTNAME,IMAINLINENAME  from IEAI_EXCELMODEL_COPY where IMAINPRONAME=? and IMAINLINENAME=? and IHEADTAILFLAG in (1,2)";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            ps.setString(2, mainline);
            rset = ps.executeQuery();
            while (rset.next())
            {
                ActInfoBean af = new ActInfoBean();
                af.setActName(rset.getString(IACTNAME));
                af.setMainline(rset.getString(IMAINLINENAME));
                resultList.add(af);
            }

        } catch (SQLException e)
        {
            log.error("selectMainLineActNameSeFlagExcelModelManager EXEC select IEAI_EXCELMODEL IS ERR"
                    + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return resultList;
    }

    /**
     * <AUTHOR>
     * @des:删除节点报警信息表数据
     * @datea:2016-3-23
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteNodeWarning ( String mainProName, String childProName, String actName, Connection conn )
            throws  SQLException, RepositoryException
    {

        String sql3 = "DELETE FROM  IEAI_NODE_WARNING where PRJNAME=? AND FLOWNAME=? AND ACTNAME=?";
        PreparedStatement ps = null;
        try
        {
            ps = conn.prepareStatement(sql3);
            ps.setString(1, mainProName);
            ps.setString(2, childProName);
            ps.setString(3, actName);

            ps.executeUpdate();

        } catch (SQLException e)
        {
            log.error("updatePreSuccExcelModelFlag EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    public boolean saveExcelModelRecord ( Connection conn, ActInfoBean actInfoBean, String fileName, String userName,
                                          File file, int basicType ) throws RepositoryException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        long uuid = -1;
        try
        {
            uuid = IdGenerator.createIdNoConnection("IEAI_EXCLEMODEL_RECORD", basicType);
        } catch (RepositoryException e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        boolean flag = true; //

        long curTime = System.currentTimeMillis();
        String inSql = "insert into IEAI_EXCLEMODEL_RECORD(IID,IEXCELNAME,IPROJECTNAME,ISYSNAME,IUPDATETIME,IUPDATEUSER,IUPDATEWIN,IBLOB) values (?,?,?,?,?,?,?,empty_blob())";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            inSql = "INSERT INTO IEAI_EXCLEMODEL_RECORD(IID,IEXCELNAME,IPROJECTNAME,ISYSNAME,IUPDATETIME,IUPDATEUSER,IUPDATEWIN,IBLOB) values (?,?,?,?,?,?,?,?)";
        } else if (JudgeDB.IEAI_DB_TYPE == 2)
        {
            inSql = "INSERT INTO IEAI_EXCLEMODEL_RECORD(IID,IEXCELNAME,IPROJECTNAME,ISYSNAME,IUPDATETIME,IUPDATEUSER,IUPDATEWIN,IBLOB) values (?,?,?,?,?,?,?,?)";
        }
        String seaSql = "SELECT IBLOB FROM IEAI_EXCLEMODEL_RECORD where IID = ?";
        try
        {
            pres = conn.prepareStatement(inSql);
            pres.setLong(1, uuid);
            pres.setString(2, fileName);
            pres.setString(3, actInfoBean.getProjectName());
            pres.setString(4, actInfoBean.getSystem());
            pres.setLong(5, curTime);
            pres.setString(6, userName);
            // 变更窗口暂时为空
            pres.setString(7, "");

            if (JudgeDB.IEAI_DB_TYPE == 3 || JudgeDB.IEAI_DB_TYPE == 2)
            {
                // mysql 和 db2数据库写入blob流到数据库表中
                byte[] buffer = null;
                try
                {
                    FileInputStream fis = new FileInputStream(file);
                    ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
                    byte[] b = new byte[1000];
                    int n;
                    while ((n = fis.read(b)) != -1)
                    {
                        bos.write(b, 0, n);
                    }
                    fis.close();
                    bos.close();
                    buffer = bos.toByteArray();
                } catch (FileNotFoundException e)
                {
                    log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                } catch (IOException e)
                {
                    log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                }
                pres.setBytes(8, buffer);

            }
            pres.executeUpdate();

            pres = conn.prepareStatement(seaSql);
            pres.setLong(1, uuid);
            rs = pres.executeQuery();
            OutputStream out = null;
            if (rs.next())
            {
                Blob blob =  rs.getBlob(1);
                if (JudgeDB.IEAI_DB_TYPE == 1)
                {
                    out = ((oracle.sql.BLOB) blob).getBinaryOutputStream();
                }
                else
                {
                    out = new ByteArrayOutputStream();
                }

                byte[] b = new byte[64 * 1024];
                int len = 0;
                InputStream fin = null;
                try
                {
                    fin = new FileInputStream(file);
                    while ((len = fin.read(b)) != -1)
                        out.write(b, 0, len);
                    fin.close();
                    out.flush();
                    out.close();
                } catch (FileNotFoundException e)
                {
                    flag = false;
                    log.error("saveExcelModelRecord EXEC save IEAI_EXCLEMODEL_RECORD IS ERR", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } catch (IOException e)
                {
                    flag = false;
                    log.error("saveExcelModelRecord EXEC save IEAI_EXCLEMODEL_RECORD IS ERR", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    try
                    {
                        if(fin != null){

                            fin.close();
                        }
                    } catch (IOException e)
                    {
                        log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                    }
                }

            }

        } catch (SQLException e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        } finally
        {
            DBResource.closePSRS(rs, pres, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return flag;
    }

    public boolean saveUpdatePriRecord ( Connection conn, ProjectSaveUtilBean projectSaveUtilBean , Map<String, ActInfoBean> mapList, String filename,
                                         String userName, int basicType ) throws RepositoryException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;

        PreparedStatement pres_fullName = null;
        ResultSet rs_fullName = null;
        String fullName="";

        boolean flag = true; //

        String insertSql = " INSERT INTO IEAI_UPDATEPRIRECORD (IID,IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, IPRIORITY, ISYSTEM, UPDATETIME, UPDATEUSER,IPRIORITYOLD) VALUES (?,?,?,?,?,?,?,?,(SELECT U.IFULLNAME from IEAI_USER U WHERE U.ILOGINNAME = ?),(SELECT IPRIORITY FROM (SELECT  T.IPRIORITY FROM IEAI_UPDATEPRIRECORD T   WHERE T.IMAINPRONAME = ? AND T.IACTNAME = ? AND T.IMAINLINENAME = ? ORDER BY T.UPDATETIME DESC ) WHERE ROWNUM=1 ))";

        if (JudgeDB.IEAI_DB_TYPE == 2)
        {
            insertSql = " INSERT INTO IEAI_UPDATEPRIRECORD (IID,IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, IPRIORITY, ISYSTEM, UPDATETIME, UPDATEUSER,IPRIORITYOLD) VALUES (?,?,?,?,?,?,?,?,(SELECT U.IFULLNAME from IEAI_USER U WHERE U.ILOGINNAME = ?),(SELECT IPRIORITY FROM ( SELECT IPRIORITY,RN FROM ( SELECT IPRIORITY, ROW_NUMBER() OVER() AS RN FROM (SELECT  T.IPRIORITY FROM IEAI_UPDATEPRIRECORD T   WHERE T.IMAINPRONAME = ? AND T.IACTNAME = ? AND T.IMAINLINENAME = ? ORDER BY T.UPDATETIME DESC ) ) TT WHERE TT.RN = 1 )))";
        } else if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            insertSql = " INSERT INTO IEAI_UPDATEPRIRECORD (IID,IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, IPRIORITY, ISYSTEM, UPDATETIME, UPDATEUSER,IPRIORITYOLD) VALUES (?,?,?,?,?,?,?,?,?,(SELECT T.IPRIORITY FROM IEAI_UPDATEPRIRECORD T WHERE T.IMAINPRONAME = ?  AND T.IMAINLINENAME = ? AND T.IACTNAME = ? ORDER BY T.UPDATETIME DESC LIMIT 1))";

            //  insertSql = " INSERT INTO IEAI_UPDATEPRIRECORD (IID,IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, IPRIORITY, ISYSTEM, UPDATETIME, UPDATEUSER,IPRIORITYOLD) VALUES (?,?,?,?,?,?,?,?,(SELECT U.IFULLNAME from IEAI_USER U WHERE U.ILOGINNAME = ?),(SELECT T.IPRIORITY FROM IEAI_UPDATEPRIRECORD T WHERE T.IMAINPRONAME = ? AND T.IACTNAME = ? AND T.IMAINLINENAME = ? ORDER BY T.UPDATETIME DESC LIMIT 1))";
        }

        try
        {



            String fullName_sql="SELECT U.IFULLNAME from IEAI_USER U WHERE U.ILOGINNAME = ?";


            pres_fullName = conn.prepareStatement(fullName_sql);
            pres_fullName.setString(1, userName);
            rs_fullName=pres_fullName.executeQuery();

            while(rs_fullName.next()){
                fullName=rs_fullName.getString("IFULLNAME");
            }



            pres = conn.prepareStatement(insertSql);

            int index=0;
            for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet())
            {

                index++;
                long iid = IdGenerator.createIdNoConnection("IEAI_UPDATEPRIRECORD", basicType);
                ActInfoBean actInfoBean = entry.getValue();
                // 从数据库中读取系统时间的毫秒时间戳
                long sysTimeMillins = getSysTimeMillins(basicType);
                pres.setLong(1, iid);
                pres.setString(2, filename);
                pres.setString(3, actInfoBean.getMainline());
                pres.setString(4, actInfoBean.getChildProjectName());
                pres.setString(5, actInfoBean.getActName());
                pres.setString(6, actInfoBean.getPriority());
                pres.setString(7, actInfoBean.getSystem());
                pres.setLong(8, sysTimeMillins);
                pres.setString(9, fullName);



                pres.setString(10, filename);

                pres.setString(11, actInfoBean.getMainline());
                pres.setString(12, actInfoBean.getActName());

                // 多写时，存储数据ieai_updateprirecord表信息
                actInfoBean.setPriRecordId(iid);
                actInfoBean.setMainProName(filename);
                actInfoBean.setFullUserName(userName);
                actInfoBean.setSysTimeMillins(sysTimeMillins);
                projectSaveUtilBean.getInsertPriRecordList().add(actInfoBean);

                pres.addBatch();
                if(index%1000==0){
                    pres.executeBatch();
                }
            }
            pres.executeBatch();


        } catch (SQLException e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, pres, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return flag;
    }

    /**
     * @Title: getSysTimeMillins
     * @Description: 从数据库中读取优先级表的系统时间作为上传时间的方法
     * @return
     * @throws RepositoryException
     * @author: yue_sun
     * @date:   2018年8月14日 下午4:35:34
     */
    public long getSysTimeMillins ( int basicType ) throws RepositoryException
    {
        long sysTimeMillins = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                String sql = "SELECT FUN_GET_DATE_NUMBER(current_timestamp,0) as updatetime FROM dual ";

                if (JudgeDB.IEAI_DB_TYPE == 2)
                {
                    sql = "SELECT FUN_GET_DATE_NUMBER_NEW(current_timestamp,8) as updatetime FROM sysibm.sysdummy1";
                }
                try
                {
                    con = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                            log, basicType);
                    ps = con.prepareStatement(sql);
                    rs = ps.executeQuery();
                    if (rs.next())
                    {
                        sysTimeMillins = rs.getLong("updatetime");
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return sysTimeMillins;
    }

    public boolean startWorkFlowUploadExcel ( Connection conn, long iid, String projectName,
                                              String flowName, int basicType ) throws RepositoryException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        String deleteFlaginside = "";
        String deleteLag = "";
        Map<String, ActInfoBean> mapList = new HashMap<String, ActInfoBean>();
        boolean flag = true; //
        long startTime = System.currentTimeMillis();
        String sql = "SELECT IID, IACTNO, IMAINPRONAME, ISYSNAME, ICHILDPRONAME, IACTNAME, IACTDESCRIPTION, ISDELETE, IOKFILEABSOLUTEPATH, IOKFILEFINDWEEK, ISHELLHOUSE, ISHELLABSOLUTEPATH, IOUTPUTPARAM, IBUSINEXPAUTO, IAGENTSOURCEGROUP, IAGENTCHECKGROUP, IPRI, IWEIGHT, APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, IMAINLINENAME, IHEADFLAG, ISENBLE,IACTPRENO,IACTSUCCNO,EXCELPATH,USERNAME,USERID,EXCELPATHNAME,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING  FROM IEAI_BASIC_EXCELMODEL T WHERE T.IID=? AND T.IMAINPRONAME=?";
        try
        {
            long starttimequery = System.currentTimeMillis();
            pres = conn.prepareStatement(sql);
            pres.setLong(1, iid);
            pres.setString(2, projectName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ActInfoBean actInfoBean = new ActInfoBean();
                actInfoBean.setActNo(rs.getString("IACTNO"));
                actInfoBean.setProjectName(rs.getString(IMAINPRONAME));
                actInfoBean.setSystem(rs.getString("ISYSNAME"));
                actInfoBean.setChildProjectName(rs.getString(ICHILDPRONAME));
                actInfoBean.setActName(rs.getString(IACTNAME));
                actInfoBean.setDescribe(rs.getString(IACTDESCRIPTION) == null ? "" : rs.getString(IACTDESCRIPTION));
                if ("2".equals(rs.getString("ISDELETE")))
                {
                    if (flowName.equals(rs.getString(IMAINLINENAME)))
                    {
                        deleteFlaginside = "2";
                    } else
                    {
                        if (!"2".equals(deleteFlaginside))
                        {
                            deleteFlaginside = "3";
                        }
                    }

                }
                actInfoBean.setDeleteFlag(rs.getString("ISDELETE"));
                actInfoBean.setOKFileABPath(rs.getString(IOKFILEABSOLUTEPATH) == null ? "" : rs
                        .getString(IOKFILEABSOLUTEPATH));
                actInfoBean.setOKFileFindWeek(rs.getString("IOKFILEFINDWEEK") == null ? "" : rs
                        .getString("IOKFILEFINDWEEK"));
                actInfoBean.setShellCrust(rs.getString(ISHELLHOUSE) == null ? "" : rs.getString(ISHELLHOUSE));
                actInfoBean.setShellABPath(rs.getString(ISHELLABSOLUTEPATH) == null ? "" : rs
                        .getString(ISHELLABSOLUTEPATH));
                actInfoBean.setOutputPamaeter(rs.getString(IOUTPUTPARAM) == null ? "" : rs.getString(IOUTPUTPARAM));
                actInfoBean.setRedo(rs.getString("IBUSINEXPAUTO"));
                actInfoBean.setAgentGropName(rs.getString(IAGENTSOURCEGROUP) == null ? "" : rs
                        .getString(IAGENTSOURCEGROUP));
                actInfoBean.setCheckAgentGropName(rs.getString("IAGENTCHECKGROUP") == null ? "" : rs
                        .getString("IAGENTCHECKGROUP"));
                actInfoBean.setPriority(rs.getString("IPRI"));
                actInfoBean.setWeights(rs.getString("IWEIGHT"));
                actInfoBean.setAptGroupName(rs.getString("APTGROUPNAME") == null ? "" : rs.getString("APTGROUPNAME"));
                actInfoBean.setAptFileName(rs.getString("APTFILENAME") == null ? "" : rs.getString("APTFILENAME"));
                actInfoBean.setIsDB2(rs.getString("ISDB2"));
                actInfoBean.setDb2IP(rs.getString("DB2IP") == null ? "" : rs.getString("DB2IP"));
                actInfoBean.setMainline(rs.getString(IMAINLINENAME));
                actInfoBean.setSEFlag(rs.getString("IHEADFLAG") == null ? "" : rs.getString("IHEADFLAG"));
                actInfoBean.setDisableFlag(rs.getString("ISENBLE"));

                try
                {
                    actInfoBean.setBeforeActList(changeStringtoList(clobToString(rs.getClob("IACTPRENO")), ","));
                    actInfoBean.setAfterActList(changeStringtoList(clobToString(rs.getClob("IACTSUCCNO")), ","));
                } catch (IOException e)
                {
                    log.info("startWorkFlowUploadExcel is set pre error:" + e.getMessage());
                }
                actInfoBean.setExcelPath(rs.getString("EXCELPATH"));
                actInfoBean.setUserName(rs.getString("USERNAME"));
                actInfoBean.setUserId(rs.getLong("USERID"));
                actInfoBean.setTestRealPath(rs.getString("EXCELPATHNAME"));
                actInfoBean.setAgentGroup(rs.getInt("ISAGENTGROUP") == 0);
                actInfoBean.setDelayTime(rs.getString("IDELAYTIME"));
                actInfoBean.setBranchCondition(rs.getString("IBRANCHCONDITION"));
                actInfoBean.setReTryCount(rs.getString("IRETRYNUM"));
                actInfoBean.setCalendName(rs.getString("ICALENDNAME"));
                actInfoBean.setReTryTime(rs.getString("IRETRYTIME"));
                actInfoBean.setReTryEndTime(rs.getString("IRETRYENDTIME"));
                actInfoBean.setSkip(rs.getString("IAUTOSKIP"));
                actInfoBean.setDelayWarnning(rs.getString("IDELAYWARNNING"));
                mapList.put(actInfoBean.getActNo(), actInfoBean);
            }
            if ("2".equals(deleteFlaginside))
            {
                deleteLag = "del";
            }
            if ("3".equals(deleteFlaginside))
            {
                return flag;
            }
            log.info("启动工作流导入查询导入数据总耗时为：" + ((System.currentTimeMillis()) - starttimequery) + DESCPROJECTNAME
                    + projectName + DESCFLOWNAME + flowName);
            // fileName
            ActInfoBean actInfoBean = new ActInfoBean();
            for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet())
            {
                actInfoBean = entry.getValue();
                break;
            }

            long timerB = 0;
            long timerE = 0;
            if (deleteLag.equals("del"))
            {
                timerB = System.currentTimeMillis();
                ActMutexConfigManager.getInstance().deleteMutexAtImport(conn, mapList);
                timerE = System.currentTimeMillis();
                log.info("删除互斥关系表数据共用时：" + (timerE - timerB) + "毫秒");
            }
            // 保存校验完的数据到excel表中
            long startUploadExcel = System.currentTimeMillis();
            importExcelModelManager(mapList, actInfoBean.getProjectName(), flowName, deleteLag,
                    actInfoBean.getUserName(), "notcopy", conn, basicType);
            log.info("启动工作流导入importExcelModelManager为：" + ((System.currentTimeMillis()) - startUploadExcel)
                    + DESCPROJECTNAME + projectName + DESCFLOWNAME + flowName);
            log.info("启动工作流导入为IEAI_ACTSUCC和IEAI_ACTPRE表中的ISELFOPERATIONID字段赋值.共用时：" + (timerB - timerE) + "毫秒");
            long endUploadExcel = System.currentTimeMillis() - startUploadExcel;
            log.info("Excel导入功能耗时流水：Excel导入成功耗时为：" + endUploadExcel + " 毫秒");
            log.info("Excel导入功能耗时流水：Excel导入成功, 操作人 " + actInfoBean.getUserName());
            if (!deleteLag.equals("del"))
            {
                timerB = System.currentTimeMillis();
                ActMutexConfigManager.getInstance().saveMutexMainAtImport(conn, mapList, actInfoBean.getUserId());
                timerE = System.currentTimeMillis();
                log.info("修改互斥关系表数据共用时：" + (timerE - timerB) + "毫秒");
            }
            if (mapList.size() > 0)
            {
                timerB = System.currentTimeMillis();
                ActMutexConfigManager.getInstance().saveSystemGroup(conn, mapList);
                timerE = System.currentTimeMillis();
                log.info("插入IEAI_SYSTEM_GROUP表数据共用时：" + (timerE - timerB) + "毫秒");
            }
            long endTime = System.currentTimeMillis();

            log.info("***************上传结束******************共用时：" + (endTime - startTime) + "毫秒");
        } catch (Exception e)
        {
            log.info("startWorkFlowUploadExcel is save excelmodel error" + e.getMessage());
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, pres, "deletePreSuccExcelModel", log);
        }
        return flag;
    }

    public boolean saveExcelVersionToTable(List<ActInfoVersionBean> actInfoVersionBeans, String proName, String shellCust) throws RepositoryException {
        boolean res = false;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement psUpdate = null;

        ResultSet rs = null;
        Connection conn = null;
        String sql = "INSERT INTO IEAI_EXCELNODEL_VERSION(IID,IPRJNAME,IVERSION,ISHELLCRUST) "
                + "VALUES (?,?,?,?)";

        String sql1 = "INSERT INTO IEAI_EXCELNODEL_VERSION_DETAIL(IID,IVERSIONID,ICONTENT) "
                + "VALUES (?,?,?)";

        String sqlUpdate = "select a.icontent from IEAI_EXCELNODEL_VERSION_DETAIL a  where a.iid = ? for update ";
        try {
            conn = DBResource.getJDBCConnection("saveExcelVersionToTable", log, Constants.IEAI_IEAI);
            long iid = IdGenerator.createIdNoConnection("IEAI_EXCELNODEL_VERSION", Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setString(2, proName);
            ps.setString(3, DateUtil.getStringNowTime());
            ps.setString(4, shellCust);
            ps.executeUpdate();


            ByteArrayOutputStream byt = new ByteArrayOutputStream();
            ObjectOutputStream obj = null;
            byte[] bytes = null;
            try {
                obj = new ObjectOutputStream(byt);
                obj.writeObject(actInfoVersionBeans);
                bytes = byt.toByteArray();
            } catch (IOException e) {
                e.printStackTrace();
            }

            Blob content = Hibernate.createBlob(bytes);

            long detailIid = IdGenerator.createIdNoConnection("IEAI_EXCELNODEL_VERSION_DETAIL", Constants.IEAI_IEAI);
            ps1 = conn.prepareStatement(sql1);
            ps1.setLong(1, detailIid);
            ps1.setLong(2, iid);
            if (JudgeDB.IEAI_DB_TYPE == 1)
            {
                ps1.setBlob(3, BLOB.empty_lob());
            } else if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                ps1.setBinaryStream(3, content.getBinaryStream(),
                        content.getBinaryStream().available());
            } else
            {
                ps1.setBlob(3, content);
            }

            ps1.executeUpdate();

            psUpdate = conn.prepareStatement(sqlUpdate);
            psUpdate.setLong(1, detailIid);

            rs = psUpdate.executeQuery();

            OutputStream blobOutputStream = null;
            while (rs.next())
            {
                Blob blob = (Blob) rs.getBlob("ICONTENT");
                if (JudgeDB.IEAI_DB_TYPE == 1)
                {
                    blobOutputStream = ((BLOB) blob).getBinaryOutputStream();
                } else
                {
                    blobOutputStream = blob.setBinaryStream(1);
                }

                try
                {
                    FileCopyUtils.copy(content.getBinaryStream(), blobOutputStream);
                } catch (IOException e)
                {
                    log.info(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
            if (blobOutputStream != null){
                blobOutputStream.close();
            }

            conn.commit();
            res = true;
        } catch (Exception e) {
            res = false;
            log.error("saveExcelVersionToTable is error ! ",e);
        } finally {
            DBResource.closePreparedStatement(ps,"saveExcelVersionToTable",log);
            DBResource.closeConnection(conn, "saveExcelVersionToTable", log);
        }
        return res;
    }
    public boolean saveExcelVersionToTable(List<ActInfoVersionBean> actInfoVersionBeans, String proName, String userName, String shellCust) throws RepositoryException {
        boolean res = false;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement psUpdate = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ResultSet rs = null;
        Connection conn = null;
        String sql = "INSERT INTO IEAI_EXCELNODEL_VERSION(IID,IPRJNAME,IVERSION,ISHELLCRUST,IUPLOADTIME,IUPLOADUSER) "
                + "VALUES (?,?,?,?,?,?)";

        String sql1 = "INSERT INTO IEAI_EXCELNODEL_VERSION_DETAIL(IID,IVERSIONID,ICONTENT) "
                + "VALUES (?,?,?)";

        String sqlUpdate = "select a.icontent from IEAI_EXCELNODEL_VERSION_DETAIL a  where a.iid = ? for update ";
        try {
            conn = DBResource.getJDBCConnection("saveExcelVersionToTable", log, Constants.IEAI_IEAI);
            long iid = IdGenerator.createIdNoConnection("IEAI_EXCELNODEL_VERSION", Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setString(2, proName);
            ps.setString(3, DateUtil.getStringNowTime());
            ps.setString(4, shellCust);
            ps.setString(5, LocalDateTime.now().format(formatter));
            ps.setString(6, userName);
            ps.executeUpdate();


            ByteArrayOutputStream byt = new ByteArrayOutputStream();
            ObjectOutputStream obj = null;
            byte[] bytes = null;
            try {
                obj = new ObjectOutputStream(byt);
                obj.writeObject(actInfoVersionBeans);
                bytes = byt.toByteArray();
            } catch (IOException e) {
                e.printStackTrace();
            }

            Blob content = Hibernate.createBlob(bytes);

            long detailIid = IdGenerator.createIdNoConnection("IEAI_EXCELNODEL_VERSION_DETAIL", Constants.IEAI_IEAI);
            ps1 = conn.prepareStatement(sql1);
            ps1.setLong(1, detailIid);
            ps1.setLong(2, iid);
            if (DBManager.Orcl_Faimily())
            {
                ps1.setBlob(3, BLOB.empty_lob());
            } else if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                ps1.setBinaryStream(3, content.getBinaryStream(),
                        content.getBinaryStream().available());
            } else
            {
                ps1.setBlob(3, content);
            }

            ps1.executeUpdate();

            psUpdate = conn.prepareStatement(sqlUpdate);
            psUpdate.setLong(1, detailIid);

            rs = psUpdate.executeQuery();

            OutputStream blobOutputStream = null;
            while (rs.next())
            {
                Blob blob = (Blob) rs.getBlob("ICONTENT");
                if (DBManager.Orcl_Faimily())
                {
                    blobOutputStream = ((oracle.sql.BLOB) blob).getBinaryOutputStream();
                } else
                {
                    blobOutputStream = blob.setBinaryStream(1);
                }

                try
                {
                    FileCopyUtils.copy(content.getBinaryStream(), blobOutputStream);
                } catch (IOException e)
                {
                    log.info(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
            if (blobOutputStream != null){
                blobOutputStream.close();
            }

            conn.commit();
            res = true;
        } catch (Exception e) {
            res = false;
            log.error("saveExcelVersionToTable is error ! ",e);
        } finally {
            DBResource.closePreparedStatement(ps1,"saveExcelVersionToTable",log);
            DBResource.closePreparedStatement(psUpdate,"saveExcelVersionToTable",log);
            DBResource.closePSRS(rs, ps, "saveExcelVersionToTable", log);
            DBResource.closeConnection(conn, "saveExcelVersionToTable", log);
        }
        return res;
    }


    public boolean uploadProjectExcel ( Connection conn, String projectName, String flowName, UserInfo userinfo,
                                        ProjectSaveUtilBean projectSaveUtilBean, int basicType ) throws RepositoryException
    {
        long startTime = System.currentTimeMillis();
        boolean flag = true; //
        try
        {
            StartCreatePrj oo = new StartCreatePrj();
            Object[] object = oo.getProjectInfo(projectName, conn);
            boolean fag = TaskUploadManager.getInstance().saveStartflowEndFlowUploadExcel(object, userinfo,
                    projectSaveUtilBean, conn, basicType);
            if (!fag)
            {
                log.info("uploadProjectExcel is saveProject is error: " + "组织工程信息入库失败,请重新导入。");
                flag = false;
            }
            long endTime = System.currentTimeMillis();
            log.info("启动工作流导入工程***************上传结束*****************共用时：" + (endTime - startTime) + "毫秒"
                    + DESCPROJECTNAME + projectName + DESCFLOWNAME + flowName);

        } catch (Exception e)
        {
            log.info("uploadProjectExcel is save excelmodel error" + e.getMessage());
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        }
        return flag;
    }


    public boolean uploadProjectExcel ( Connection conn, String projectName, String flowName, UserInfo userinfo,
                                        ProjectSaveUtilBean projectSaveUtilBean, int basicType,String  paramIsRequired) throws RepositoryException
    {
        long startTime = System.currentTimeMillis();
        boolean flag = true; //
        try
        {
            StartCreatePrj oo = new StartCreatePrj();
            Object[] object = oo.getProjectInfo(projectName, conn);
            boolean fag = TaskUploadManager.getInstance().saveStartflowEndFlowUploadExcel(object, userinfo,
                    projectSaveUtilBean, conn, basicType,paramIsRequired);
            if (!fag)
            {
                log.info("uploadProjectExcel is saveProject is error: " + "组织工程信息入库失败,请重新导入。");
                flag = false;
            }
            long endTime = System.currentTimeMillis();
            log.info("启动工作流导入工程***************上传结束*****************共用时：" + (endTime - startTime) + "毫秒"
                    + DESCPROJECTNAME + projectName + DESCFLOWNAME + flowName);

        } catch (Exception e)
        {
            log.info("uploadProjectExcel is save excelmodel error" + e.getMessage());
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        }
        return flag;
    }

    public boolean saveCopyExcelModelUploadExcel ( Connection conn,
                                                   Map<String, ActInfoBean> mapList, String excelPath, String userName, Long userId, String testRealPath,
                                                   Long iid ) throws RepositoryException
    {

        PreparedStatement pres = null;
        ResultSet rs = null;
        boolean flag = true; //
        String sql = "INSERT INTO IEAI_BASIC_EXCELMODEL (IID, IACTNO, IMAINPRONAME, ISYSNAME, ICHILDPRONAME, IACTNAME, IACTDESCRIPTION, ISDELETE, IOKFILEABSOLUTEPATH, IOKFILEFINDWEEK, ISHELLHOUSE, ISHELLABSOLUTEPATH, IOUTPUTPARAM, IBUSINEXPAUTO, IAGENTSOURCEGROUP, IAGENTCHECKGROUP, IPRI, IWEIGHT, APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, IMAINLINENAME, IHEADFLAG, ISENBLE, IACTPRENO, IACTSUCCNO,EXCELPATH,USERNAME,USERID,EXCELPATHNAME,ISTATE,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING)"
                + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";


        int index=0;
        try
        {

            pres = conn.prepareStatement(sql);
            for (ActInfoBean aif : mapList.values())
            {
                if (DBManager.Orcl_Faimily())
                {
                    pres.setLong(1, iid);
                    pres.setLong(2, Long.valueOf(aif.getActNo()));
                    pres.setString(3, aif.getProjectName());
                    pres.setString(4, aif.getSystem());
                    pres.setString(5, aif.getChildProjectName());
                    pres.setString(6, aif.getActName());
                    pres.setString(7, aif.getDescribe());
                    pres.setString(8, aif.getDeleteFlag());
                    pres.setString(9, aif.getOKFileABPath());
                    pres.setString(10, aif.getOKFileFindWeek());
                    pres.setString(11, aif.getShellCrust());
                    pres.setString(12, aif.getShellABPath());
                    pres.setString(13, aif.getOutputPamaeter());
                    pres.setString(14, aif.getRedo());
                    pres.setString(15,
                            StringUtils.isBlank(aif.getAgentInfo()) ? aif.getAgentGropName() : aif.getAgentInfo());
                    pres.setString(16, aif.getCheckAgentGropName());
                    pres.setLong(17, Long.valueOf(aif.getPriority()));
                    pres.setLong(18, Long.valueOf(aif.getWeights()));
                    pres.setString(19, aif.getAptGroupName());
                    pres.setString(20, aif.getAptFileName());
                    pres.setString(21, aif.getIsDB2());
                    pres.setString(22, aif.getDb2IP());
                    pres.setString(23, aif.getMainline());
                    pres.setString(24, aif.getSEFlag());
                    pres.setString(25, aif.getDisableFlag());

                    String iactpreno = "";
                    if (DBManager.Orcl_Faimily())
                    {
                        pres.setString(26, "");
                    } else
                    {
                        InputStream fis1 = null;
                        try
                        {
                            if (StringUtils.isNotBlank(iactpreno))
                            {
                                fis1 = new ByteArrayInputStream(iactpreno.getBytes(Constants.CHARSET_UTF_8));
                                pres.setAsciiStream(26, fis1, iactpreno.getBytes(Constants.CHARSET_UTF_8).length);
                            } else
                            {
                                pres.setAsciiStream(26, null, 0);
                            }

                        } catch (UnsupportedEncodingException e)
                        {
                            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                        } finally
                        {
                            closeInputStream(fis1);
                        }

                    }

                    String iactsuccno = "";
                    if (DBManager.Orcl_Faimily())
                    {
                        pres.setString(27, "");
                    } else
                    {
                        InputStream fis1 = null;
                        try
                        {
                            if (StringUtils.isNotBlank(iactsuccno))
                            {
                                fis1 = new ByteArrayInputStream(iactsuccno.getBytes(Constants.CHARSET_UTF_8));
                                pres.setAsciiStream(27, fis1, iactsuccno.getBytes(Constants.CHARSET_UTF_8).length);
                            } else
                            {
                                pres.setAsciiStream(27, null, 0);
                            }

                        } catch (UnsupportedEncodingException e)
                        {
                            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                        } finally
                        {
                            closeInputStream(fis1);
                        }

                    }
                    pres.setString(28, excelPath);
                    pres.setString(29, userName);
                    pres.setLong(30, userId);
                    pres.setString(31, testRealPath);
                    pres.setInt(32, 0);
                    pres.setInt(33, aif.isAgentGroup() ? 0 : 1);
                    pres.setString(34, aif.getDelayTime());
                    pres.setString(35, aif.getBranchCondition());
                    if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount())
                            && !"null".equals(aif.getReTryCount()))
                    {
                        pres.setInt(36, Integer.parseInt(aif.getReTryCount()));//

                    } else
                    {
                        pres.setInt(36, 0);// ,IRETRYNUM,ICALENDNAME
                    }
                    pres.setString(37, aif.getCalendName());
                    pres.setString(38, aif.getReTryTime());
                    pres.setString(39, aif.getReTryEndTime());
                    pres.setString(40, aif.getSkip());
                    pres.setString(41, aif.getDelayWarnning());
                } else
                {
                    pres.setLong(1, iid);
                    pres.setLong(2, Long.valueOf(aif.getActNo()));
                    pres.setString(3,  "".equals(aif.getProjectName()) ? null : aif.getProjectName());
                    pres.setString(4, "".equals(aif.getSystem()) ? null : aif.getSystem());
                    pres.setString(5, "".equals(aif.getChildProjectName()) ? null : aif.getChildProjectName());
                    pres.setString(6, "".equals(aif.getActName()) ? null : aif.getActName());
                    pres.setString(7, "".equals(aif.getDescribe()) ? null : aif.getDescribe());
                    pres.setString(8, "".equals(aif.getDeleteFlag()) ? null : aif.getDeleteFlag());
                    pres.setString(9,  "".equals(aif.getOKFileABPath()) ? null : aif.getOKFileABPath());
                    pres.setString(10,  "".equals(aif.getOKFileFindWeek()) ? null : aif.getOKFileFindWeek());
                    pres.setString(11,  "".equals(aif.getShellCrust()) ? null : aif.getShellCrust());
                    pres.setString(12, "".equals(aif.getShellABPath()) ? null : aif.getShellABPath());
                    pres.setString(13, "".equals(aif.getOutputPamaeter()) ? null : aif.getOutputPamaeter());
                    pres.setString(14,  "".equals(aif.getRedo()) ? null : aif.getRedo());
                    pres.setString(15, StringUtils.isBlank(aif.getAgentInfo()) ? ( "".equals(aif.getAgentGropName()) ? null : aif.getAgentGropName()) : aif.getAgentInfo());
                    pres.setString(16,   "".equals(aif.getCheckAgentGropName()) ? null : aif.getCheckAgentGropName());
                    pres.setLong(17, Long.valueOf(aif.getPriority()));
                    pres.setLong(18, Long.valueOf(aif.getWeights()));
                    pres.setString(19,  "".equals(aif.getAptGroupName()) ? null : aif.getAptGroupName());
                    pres.setString(20,  "".equals(aif.getAptFileName()) ? null : aif.getAptFileName());
                    pres.setString(21,  "".equals(aif.getIsDB2()) ? null : aif.getIsDB2());
                    pres.setString(22,  "".equals(aif.getDb2IP()) ? null : aif.getDb2IP());
                    pres.setString(23,  "".equals(aif.getMainline()) ? null : aif.getMainline());
                    pres.setString(24,  "".equals(aif.getSEFlag()) ? null : aif.getSEFlag());
                    pres.setString(25,  "".equals(aif.getDisableFlag()) ? null : aif.getDisableFlag());

                    String iactpreno = "";
                    if (DBManager.Orcl_Faimily())
                    {
                        pres.setString(26, "");
                    } else
                    {
                        InputStream fis1 = null;
                        try
                        {
                            if (StringUtils.isNotBlank(iactpreno))
                            {
                                fis1 = new ByteArrayInputStream(iactpreno.getBytes(Constants.CHARSET_UTF_8));
                                pres.setAsciiStream(26, fis1, iactpreno.getBytes(Constants.CHARSET_UTF_8).length);
                            } else
                            {
                                pres.setAsciiStream(26, null, 0);
                            }

                        } catch (UnsupportedEncodingException e)
                        {
                            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                        }

                    }


                    String iactsuccno = "";
                    if (DBManager.Orcl_Faimily())
                    {
                        pres.setString(27, "");
                    } else
                    {
                        InputStream fis1 = null;
                        try
                        {
                            if (StringUtils.isNotBlank(iactsuccno))
                            {
                                fis1 = new ByteArrayInputStream(iactsuccno.getBytes(Constants.CHARSET_UTF_8));
                                pres.setAsciiStream(27, fis1, iactsuccno.getBytes(Constants.CHARSET_UTF_8).length);
                            } else
                            {
                                pres.setAsciiStream(27, null, 0);
                            }

                        } catch (UnsupportedEncodingException e)
                        {
                            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                        }

                    }
                    pres.setString(28,   "".equals(excelPath) ? null : excelPath);
                    pres.setString(29,  "".equals(userName) ? null : userName);
                    pres.setLong(30, userId);
                    pres.setString(31,  "".equals(testRealPath) ? null : testRealPath);
                    pres.setInt(32, 0);
                    pres.setInt(33, aif.isAgentGroup() ? 0 : 1);
                    pres.setString(34,  "".equals(aif.getDelayTime()) ? null : aif.getDelayTime());
                    pres.setString(35,  "".equals(aif.getBranchCondition()) ? null : aif.getBranchCondition());
                    if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount())
                            && !"null".equals(aif.getReTryCount()))
                    {
                        pres.setInt(36, Integer.parseInt(aif.getReTryCount()));//

                    } else
                    {
                        pres.setInt(36, 0);// ,IRETRYNUM,ICALENDNAME
                    }
                    pres.setString(37,  "".equals(aif.getCalendName()) ? null : aif.getCalendName());
                    pres.setString(38,  "".equals(aif.getReTryTime()) ? null : aif.getReTryTime());
                    pres.setString(39,  "".equals(aif.getReTryEndTime()) ? null : aif.getReTryEndTime());
                    pres.setString(40,  "".equals(aif.getSkip()) ? null : aif.getSkip());
                    pres.setString(41,  "".equals(aif.getDelayWarnning()) ? null : aif.getDelayWarnning());
                }

                pres.addBatch();

                index++;
                if(index % 3000 == 0){
                    pres.executeBatch();
                }
            }
            pres.executeBatch();
        } catch (Exception e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, pres, "saveCopyExcelModelUploadExcel", log);
        }
        return flag;
    }

    private void closeInputStream(InputStream fis1){

        if(fis1 != null){
            try
            {
                fis1.close();
            } catch (IOException e)
            {
                log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            }
        }
    }

    public boolean saveAgentInfoToTable ( Map<String, ActInfoBean> mapList, ProjectSaveUtilBean projectSaveUtilBean,
                                          Connection conn, int basicType ) throws RepositoryException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        PreparedStatement pres1 = null;
        ResultSet rs1 = null;
        PreparedStatement press = null;
        ResultSet rss = null;
        boolean flag = true; //
        String sqls = "select IAGENTINFO_ID as iid from IEAI_AGENTINFO where IAGENT_IP=? and IAGENT_PORT=?";
        String sqlprj = "insert into ieai_project_agentinfo (IID,IPRJID,IPRJNAME,IRESOURCENAME,IAGENTIP,IAGENTPORT,IAGENTINFOID) values (?,?,?,?,?,?,?)";
        String sqlQprj = "SELECT 1 FROM ieai_project_agentinfo A WHERE EXISTS (SELECT * FROM ieai_project_agentinfo B WHERE B.IAGENTIP=? AND B.IAGENTPORT=? AND B.IPRJNAME=?)";
        try
        {

            for (ActInfoBean aif : mapList.values())
            {

                PreparedStatement ps1 = null;
                ps1 = conn.prepareStatement(sqlQprj);
                pres1 = conn.prepareStatement(sqlprj);
                if (!"".equals(aif.getAgentInfo()))
                {
                    boolean existsprj = false;
                    String[] agent = new String[2];
                    agent = aif.getAgentInfo().split(":");
                    ps1.setString(1, agent[0]);
                    ps1.setString(2, agent[1]);
                    ps1.setString(3, aif.getChildProjectName());
                    rs1 = ps1.executeQuery();
                    while (rs1.next())
                    {
                        existsprj = true;
                        break;
                    }
                    if (!existsprj)
                    {
                        long id = -1;
                        try
                        {
                            id = IdGenerator.createIdNoConnection("IEAI_PROJECT_AGENTINFO", basicType);
                        } catch (RepositoryException e)
                        {
                            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                        }
                        press = conn.prepareStatement(sqls);
                        press.setString(1, agent[0]);
                        press.setString(2, agent[1]);
                        rss = press.executeQuery();
                        if (rss.next())
                        {
                            if (id != -1)
                            {
                                long prjid = TaskUploadManager.getInstance().getPrjLastIdByPrjName(
                                        aif.getChildProjectName(), conn);
                                pres1.setLong(1, id);
                                pres1.setLong(2, prjid);
                                pres1.setString(3, aif.getChildProjectName());
                                pres1.setString(4, aif.getAgentGropName());
                                pres1.setString(5, agent[0]);
                                pres1.setString(6, agent[1]);
                                pres1.setLong(7, rss.getLong("iid"));

                                // 用于多写ieai_projet_agentinfo表
                                RepProjectAgentInfo prjAgentInfo = new RepProjectAgentInfo();
                                prjAgentInfo.setIid(id);
                                prjAgentInfo.setPrjId(prjid);
                                prjAgentInfo.setPrjName(aif.getChildProjectName());
                                prjAgentInfo.setResourceName(aif.getAgentGropName());
                                prjAgentInfo.setAgentIp(agent[0]);
                                prjAgentInfo.setAgentPort(agent[1]);
                                prjAgentInfo.setAgentInfoId(rss.getLong("iid"));
                                projectSaveUtilBean.getInsertPrjAgentInfoList().add(prjAgentInfo);

                                pres1.executeUpdate();
                            }
                        }
                    }
                }
                DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                DBResource.closePSRS(rss, press,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                DBResource.closePreparedStatement(pres1,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                DBResource.closeResultSet(rs1,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            }

        } catch (Exception e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(pres, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs1,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rss, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(pres1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return flag;
    }

    /**
     * 对象类型转换：Clob转String
     *
     * @since 2013.12.16
     * <AUTHOR>
     * @param clob
     * @return
     * @throws SQLException
     * @throws IOException
     */
    private String clobToString ( Clob clob ) throws SQLException, IOException
    {

        String reString = null;
        if (clob == null)
        {
            return reString;
        }
        Reader is = clob.getCharacterStream();// 得到流
        BufferedReader br = new BufferedReader(is);
        String s = br.readLine();
        StringBuilder sb = new StringBuilder();
        while (s != null)
        {
            // 执行循环将字符串全部取出付值给StringBuffer由StringBuffer转成STRING
            sb.append(s);
            sb.append("\n");
            s = br.readLine();
        }
        br.close();
        is.close();
        reString = sb.toString();
        if (reString.equals(""))
        {
            return null;
        }
        return reString;
    }

    /**
     * 将以指定分隔符切割的字符串转换为无重复值的List
     *
     * @param str
     * @param delimiter 分隔符
     * @return
     */
    private static List<String> changeStringtoList ( String str, String delimiter )
    {
        List<String> resaultList = new ArrayList<String>();
        if (str != null && (!str.equals("")))
        {
            String[] strlist = str.split(delimiter);
            HashSet<String> stringset = new HashSet<String>();
            for (int i = 0; i < strlist.length; i++)
            {
                stringset.add(strlist[i].trim());
            }
            Iterator<String> in = stringset.iterator();
            while (in.hasNext())
            {
                resaultList.add(in.next().trim());
            }
        }
        return resaultList;
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws RepositoryException
     */
    public Map saveUpdateExcelModel ( ActInfoBean aif,ProjectSaveUtilBean projectSaveUtilBean, Map<String, ActInfoBean> mapList, String type, Long iid,
                                      Connection conn,  int basicType ,Map allActMap)
            throws ServerException, SQLException, RepositoryException
    {
        Long proid = null;
        Map returnMap = new HashMap();


        String sql = "";

        String sql1 = "";

        String sql2 = "";
        String sqli = "";
        String sqlq = "";

        String sql5 = "";

        String sql22 = "";

        long id = 0;
        if ("copy".equals(type))
        {
            projectSaveUtilBean.setExcelModelCopy(true);
            if(Environment.getInstance().getDGBankSwitch()){
                sql = "insert into IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,DAYS,LOGIC,WDAYS,IMONTH,IACTPARAMS) values(?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }else if(Environment.getInstance().getGYBankSwitch()){
                sql = "insert into IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,DAYS,LOGIC,WDAYS,IMONTH,IACTNO) values(?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }else{
                sql = "insert into IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,DAYS,LOGIC,WDAYS,IMONTH,IJOBTYPE) values(?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }
            sql1 = " INSERT INTO IEAI_ACTPRE_COPY(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql2 = " INSERT INTO IEAI_ACTSUCC_COPY(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql22 = "INSERT INTO IEAI_SUPDATA (IVESIONID,projectname, isystem, mainline, actname, preprojectname, premainline, preactname) values (?,?, ?, ?, ?, ?, ?, ?) ";
        } else
        {
            if (Environment.getInstance().getDGBankSwitch())
            {
                sql = "insert into IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,DAYS,LOGIC,WDAYS,IMONTH,IACTPARAMS) values(?,?,?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            } else if(Environment.getInstance().getGYBankSwitch()){
                sql = "insert into IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,DAYS,LOGIC,WDAYS,IMONTH,IACTNO) values(?,?,?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            }else{
                sql = "insert into IEAI_EXCELMODEL(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                        + "IOKFILEABSOLUTEPATH,IINPUTPARAM,ICHILDPRONAME,ICHANGEOPR,IACTNAME,IACTDESCRIPTION,IOUTPUTPARAM,ISHELLHOUSE,ISHELLABSOLUTEPATH,ILASTLINE,IWEIGHTS,IPRIORITY,IOKFILEFINDWEEK,IFLAG,ICHECKAGENTGROUP,APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, APTRESGROUPNAME,ISYSTEM,IREDO,ISAGENTGROUP,IDELAYTIME,IBRANCHCONDITION,IRETRYNUM,ICALENDNAME,IRETRYTIME,IRETRYENDTIME,IAUTOSKIP,IDELAYWARNNING,JOBLIST,SEQUENCENUMBER,IENDTAG,IUSERTASK,DAYS,LOGIC,WDAYS,IMONTH,IJOBTYPE) values(?,?,?,?,?,?,?,?,"
                        + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }

            sql1 = "insert into IEAI_ACTPRE(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql2 = "insert into IEAI_ACTSUCC(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";
            sqli = "insert into ieai_actfinished_flag_new (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values ("
                    + SQLTOCHARTDATEYYMMDD
                    + SQLSTIEAIEXCELMODELWHEMAI;
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqli = "insert into ieai_actfinished_flag_new (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values ("
                        + SQLTOCHARTDATEYYMMDD
                        + SQLSELSYSDATERTIEAIEXCELMODELWH;
            }

            sqlq = "select count(t.iid) as cou from ieai_actfinished_flag_new t where t.iproname=? and t.iflowname=? and t.iactname=? and t.idatadate=(to_char(to_date(?, 'YYYYMMDD') - ?, 'YYYYMMDD')) and t.ioperationid in (select t.ioperationid from ieai_excelmodel t where t.imainproname=? and t.isystem=? and t.imainlinename=? and t.iactname=?)";

            sql5 = "SELECT TO_CHAR(MAX(TO_DATE(T2.IDATADATE, 'YYYYMMDD')),'YYYYMMDD') AS IDATADATE FROM IEAI_ACTFINISHED_FLAG_NEW T2 WHERE T2.IPRONAME=? AND T2.IFLOWNAME=? AND T2.IACTNAME=?";

        }

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement presinsertws = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps1 = conn.prepareStatement(sql1);
            ps2 = conn.prepareStatement(sql2);
            if ("copy".equals(type))
            {
                if(allActMap.isEmpty()){
                    id = generCountExcelModel( basicType);
                }else{
                    id = allActMap.get(aif.getProjectName().trim()+aif.getMainline().trim()+aif.getActName().trim()) == null?0:(long) allActMap.get(aif.getProjectName().trim()+aif.getMainline().trim()+aif.getActName().trim());
                }
                if(id == 0){
                    id = generCountExcelModel( basicType);
                }
                ps6 = conn.prepareStatement(sql22);
            } else
            {
                id = getExcelModelIid(conn, aif);
                presinsertws = conn.prepareStatement(sqli);
                ps4 = conn.prepareStatement(sqlq);
                ps5 = conn.prepareStatement(sql5);
            }

            long iHeadTailFlag = 0;
            String okFileABPath = "0";
            long iweights = 1;
            long priority = 1;
            long oKFileFindWeek = 0;
            long retryNum = 0;
            long retryTime = 0;

            ps.setLong(1, id);
            ps.setString(2, aif.getProjectName());
            ps.setString(3, aif.getMainline());
            if ("总头".equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 1;
                ps.setLong(4, 1);
            } else if ("主线头".equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 2;
                ps.setLong(4, 2);
            } else if ("总尾".equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 6;
                ps.setLong(4, 6);
            } else if ("主线尾".equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 7;
                ps.setLong(4, 7);
            } else
            {
                iHeadTailFlag = 0;
                ps.setLong(4, 0);
            }
            ps.setString(5, StringUtils.isBlank(aif.getAgentInfo()) ? aif.getAgentGropName() : aif.getAgentInfo());
            ps.setString(6, aif.getOKFileABPath());
            ps.setString(7, "数据日期");
            ps.setString(8, aif.getChildProjectName());
            ps.setLong(9, Long.valueOf(aif.getDeleteFlag()));
            ps.setString(10, aif.getActName());
            ps.setString(11, aif.getDescribe());
            ps.setString(12, aif.getOutputPamaeter());
            ps.setString(13, aif.getShellCrust());
            ps.setString(14, aif.getShellABPath());
            if (null == aif.getOKFileABPath() || "".equals(aif.getOKFileABPath()))
            {
                ps.setString(15, "0");
            } else
            {
                okFileABPath = "0,nf";
                ps.setString(15, "0,nf");
            }
            if (null == aif.getWeights() || "".equals(aif.getWeights()))
            {
                ps.setLong(16, 1);
            } else
            {
                iweights = Long.valueOf(aif.getWeights());
                ps.setLong(16, Long.valueOf(aif.getWeights()));
            }
            if (null == aif.getPriority() || "".equals(aif.getPriority()))
            {
                ps.setLong(17, 1);
            } else
            {
                ps.setLong(17, Long.valueOf(aif.getPriority()));
            }
            if (null == aif.getOKFileFindWeek() || "".equals(aif.getOKFileFindWeek()))
            {
                ps.setLong(18, 0);
            } else
            {
                ps.setLong(18, Long.valueOf(aif.getOKFileFindWeek()));
            }
            ps.setLong(19, Long.valueOf("1"));
            ps.setString(20, aif.getCheckAgentGropName());
            // apt
            ps.setString(21, aif.getAptGroupName());
            ps.setString(22, aif.getAptFileName());
            ps.setString(23, aif.getIsDB2());
            ps.setString(24, aif.getDb2IP());
            ps.setString(25, aif.getAptResGroupname());
            // apt
            ps.setString(26, aif.getSystem());
            ps.setString(27, aif.getRedo());
            ps.setInt(28, aif.isAgentGroup() ? 0 : 1);
            ps.setString(29, aif.getDelayTime());
            ps.setString(30, aif.getBranchCondition());
            if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount()) && !"null".equals(aif.getReTryCount()))
            {
                retryNum = Integer.parseInt(aif.getReTryCount());
                ps.setInt(31, Integer.parseInt(aif.getReTryCount()));//

            } else
            {
                ps.setInt(31, 0);// ,IRETRYNUM,ICALENDNAME
            }
            ps.setString(32, aif.getCalendName());
            // ,IRETRYNUM,ICALENDNAME
            if (null != aif.getReTryTime() && !"".equals(aif.getReTryTime()) && !"null".equals(aif.getReTryTime()))
            {
                retryTime = Long.parseLong(aif.getReTryTime());
                ps.setInt(33, Integer.parseInt(aif.getReTryTime()));//

            } else
            {
                ps.setInt(33, 0);
            }
            ps.setString(34, aif.getReTryEndTime());
            ps.setString(35, aif.getSkip());
            ps.setString(36, aif.getDelayWarnning());
            ps.setString(37, StringUtils.isNotEmpty(aif.getJoblist())?aif.getJoblist():"");
            ps.setString(38,  StringUtils.isNotEmpty(aif.getActNo())?aif.getActNo():"");
            ps.setInt(39, "是".equals(aif.getEndTag()) ? 1: 0);
            /*    ps.setString(40,  StringUtils.isNotEmpty(aif.getOkFileName())?aif.getOkFileName():"");*/
            ps.setInt(40, aif.getUserTask());
            ps.setString(41, StringUtils.isNotEmpty(aif.getDays())?aif.getDays():"");
            ps.setString(42, StringUtils.isNotEmpty(aif.getLogic())?aif.getLogic():"");
            ps.setString(43, StringUtils.isNotEmpty(aif.getWdays())?aif.getWdays():"");
            ps.setString(44, StringUtils.isNotEmpty(aif.getMonth())?aif.getMonth():"");
            if(Environment.getInstance().getDGBankSwitch()){
                ps.setString(45, StringUtils.isNotEmpty(aif.getActParams())?aif.getActParams():"");
            }else if(Environment.getInstance().getGYBankSwitch()){
                ps.setString(45, aif.getActNo()+"");
            }else{
                ps.setString(45, StringUtils.isNotEmpty(aif.getJobType())?aif.getJobType():"");
            }


            // 多写记录存储excelmodel表数据
            ExcelmodelBean excelmodelBean = new ExcelmodelBean();
            excelmodelBean.setIoperationid(id);
            excelmodelBean.setImainproname(aif.getProjectName());
            excelmodelBean.setImainlinename(aif.getMainline());
            excelmodelBean.setIheadtailflag(iHeadTailFlag);
            excelmodelBean.setIagentsourcegroup(StringUtils.isBlank(aif.getAgentInfo()) ? aif.getAgentGropName() : aif.getAgentInfo());
            excelmodelBean.setIokfileabsolutepath(aif.getOKFileABPath());
            excelmodelBean.setIinputparam("数据日期");
            excelmodelBean.setIchildproname(aif.getChildProjectName());
            excelmodelBean.setIdeleteflag(Long.valueOf(aif.getDeleteFlag()));
            excelmodelBean.setIactname(aif.getActName());
            excelmodelBean.setIactdescription(aif.getDescribe());
            excelmodelBean.setIoutputparam(aif.getOutputPamaeter());
            excelmodelBean.setIshellhouse(aif.getShellCrust());
            excelmodelBean.setIshellabsolutepath(aif.getShellABPath());
            excelmodelBean.setIokfileabsolutepath(okFileABPath);
            excelmodelBean.setIweights(iweights);
            excelmodelBean.setIpriority(priority);
            excelmodelBean.setIokfilefindweek(oKFileFindWeek);
            excelmodelBean.setIflag(Long.valueOf("1"));
            excelmodelBean.setCheckAgentGropName(aif.getCheckAgentGropName());
            excelmodelBean.setAptgroupname(aif.getAptGroupName());
            excelmodelBean.setAptfilename(aif.getAptFileName());
            excelmodelBean.setIsdb2(aif.getIsDB2());
            excelmodelBean.setDb2ip(aif.getDb2IP());
            excelmodelBean.setAptresgroupname(aif.getAptResGroupname());
            excelmodelBean.setIsystem(aif.getSystem());
            excelmodelBean.setIredo(aif.getRedo());
            excelmodelBean.setIsAgentGroup(aif.isAgentGroup() ? 0 : 1);
            excelmodelBean.setDelayTime(aif.getDelayTime());
            excelmodelBean.setBranchCondition(aif.getBranchCondition());
            excelmodelBean.setReTryCount(retryNum);
            excelmodelBean.setCalendName(aif.getCalendName());
            excelmodelBean.setReTryTime(retryTime);
            excelmodelBean.setReTryEndTime(aif.getReTryEndTime());
            excelmodelBean.setScString(aif.getSkip());
            excelmodelBean.setDelayWarnning(aif.getDelayWarnning());
            excelmodelBean.setJobType(aif.getJobType());
            projectSaveUtilBean.getInsertExcelModelList().add(excelmodelBean);

            // ,IRETRYTIME,IRETRYENDTIME
            ps.executeUpdate();

            projectSaveUtilBean.getAifInfoList().add(aif);
            if ("copy".equals(type))
            {
                updateExcelModelCopyOpertionId(aif, id, conn);
            } else
            {
                if (SystemConfig.isUpdateOperIdflag())
                {
                    updateExcelModelOpertionId(aif, id, conn);
                }
            }
            List<String> listBefore = aif.getBeforeActList();

            for (int j = 0; j < listBefore.size(); j++)
            {
                String before = listBefore.get(j);
                ActInfoBean aifInfoBean = mapList.get(before);
                aifInfoBean.setOprationid(id);
                ps1.setLong(1, id);
                ps1.setString(2, aifInfoBean.getActName());
                ps1.setString(3, aifInfoBean.getChildProjectName());
                ps1.setString(4, aifInfoBean.getProjectName());
                ps1.setString(5, aifInfoBean.getMainline());
                ps1.executeUpdate();
                // 多写时存储ieai_actpre表记录的信息
                projectSaveUtilBean.getInserActpreList().add(aifInfoBean);
                if ("copy".equals(type))
                {
                    if (!aif.getMainline().equals(aifInfoBean.getMainline()))
                    {

                        ActFinishedFlagNew supdataBean = new ActFinishedFlagNew();
                        ps6.setLong(1, iid);
                        ps6.setString(2, aif.getProjectName());
                        ps6.setString(3, aif.getSystem());
                        ps6.setString(4, aif.getMainline());
                        ps6.setString(5, aif.getActName());
                        ps6.setString(6, aifInfoBean.getProjectName());
                        ps6.setString(7, aifInfoBean.getMainline());
                        ps6.setString(8, aifInfoBean.getActName());
                        // 存储excelmodelcopy时，多写存储ieai_supdata表记录
                        supdataBean.setIid(iid);
                        supdataBean.setPrjName(aif.getProjectName());
                        supdataBean.setSystem(aif.getSystem());
                        supdataBean.setMainLineName(aif.getMainline());
                        supdataBean.setActName(aif.getActName());
                        supdataBean.setIproname(aifInfoBean.getProjectName());
                        supdataBean.setIflowname( aifInfoBean.getMainline());
                        supdataBean.setIactname(aifInfoBean.getActName());
                        projectSaveUtilBean.getInsertSupdataList().add(supdataBean);
                        ps6.executeUpdate();
                    }
                } else
                {
                    if (!aif.getMainline().equals(aifInfoBean.getMainline()) && SystemConfig.isInsertFlag() && ps5 != null)
                    {

                        String datadate = "";
                        ps5.setString(1, aifInfoBean.getProjectName());
                        ps5.setString(2, aifInfoBean.getMainline());
                        ps5.setString(3, aifInfoBean.getActName());
                        int forcount = 0;
                        try
                        {
                            rs = ps5.executeQuery();
                            forcount = SystemConfig.getInsertData();
                            while (rs.next())
                            {
                                datadate = rs.getString(CIDATADATE);
                            }
                        } catch (Exception e)
                        {
                            log.info("saveUpdateExcelModel is get datadate is error:" + e.getMessage());
                        }

                        if (!"".equals(datadate) && null != datadate)
                        {

                            for (int ii = 0; ii <= forcount; ii++)
                            {
                                int count = 0;
                                ps4.setString(1, aifInfoBean.getProjectName());
                                ps4.setString(2, aifInfoBean.getMainline());
                                ps4.setString(3, aifInfoBean.getActName());
                                ps4.setString(4, datadate);
                                ps4.setInt(5, ii);
                                ps4.setString(6, aif.getProjectName());
                                ps4.setString(7, aif.getSystem());
                                ps4.setString(8, aif.getMainline());
                                ps4.setString(9, aif.getActName());
                                rs = ps4.executeQuery();
                                while (rs.next())
                                {
                                    count = rs.getInt("cou");
                                }
                                if (count <= 0)
                                {
                                    long actFlagId = IdGenerator.createIdNoConnection(IEAIACTFINISHEDFLAGNEW,
                                            basicType);
                                    presinsertws.setLong(1, actFlagId);
                                    presinsertws.setLong(2, ii + 1L);
                                    presinsertws.setString(3, datadate);
                                    presinsertws.setInt(4, ii);
                                    presinsertws.setString(5, aifInfoBean.getProjectName());
                                    presinsertws.setString(6, aifInfoBean.getMainline());
                                    presinsertws.setString(7, aifInfoBean.getActName());
                                    presinsertws.setString(8, aif.getProjectName());
                                    presinsertws.setString(9, aif.getSystem());
                                    presinsertws.setString(10, aif.getMainline());
                                    presinsertws.setString(11, aif.getActName());
                                    presinsertws.addBatch();

                                    // 多写时，写入ieai_actfinishedflag_new表记录的数据
                                    ActFinishedFlagNew actFlagNew = new ActFinishedFlagNew();
                                    actFlagNew.setIid(actFlagId);
                                    actFlagNew.setIprjid(ii + 1L);
                                    actFlagNew.setIdatadate(datadate);
                                    actFlagNew.setIi(ii);
                                    actFlagNew.setIproname(aifInfoBean.getProjectName());
                                    actFlagNew.setIflowname( aifInfoBean.getMainline());
                                    actFlagNew.setIactname(aifInfoBean.getActName());
                                    actFlagNew.setPrjName(aif.getProjectName());
                                    actFlagNew.setSystem(aif.getSystem());
                                    actFlagNew.setMainLineName(aif.getMainline());
                                    actFlagNew.setActName(aif.getActName());
                                    projectSaveUtilBean.getInsertActFlagNew().add(actFlagNew);
                                }
                            }
                        }

                    }

                }

                if (!"copy".equals(type) && SystemConfig.isInsertFlag() && presinsertws != null)
                {
                    presinsertws.executeBatch();
                }
            }
            List<String> listAfter = aif.getAfterActList();
            for (int j = 0; j < listAfter.size(); j++)
            {
                String after = listAfter.get(j);
                ActInfoBean aifInfoBean = mapList.get(after);
                aifInfoBean.setOprationid(id);
                ps2.setLong(1, id);
                ps2.setString(2, aifInfoBean.getActName());
                ps2.setString(3, aifInfoBean.getChildProjectName());
                ps2.setString(4, aifInfoBean.getProjectName());
                ps2.setString(5, aifInfoBean.getMainline());
                ps2.executeUpdate();
                //多写时写入ieai_actsucc表记录的信息
                projectSaveUtilBean.getInserActSuccList().add(aifInfoBean);
            }

            proid = id;
            RepWorkflowInstance conBean = new RepWorkflowInstance();

            conBean.setProjectName(aif.getChildProjectName());
            conBean.setFlowName(aif.getActName());
            conBean.set_isystem(aif.getSystem());
            conBean.set_isetUser(aif.getUserName());

            if (null != aif.getDisableFlag() && "1".equals(aif.getDisableFlag()))
            {
                // 新增禁用记录
                returnMap.put(CADDBEAN, conBean);
            } else if (null != aif.getDisableFlag() && "0".equals(aif.getDisableFlag()))
            {
                // 删除禁用记录
                returnMap.put(CDELETEBEAN, conBean);
            }
            if (SystemConfig.isUpdateOperIdflag())
            {
                returnMap.put("id", id);
            }
            if ("copy".equals(type))
            {
                projectSaveUtilBean.getUpdatepreSuccList().add(aif);
                updatePreSuccCopyOpertionId(aif, id, conn);
            } else
            {
                if (SystemConfig.isUpdateOperIdflag())
                {
                    projectSaveUtilBean.getUpdatepreSuccList().add(aif);
                    updatePreSuccOpertionId(aif, id, conn);
                }
            }

        } catch (SQLException e)
        {
            log.error("saveExcelModel EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(presinsertws, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps5, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps6, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        returnMap.put(CPROID, proid);
        return returnMap;
    }

    /**
     * <AUTHOR>
     * @des:修改excel及依赖触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public Map updateExcelModel ( ActInfoBean aif,ProjectSaveUtilBean projectSaveUtilBean, Map<String, ActInfoBean> mapList, String actId, String type,
                                  Long iid, Connection conn, int basicType ) throws  SQLException, RepositoryException
    {
        projectSaveUtilBean.setUpdateExcelModel(true);
        Long proid = null;

        Map returnMap = new HashMap();

        String sql3 = "";

        String sql1 = "";

        String sql2 = "";

        String sqli = "";

        String sqlq = "";

        String sql5 = "";

        String sql22 = "";

        if ("copy".equals(type))
        {

            if(Environment.getInstance().getDGBankSwitch()){
                sql3 = "update IEAI_EXCELMODEL_COPY set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=?,IAUTOSKIP=? ,IDELAYWARNNING=?,DAYS =?,LOGIC=?,WDAYS=?,PERFORMUSER=?,VIRTUALNAME=?,JOBLIST=?,SEQUENCENUMBER=?,IENDTAG=?,IUSERTASK=?,IMONTH=?,IACTPARAMS=? where IOPERATIONID=?";
            }else if(Environment.getInstance().getGYBankSwitch())
            {
                sql3 = "update IEAI_EXCELMODEL_COPY set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=?,IAUTOSKIP=? ,IDELAYWARNNING=?,DAYS =?,LOGIC=?,WDAYS=?,PERFORMUSER=?,VIRTUALNAME=?,JOBLIST=?,SEQUENCENUMBER=?,IENDTAG=?,IUSERTASK=?,IMONTH=?,IACTNO=? where IOPERATIONID=?";
            }else{
                sql3 = "update IEAI_EXCELMODEL_COPY set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                    + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=?,IAUTOSKIP=? ,IDELAYWARNNING=?,DAYS =?,LOGIC=?,WDAYS=?,PERFORMUSER=?,VIRTUALNAME=?,JOBLIST=?,SEQUENCENUMBER=?,IENDTAG=?,IUSERTASK=?,IMONTH=?,IJOBTYPE=? where IOPERATIONID=?";
            }


            sql1 = "insert into IEAI_ACTPRE_COPY(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql2 = "insert into IEAI_ACTSUCC_COPY(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql22 = "insert into ieai_supdata (IVESIONID,projectname, isystem, mainline, actname, preprojectname, premainline, preactname) values (?,?, ?, ?, ?, ?, ?, ?) ";
        } else
        {

            if(Environment.getInstance().getDGBankSwitch())
            {
                sql3 = "update IEAI_EXCELMODEL set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=? ,IAUTOSKIP=?,IDELAYWARNNING=?,DAYS =?,LOGIC=?,WDAYS=?,PERFORMUSER=?,VIRTUALNAME=?,JOBLIST=?,SEQUENCENUMBER=?,IENDTAG=?,IUSERTASK=?,IMONTH=?,IACTPARAMS=? where IOPERATIONID=?";
            }else if(Environment.getInstance().getGYBankSwitch())
            {
                sql3 = "update IEAI_EXCELMODEL set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=? ,IAUTOSKIP=?,IDELAYWARNNING=?,DAYS =?,LOGIC=?,WDAYS=?,PERFORMUSER=?,VIRTUALNAME=?,JOBLIST=?,SEQUENCENUMBER=?,IENDTAG=?,IUSERTASK=?,IMONTH=?,IACTNO=?  where IOPERATIONID=?";
            }else{
                sql3 = "update IEAI_EXCELMODEL set IMAINPRONAME=?,IMAINLINENAME=?,IHEADTAILFLAG=?,IAGENTSOURCEGROUP=?,"
                        + "IOKFILEABSOLUTEPATH=?,IINPUTPARAM=?,ICHILDPRONAME=?,ICHANGEOPR=?,IACTNAME=?,IACTDESCRIPTION=?,IOUTPUTPARAM=?,ISHELLHOUSE=?,ISHELLABSOLUTEPATH=?,ILASTLINE=?,IWEIGHTS=?,IPRIORITY=?,IOKFILEFINDWEEK=?,IFLAG=?,ICHECKAGENTGROUP=? , APTGROUPNAME=?, APTFILENAME=?, ISDB2=?, DB2IP=?, APTRESGROUPNAME=? ,ISYSTEM=? ,IREDO=?,ISAGENTGROUP=?,IDELAYTIME=?,IBRANCHCONDITION=?,IRETRYNUM=?,ICALENDNAME=?,IRETRYTIME=?,IRETRYENDTIME=? ,IAUTOSKIP=?,IDELAYWARNNING=?,DAYS =?,LOGIC=?,WDAYS=?,PERFORMUSER=?,VIRTUALNAME=?,JOBLIST=?,SEQUENCENUMBER=?,IENDTAG=?,IUSERTASK=?,IMONTH=?,IJOBTYPE=? where IOPERATIONID=?";

            }
            sql1 = "insert into IEAI_ACTPRE(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sql2 = "insert into IEAI_ACTSUCC(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

            sqli = "insert into ieai_actfinished_flag_new (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values "
                    + SQLTOCHARTDATEYYMMDD + " ?, ?, ?, SYSDATE, ?)";

            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqli = "insert into ieai_actfinished_flag_new (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values "
                        + SQLTOCHARTDATEYYMMDD + " ?, ?, ?, sysdate(), ?)";
            }

            sqlq = "select count(t.iid) as cou from ieai_actfinished_flag_new t where t.iproname=? and t.iflowname=? and t.iactname=? and t.idatadate=(to_char(to_date(?, 'YYYYMMDD') - ?, 'YYYYMMDD')) and t.ioperationid=?";

            sql5 = "select to_char(max(to_date(t2.idatadate, 'YYYYMMDD')),'YYYYMMDD') as idatadate from ieai_actfinished_flag_new t2 where t2.iproname=? and t2.iflowname=? and t2.iactname=?";

        }

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement presinsertws = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql3);
            ps1 = conn.prepareStatement(sql1);
            ps2 = conn.prepareStatement(sql2);
            if ("copy".equals(type))
            {
                ps6 = conn.prepareStatement(sql22);
            } else
            {
                presinsertws = conn.prepareStatement(sqli);
                ps4 = conn.prepareStatement(sqlq);
                ps5 = conn.prepareStatement(sql5);
            }

            ps.setString(1, aif.getProjectName());
            ps.setString(2, aif.getMainline());
            long iHeadTailFlag = 0;
            if (("总头").equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 1;
                ps.setLong(3, 1);
            } else if ("主线头".equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 2;
                ps.setLong(3, 2);
            } else if ("总尾".equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 6;
                ps.setLong(3, 6);
            } else if ("主线尾".equals(aif.getSEFlag()))
            {
                iHeadTailFlag = 7;
                ps.setLong(3, 7);
            } else
            {
                iHeadTailFlag = 0;
                ps.setLong(3, 0);
            }
            String agentInfo = "";
            if (StringUtils.isNotBlank(aif.getAgentInfo()))
            {
                agentInfo = aif.getAgentInfo();
                ps.setString(4, aif.getAgentInfo());
            } else
            {
                agentInfo = aif.getAgentGropName();
                ps.setString(4, aif.getAgentGropName());
            }
            ps.setString(5, aif.getOKFileABPath());
            ps.setString(6, "数据日期");
            ps.setString(7, aif.getChildProjectName());
            ps.setLong(8, Long.valueOf(aif.getDeleteFlag()));
            ps.setString(9, aif.getActName());
            ps.setString(10, aif.getDescribe());
            ps.setString(11, aif.getOutputPamaeter());
            ps.setString(12, aif.getShellCrust());
            ps.setString(13, aif.getShellABPath());
            String okFileABPath = "0";
            if (null == aif.getOKFileABPath() || "".equals(aif.getOKFileABPath()))
            {
                ps.setString(14, "0");
            } else
            {
                okFileABPath = "0,nf";
                ps.setString(14, "0,nf");
            }
            long iweights = 1;
            if (null == aif.getWeights() || "".equals(aif.getWeights()))
            {
                ps.setLong(15, 1);
            } else
            {
                iweights = Long.valueOf(aif.getWeights());
                ps.setLong(15, Long.valueOf(aif.getWeights()));
            }
            long priority = 1;
            if (null == aif.getPriority() || "".equals(aif.getPriority()))
            {
                ps.setLong(16, 1);
            } else
            {
                priority = Long.valueOf(aif.getPriority());
                ps.setLong(16, Long.valueOf(aif.getPriority()));
            }
            long oKFileFindWeek = 0;
            if (null == aif.getOKFileFindWeek() || "".equals(aif.getOKFileFindWeek()))
            {
                ps.setLong(17, 0);
            } else
            {
                oKFileFindWeek = Long.valueOf(aif.getOKFileFindWeek());
                ps.setLong(17, Long.valueOf(aif.getOKFileFindWeek()));
            }
            ps.setLong(18, Long.valueOf(aif.getFlag()));
            ps.setString(19, aif.getCheckAgentGropName());
            // apt
            ps.setString(20, aif.getAptGroupName());
            ps.setString(21, aif.getAptFileName());
            ps.setString(22, aif.getIsDB2());
            ps.setString(23, aif.getDb2IP());
            ps.setString(24, aif.getAptResGroupname());
            // apt
            ps.setString(25, aif.getSystem());
            ps.setString(26, aif.getRedo());
            ps.setInt(27, aif.isAgentGroup() ? 0 : 1);
            ps.setString(28, aif.getDelayTime());
            ps.setString(29, aif.getBranchCondition());
            long retryNum = 0;
            if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount()) && !"null".equals(aif.getReTryCount()))
            {
                retryNum = Long.parseLong(aif.getReTryCount());
                ps.setInt(30, Integer.parseInt(aif.getReTryCount()));//

            } else
            {
                ps.setInt(30, 0);// ,IRETRYNUM,ICALENDNAME
            }

            ps.setString(31, aif.getCalendName());
            long retryTime = 0;
            if (null != aif.getReTryTime() && !"".equals(aif.getReTryTime()) && !"null".equals(aif.getReTryTime()))
            {
                retryTime = Long.parseLong(aif.getReTryTime());
                ps.setInt(32, Integer.parseInt(aif.getReTryTime()));//

            } else
            {
                ps.setInt(32, 0);// ,IRETRYNUM,ICALENDNAME
            }
            ps.setString(33, aif.getReTryEndTime());
            ps.setString(34, aif.getSkip());
            ps.setString(35, aif.getDelayWarnning());
            ps.setString(36, StringUtils.isNotEmpty(aif.getDays())?aif.getDays():"");
            ps.setString(37, StringUtils.isNotEmpty(aif.getLogic())?aif.getLogic():"");
            ps.setString(38, StringUtils.isNotEmpty(aif.getWdays())?aif.getWdays():"");
            ps.setString(39, StringUtils.isNotEmpty(aif.getPerformUser())?aif.getPerformUser():"");
            ps.setString(40, StringUtils.isNotEmpty(aif.getVirtualName())?aif.getVirtualName():"");
            ps.setString(41, StringUtils.isNotEmpty(aif.getJoblist())?aif.getJoblist():"");
            ps.setString(42,  StringUtils.isNotEmpty(aif.getActNo())?aif.getActNo():"");
            ps.setInt(43,"是".equals(aif.getEndTag())?1:0);
            /*     ps.setString(44,  StringUtils.isNotEmpty(aif.getOkFileName())?aif.getOkFileName():"");*/
            ps.setInt(44,aif.getUserTask());
            ps.setString(45, StringUtils.isNotEmpty(aif.getMonth())?aif.getMonth():"");
            if(Environment.getInstance().getDGBankSwitch()){
                ps.setString(46, aif.getActParams());
                ps.setLong(47, Long.valueOf(actId));
            }else if(Environment.getInstance().getGYBankSwitch()){
                ps.setString(46, aif.getActNo());
                ps.setLong(47, Long.valueOf(actId));
            }else {
                ps.setString(46, aif.getJobType());
                ps.setLong(47, Long.valueOf(actId));
            }


            //ps.setString(40, StringUstils.isNotEmpty(aif.getMonth())? aif.getMonth():"");
            // 多写记录存储excelmodel表数据
            ExcelmodelBean excelmodelBean = new ExcelmodelBean();
            excelmodelBean.setImainproname(aif.getProjectName());
            excelmodelBean.setImainlinename(aif.getMainline());
            excelmodelBean.setIheadtailflag(iHeadTailFlag);
            excelmodelBean.setIagentsourcegroup(agentInfo);
            excelmodelBean.setIokfileabsolutepath(aif.getOKFileABPath());
            excelmodelBean.setIinputparam("数据日期");
            excelmodelBean.setIchildproname(aif.getChildProjectName());
            excelmodelBean.setIdeleteflag(Long.valueOf(aif.getDeleteFlag()));
            excelmodelBean.setIactname(aif.getActName());
            excelmodelBean.setIactdescription(aif.getDescribe());
            excelmodelBean.setIoutputparam(aif.getOutputPamaeter());
            excelmodelBean.setIshellhouse(aif.getShellCrust());
            excelmodelBean.setIshellabsolutepath(aif.getShellABPath());
            excelmodelBean.setIokfileabsolutepath(okFileABPath);
            excelmodelBean.setIweights(iweights);
            excelmodelBean.setIpriority(priority);
            excelmodelBean.setIokfilefindweek(oKFileFindWeek);
            excelmodelBean.setIflag(Long.valueOf(aif.getFlag()));
            excelmodelBean.setCheckAgentGropName(aif.getCheckAgentGropName());
            excelmodelBean.setAptgroupname(aif.getAptGroupName());
            excelmodelBean.setAptfilename(aif.getAptFileName());
            excelmodelBean.setIsdb2(aif.getIsDB2());
            excelmodelBean.setDb2ip(aif.getDb2IP());
            excelmodelBean.setAptresgroupname(aif.getAptResGroupname());
            excelmodelBean.setIsystem(aif.getSystem());
            excelmodelBean.setIredo(aif.getRedo());
            excelmodelBean.setIsAgentGroup(aif.isAgentGroup() ? 0 : 1);
            excelmodelBean.setDelayTime(aif.getDelayTime());
            excelmodelBean.setBranchCondition(aif.getBranchCondition());
            excelmodelBean.setReTryCount(retryNum);
            excelmodelBean.setCalendName(aif.getCalendName());
            excelmodelBean.setReTryTime(retryTime);
            excelmodelBean.setReTryEndTime(aif.getReTryEndTime());
            excelmodelBean.setSkip(aif.getSkip());
            excelmodelBean.setDelayWarnning(aif.getDelayWarnning());
            excelmodelBean.setPerformUser(aif.getPerformUser());
            excelmodelBean.setVirtualName(aif.getVirtualName());
            excelmodelBean.setVirtualName(aif.getJoblist());
            excelmodelBean.setIoperationid(Long.valueOf(actId));
            excelmodelBean.setEndTag(aif.getEndTag());
            /*            excelmodelBean.setEndTag(aif.getOkFileName());*/
            excelmodelBean.setUserTask(aif.getUserTask());
            excelmodelBean.setJobType(aif.getJobType());
            projectSaveUtilBean.getUpdateExcelModelList().add(excelmodelBean);
            ps.executeUpdate();

            List<String> listBefore = aif.getBeforeActList();
            if (!listBefore.isEmpty())
            {
                for (int j = 0; j < listBefore.size(); j++)
                {
                    String before = listBefore.get(j);
                    ActInfoBean aifInfoBean = mapList.get(before);
                    ActPreBean actPreBeans = selectOneActpreManager(Long.valueOf(actId), aifInfoBean.getProjectName(),
                            aifInfoBean.getMainline(), aifInfoBean.getChildProjectName(), aifInfoBean.getActName(), type,
                            conn);
                    if (null == actPreBeans)
                    {
                        ps1.setLong(1, Long.valueOf(actId));
                        ps1.setString(2, aifInfoBean.getActName());
                        ps1.setString(3, aifInfoBean.getChildProjectName());
                        ps1.setString(4, aifInfoBean.getProjectName());
                        ps1.setString(5, aifInfoBean.getMainline());
                        ps1.executeUpdate();

                        ActInfoBean preBean = mapList.get(before);
                        preBean.setOprationid(Long.valueOf(actId));
                        preBean.setActName(aifInfoBean.getActName());
                        preBean.setChildProjectName(aifInfoBean.getChildProjectName());
                        preBean.setProjectName(aifInfoBean.getProjectName());
                        preBean.setMainline(aifInfoBean.getMainline());
                        // 多写时存储ieai_actpre表记录的信息
                        projectSaveUtilBean.getInserActpreList().add(preBean);

                        if ("copy".equals(type))
                        {
                            if (!aif.getMainline().equals(aifInfoBean.getMainline()))
                            {
                                ps6.setLong(1, iid);
                                ps6.setString(2, aif.getProjectName());
                                ps6.setString(3, aif.getSystem());
                                ps6.setString(4, aif.getMainline());
                                ps6.setString(5, aif.getActName());
                                ps6.setString(6, aifInfoBean.getProjectName());
                                ps6.setString(7, aifInfoBean.getMainline());
                                ps6.setString(8, aifInfoBean.getActName());

                                // 存储excelmodelcopy时，多写存储ieai_supdata表记录
                                ActFinishedFlagNew supdataBean = new ActFinishedFlagNew();
                                supdataBean.setIid(iid);
                                supdataBean.setPrjName(aif.getProjectName());
                                supdataBean.setSystem(aif.getSystem());
                                supdataBean.setMainLineName(aif.getMainline());
                                supdataBean.setActName(aif.getActName());
                                supdataBean.setIproname(aifInfoBean.getProjectName());
                                supdataBean.setIflowname( aifInfoBean.getMainline());
                                supdataBean.setIactname(aifInfoBean.getActName());
                                projectSaveUtilBean.getInsertSupdataList().add(supdataBean);
                                ps6.executeUpdate();
                            }
                        } else
                        {
                            if (!aif.getMainline().equals(aifInfoBean.getMainline()) && SystemConfig.isInsertFlag() && ps5 != null)
                            {
                                String datadate = "";
                                ps5.setString(1, aifInfoBean.getProjectName());
                                ps5.setString(2, aifInfoBean.getMainline());
                                ps5.setString(3, aifInfoBean.getActName());
                                int forcount = 0;
                                try
                                {
                                    rs = ps5.executeQuery();
                                    forcount = SystemConfig.getInsertData();
                                    while (rs.next())
                                    {
                                        datadate = rs.getString(CIDATADATE);
                                    }
                                } catch (Exception e)
                                {
                                    log.info("updateExcelModel is get datadate is error:" + e.getMessage());
                                }

                                if (!"".equals(datadate) && null != datadate)
                                {
                                    for (int ii = 0; ii <= forcount; ii++)
                                    {
                                        int count = 0;
                                        ps4.setString(1, aifInfoBean.getProjectName());
                                        ps4.setString(2, aifInfoBean.getMainline());
                                        ps4.setString(3, aifInfoBean.getActName());
                                        ps4.setString(4, datadate);
                                        ps4.setInt(5, ii);
                                        ps4.setLong(6, Long.valueOf(actId));
                                        rs = ps4.executeQuery();
                                        while (rs.next())
                                        {
                                            count = rs.getInt("cou");
                                        }
                                        if (count <= 0)
                                        {
                                            long actFlagId = IdGenerator.createIdNoConnection(
                                                    IEAIACTFINISHEDFLAGNEW, basicType);
                                            presinsertws.setLong(1, actFlagId);
                                            presinsertws.setLong(2, ii + 1L);
                                            presinsertws.setString(3, datadate);
                                            presinsertws.setInt(4, ii);
                                            presinsertws.setString(5, aifInfoBean.getProjectName());
                                            presinsertws.setString(6, aifInfoBean.getMainline());
                                            presinsertws.setString(7, aifInfoBean.getActName());
                                            presinsertws.setLong(8, Long.valueOf(actId));
                                            presinsertws.addBatch();

                                            // 多写时，写入ieai_actfinishedflag_new表记录的数据
                                            ActFinishedFlagNew actFlagNew = new ActFinishedFlagNew();
                                            actFlagNew.setIid(actFlagId);
                                            actFlagNew.setIprjid(ii + 1L);
                                            actFlagNew.setIdatadate(datadate);
                                            actFlagNew.setIi(ii);
                                            actFlagNew.setIproname(aifInfoBean.getProjectName());
                                            actFlagNew.setIflowname( aifInfoBean.getMainline());
                                            actFlagNew.setIactname(aifInfoBean.getActName());
                                            actFlagNew.setOperationId(Long.valueOf(actId));
                                            projectSaveUtilBean.getInsertActFlagNew().add(actFlagNew);
                                        }
                                    }
                                }
                            }

                        }

                    }
                }
                if (!"copy".equals(type) && SystemConfig.isInsertFlag() && presinsertws !=null)
                {
                    presinsertws.executeBatch();
                }
            }
            List<String> listAfter = aif.getAfterActList();
            if (!listAfter.isEmpty())
            {
                for (int j = 0; j < listAfter.size(); j++)
                {
                    String after = listAfter.get(j);
                    ActInfoBean aifInfoBean = mapList.get(after);
                    ActSuccBean actSuccBean = selectOneActsuccManager(Long.valueOf(actId),
                            aifInfoBean.getProjectName(), aifInfoBean.getMainline(), aifInfoBean.getChildProjectName(),
                            aifInfoBean.getActName(), type, conn);
                    if (actSuccBean == null)
                    {
                        ps2.setLong(1, Long.valueOf(actId));
                        ps2.setString(2, aifInfoBean.getActName());
                        ps2.setString(3, aifInfoBean.getChildProjectName());
                        ps2.setString(4, aifInfoBean.getProjectName());
                        ps2.setString(5, aifInfoBean.getMainline());
                        ps2.executeUpdate();

                        //多写时写入ieai_actsucc表记录的信息
                        ActInfoBean succBean = new ActInfoBean();
                        succBean.setOprationid(Long.valueOf(actId));
                        succBean.setActName(aifInfoBean.getActName());
                        succBean.setChildProjectName(aifInfoBean.getChildProjectName());
                        succBean.setProjectName(aifInfoBean.getProjectName());
                        succBean.setMainline(aifInfoBean.getMainline());
                        projectSaveUtilBean.getInserActSuccList().add(succBean);
                    }
                }
            }

            proid = new Long(actId);
            RepWorkflowInstance conBean = new RepWorkflowInstance();

            conBean.setProjectName(aif.getChildProjectName());
            conBean.setFlowName(aif.getActName());
            conBean.set_isystem(aif.getSystem());
            conBean.set_isetUser(aif.getUserName());

            if (null != aif.getDisableFlag() && "1".equals(aif.getDisableFlag()))
            {
                // 新增禁用记录
                returnMap.put(CADDBEAN, conBean);
            } else if (null != aif.getDisableFlag() && "0".equals(aif.getDisableFlag()))
            {
                // 删除禁用记录
                returnMap.put(CDELETEBEAN, conBean);
            }

        } catch (SQLException e)
        {
            log.error("updateExcelModel EXEC save IEAI_EXCELMODEL IS ERR" , e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(presinsertws, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps5, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps6, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        returnMap.put(CPROID, proid);
        return returnMap;
    }

    /**
     * <AUTHOR>
     * @des:删除ieai_basic_Excelmodel信息
     * @datea:2014-4-2
     * @param actId
     * @throws SQLException
     * @throws RepositoryException
     */
    public void deleteBasicExcelModel ( String proName, Connection conn ) throws SQLException, RepositoryException
    {
        String sql3 = "delete from ieai_basic_excelmodel where iid not in(select t.iid from ieai_basic_excelmodel t   where t.imainproname=? and t.istate=0 group by t.iid)"
                + " and imainproname=?";
        String sql = "";
        if (JudgeDB.IEAI_DB_TYPE ==3)
        {
            sql = "select t.iid from ieai_basic_excelmodel t where t.imainproname=? and t.istate=0 group by t.iid";
            sql3 = "delete from ieai_basic_excelmodel where iid not in(?) and imainproname=?";
        }
        PreparedStatement ps1 = null;
        ResultSet rSet = null;
        PreparedStatement ps2 = null;
        try
        {
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                ps1 = conn.prepareStatement(sql);
                ps1.setString(1, proName);
                rSet = ps1.executeQuery();

                String ids = "";
                while (rSet.next())
                {
                    ids += "'" + rSet.getInt("iid") + "',";
                }
                if (StringUtils.isBlank(ids))
                {
                    ids = "''";
                } else
                {
                    ids = StringUtils.substring(ids, 0, ids.length() - 1);
                    sql3 = "delete from ieai_basic_excelmodel where iid not in(" + ids + ") and imainproname=?";
                    ps2 = conn.prepareStatement(sql3);
                    ps2.setString(1, proName);
                    ps2.executeUpdate();
                }
            } else
            {
                ps2 = conn.prepareStatement(sql3);
                ps2.setString(1, proName);
                ps2.setString(2, proName);
                ps2.executeUpdate();
            }
        } catch (SQLException e)
        {
            log.error("deleteBasicExcelModel EXEC DEL ieai_basic_excelmodel IS ERR ", e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rSet, ps1, "deleteBasicExcelModel", log);
            DBResource.closePreparedStatement(ps2, "deleteBasicExcelModel", log);
        }
    }

    /**
     * <AUTHOR>
     * @des:修改excel信息的状态
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void updateExcelModelCopyOpertionId ( ActInfoBean aif, Long operationid, Connection conn )
            throws  SQLException, RepositoryException
    {

        String sql3 = "UPDATE IEAI_EXCELMODEL_COPY T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=? ";

        String sql4 = "SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?";

        String sql5 = "insert into ieai_deloptionid (ioldoperationid, imainproname,ICHILDPRONAME, imainlinename, iactname, isystem,IPERATIONUSER,IENDTIME) values (?, ?,?,?, ?, ?,?,SYSDATE)";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql5 = "insert into ieai_deloptionid (ioldoperationid, imainproname,ICHILDPRONAME, imainlinename, iactname, isystem,IPERATIONUSER,IENDTIME) values (?, ?,?,?, ?, ?,?,sysdate())";
        }
        PreparedStatement ps = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        ResultSet rs = null;
        long oldid = 0;
        try
        {

            ps3 = conn.prepareStatement(sql4);
            ps3.setString(1, aif.getProjectName());
            ps3.setString(2, aif.getMainline());
            ps3.setString(3, aif.getActName());
            ps3.setString(4, aif.getSystem());
            ps3.setString(5, aif.getChildProjectName());
            rs = ps3.executeQuery();
            while (rs.next())
            {
                oldid = rs.getLong(CIOLDOPERATIONID);
            }
            if (oldid > 0)
            {
                if (oldid != operationid)
                {
                    ps = conn.prepareStatement(sql3);
                    ps.setString(1, aif.getProjectName());
                    ps.setString(2, aif.getMainline());
                    ps.setString(3, aif.getActName());
                    ps.setString(4, aif.getSystem());
                    ps.setString(5, aif.getChildProjectName());
                    ps.setLong(6, operationid);
                    ps.executeUpdate();
                }
            } else
            {
                ps4 = conn.prepareStatement(sql5);
                ps4.setLong(1, operationid);
                ps4.setString(2, aif.getProjectName());
                ps4.setString(3, aif.getChildProjectName());
                ps4.setString(4, aif.getMainline());
                ps4.setString(5, aif.getActName());
                ps4.setString(6, aif.getSystem());
                ps4.setString(7, aif.getUserName());
                ps4.executeUpdate();
            }

        } catch (SQLException e)
        {
            log.error("updateExcelModelCopyOpertionId EXEC save IEAI_EXCELMODEL_COPY IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:修改excel信息的状态
     * @datea:2014-4-2
     * @param actId
     * @param flag
     * @throws SQLException
     * @throws RepositoryException
     */
    public void updatePreSuccCopyOpertionId ( ActInfoBean aif, Long operationid, Connection conn )
            throws  SQLException, RepositoryException
    {

        String sql1 = "UPDATE IEAI_ACTPRE_COPY T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=?";

        String sql2 = "UPDATE IEAI_ACTSUCC_COPY T SET T.IOPERATIONID=(SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?) WHERE T.IOPERATIONID=?  ";

        String sql4 = "SELECT T1.ioldoperationid FROM ieai_deloptionid T1 WHERE T1.IMAINPRONAME=? AND T1.IMAINLINENAME=? AND T1.IACTNAME=? AND T1.ISYSTEM=? AND T1.ICHILDPRONAME=?";

        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        ResultSet rs = null;
        long oldid = 0;
        try
        {

            ps3 = conn.prepareStatement(sql4);
            ps3.setString(1, aif.getProjectName());
            ps3.setString(2, aif.getMainline());
            ps3.setString(3, aif.getActName());
            ps3.setString(4, aif.getSystem());
            ps3.setString(5, aif.getChildProjectName());
            rs = ps3.executeQuery();
            while (rs.next())
            {
                oldid = rs.getLong(CIOLDOPERATIONID);
            }
            if (oldid > 0)
            {
                if (oldid != operationid)
                {
                    ps1 = conn.prepareStatement(sql1);
                    ps1.setString(1, aif.getProjectName());
                    ps1.setString(2, aif.getMainline());
                    ps1.setString(3, aif.getActName());
                    ps1.setString(4, aif.getSystem());
                    ps1.setString(5, aif.getChildProjectName());
                    ps1.setLong(6, operationid);
                    ps1.executeUpdate();

                    ps2 = conn.prepareStatement(sql2);
                    ps2.setString(1, aif.getProjectName());
                    ps2.setString(2, aif.getMainline());
                    ps2.setString(3, aif.getActName());
                    ps2.setString(4, aif.getSystem());
                    ps2.setString(5, aif.getChildProjectName());
                    ps2.setLong(6, operationid);
                    ps2.executeUpdate();
                }
            }

        } catch (SQLException e)
        {
            log.error("updatePreSuccCopyOpertionId EXEC save IEAI_EXCELMODEL_COPY IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    public List<String> saveNotMainLine ( Connection conn, Long iid, String projectName ) throws RepositoryException
    {

        String sqlPre = "SELECT DISTINCT T1.IPROJECTNAME,T1.IMAINLINENAME FROM IEAI_ACTPRE T1,IEAI_EXCELMODEL_COPY T WHERE  "
                + "T1.IOPERATIONID=T.IOPERATIONID AND T.IMAINPRONAME=? AND T1.IMAINLINENAME!=T.IMAINLINENAME AND T.IFLAG=2";
        String sqlActSucc = "SELECT DISTINCT T1.IPROJECTNAME,T1.IMAINLINENAME FROM IEAI_ACTSUCC T1,IEAI_EXCELMODEL_COPY T"
                + " WHERE  T1.IOPERATIONID=T.IOPERATIONID AND T.IMAINPRONAME=? AND T1.IMAINLINENAME!=T.IMAINLINENAME AND T.IFLAG=2";
        String sqltemp = "SELECT DISTINCT T.IMAINLINENAME FROM TMP_IEAI_EXCELMODEL T WHERE T.IMAINPRONAME=?";
        String insertBasicExcelmodel = "INSERT INTO IEAI_BASIC_EXCELMODEL (IID, IACTNO, IMAINPRONAME, ISYSNAME, ICHILDPRONAME, IACTNAME, IACTDESCRIPTION, ISDELETE, IOKFILEABSOLUTEPATH, IOKFILEFINDWEEK, ISHELLHOUSE, ISHELLABSOLUTEPATH, IOUTPUTPARAM, IBUSINEXPAUTO, IAGENTSOURCEGROUP, IAGENTCHECKGROUP, IPRI, IWEIGHT, APTGROUPNAME, APTFILENAME, ISDB2, DB2IP, IMAINLINENAME, IHEADFLAG, ISENBLE, IACTPRENO, IACTSUCCNO,EXCELPATH,USERNAME,USERID,EXCELPATHNAME,ISTATE)"
                + " SELECT  "
                + iid
                + ",1,TJ.IMAINPRONAME,TJ.ISYSTEM,TJ.ICHILDPRONAME,TJ.IACTNAME,TJ.IACTDESCRIPTION,1,'',0,'','',"
                + " TJ.IOUTPUTPARAM,TJ.IREDO,TJ.IAGENTSOURCEGROUP,TJ.ICHECKAGENTGROUP,TJ.IPRIORITY,TJ.IWEIGHTS,TJ.APTGROUPNAME,TJ.APTFILENAME,TJ.ISDB2,TJ.DB2IP,TJ.IMAINLINENAME,TJ.IHEADTAILFLAG,'','','','','',0,'',2 "
                + " FROM IEAI_EXCELMODEL_COPY TJ WHERE TJ.IOPERATIONID IN (SELECT MAX(TP.IOPERATIONID) FROM IEAI_EXCELMODEL_COPY TP"
                + "   WHERE TP.IMAINPRONAME = ? ";
        String groupBy = " AND TP.IFLAG IN(0,1) GROUP BY TP.IMAINLINENAME)";
        Map<String, String> mapMainline = new HashMap<String, String>();
        List<String> listTmpMainLine = new ArrayList<String>();
        List<String> notTmpMainLine = new ArrayList<String>();
        PreparedStatement presPre = null;
        ResultSet preRes = null;
        PreparedStatement presSucc = null;
        ResultSet succRes = null;
        PreparedStatement preTemp = null;
        ResultSet resTemp = null;
        PreparedStatement preInserbasic = null;
        String ssin = null;
        try
        {

            presPre = conn.prepareStatement(sqlPre);
            presPre.setString(1, projectName);
            preRes = presPre.executeQuery();
            while (preRes.next())
            {
                mapMainline.put(preRes.getString(IMAINLINENAME), preRes.getString(IMAINLINENAME));
            }

            presSucc = conn.prepareStatement(sqlActSucc);
            presSucc.setString(1, projectName);
            succRes = presSucc.executeQuery();
            while (succRes.next())
            {
                mapMainline.put(succRes.getString(IMAINLINENAME), succRes.getString(IMAINLINENAME));
            }
            preTemp = conn.prepareStatement(sqltemp);
            preTemp.setString(1, projectName);
            resTemp = preTemp.executeQuery();
            while (resTemp.next())
            {
                listTmpMainLine.add(resTemp.getString(IMAINLINENAME));
            }
            for (String aif : mapMainline.values())
            {
                boolean falg = false;
                for (int n = 0; n < listTmpMainLine.size(); n++)
                {
                    if (aif.equals(listTmpMainLine.get(n)))
                    {
                        falg = true;
                    }
                }
                if (!falg)
                {
                    notTmpMainLine.add(aif);
                }
            }
            if (!notTmpMainLine.isEmpty())
            {
                String[] mainlineName = new String[notTmpMainLine.size()];

                for (int n = 0; n < notTmpMainLine.size(); n++)
                {
                    mainlineName[n] = "'" + notTmpMainLine.get(n) + "'";
                }
                ssin = Conscommon.getInSql(mainlineName, " AND TP.IMAINLINENAME ", 1000);
                insertBasicExcelmodel = insertBasicExcelmodel + ssin + groupBy;
                preInserbasic = conn.prepareStatement(insertBasicExcelmodel);
                preInserbasic.setString(1, projectName);
                preInserbasic.executeUpdate();
            }
        } catch (Exception e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(preRes, presPre, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePSRS(succRes, presSucc, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePSRS(resTemp, preTemp, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(preInserbasic, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return notTmpMainLine;
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws ServerException
     * @throws NumberFormatException
     * @throws RepositoryException
     */
    public void importCopyExcelModelManager ( String fileName, String flowName, Connection conn )
            throws  SQLException, RepositoryException
    {

        String sql = "DELETE FROM IEAI_ACTPRE TA WHERE TA.IOPERATIONID IN(SELECT tl.ioperationid FROM IEAI_EXCELMODEL TL WHERE TL.IMAINPRONAME=? AND TL.IMAINLINENAME=?)";
        String sql1 = "DELETE FROM IEAI_ACTSUCC TS WHERE TS.IOPERATIONID IN(SELECT tl.ioperationid FROM IEAI_EXCELMODEL TL WHERE TL.IMAINPRONAME=? AND TL.IMAINLINENAME=?)";
        String sql2 = "DELETE FROM Ieai_Excelmodel TE WHERE TE.IMAINPRONAME=? AND TE.IMAINLINENAME=?";
        String querySql = "";
        if (JudgeDB.IEAI_DB_TYPE ==3)
        {
            querySql = "SELECT tl.ioperationid FROM IEAI_EXCELMODEL TL WHERE TL.IMAINPRONAME=? AND TL.IMAINLINENAME=?";
            sql2 = "DELETE FROM IEAI_EXCELMODEL WHERE IMAINPRONAME=? AND IMAINLINENAME=?";
        }
        String sql3= "INSERT INTO IEAI_EXCELMODEL SELECT * FROM IEAI_EXCELMODEL_COPY TCO WHERE TCO.IMAINPRONAME=? AND TCO.IMAINLINENAME=? AND TCO.IFLAG!=2";
        String sql4 = "INSERT INTO IEAI_ACTPRE (IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME,ISELFOPERATIONID) SELECT IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME,ISELFOPERATIONID FROM IEAI_ACTPRE_COPY TCY WHERE TCY.IOPERATIONID IN(SELECT TCO.IOPERATIONID FROM IEAI_EXCELMODEL_COPY TCO WHERE TCO.IMAINPRONAME=? AND TCO.IMAINLINENAME=?)";
        String sql5 = "INSERT INTO IEAI_ACTSUCC (IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME,ISELFOPERATIONID) SELECT IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME,ISELFOPERATIONID FROM Ieai_Actsucc_Copy TCY WHERE TCY.IOPERATIONID IN(SELECT TCO.IOPERATIONID FROM IEAI_EXCELMODEL_COPY TCO WHERE TCO.IMAINPRONAME=? AND TCO.IMAINLINENAME=?)";

        PreparedStatement psQuery = null;
        ResultSet rSet = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;

        try
        {
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                psQuery = conn.prepareStatement(querySql);
                psQuery.setString(1, fileName);
                psQuery.setString(2, flowName);
                rSet = psQuery.executeQuery();
                String ids = "";
                while (rSet.next())
                {
                    ids += "'" + rSet.getInt("ioperationid") + "',";
                }
                if (StringUtils.isBlank(ids))
                {
                    ids = "-1";
                } else
                {
                    ids = StringUtils.substring(ids, 0, ids.length() - 1);
                }

                sql = "DELETE FROM IEAI_ACTPRE WHERE IOPERATIONID IN(" + ids + ")";
                ps = conn.prepareStatement(sql);
                ps.executeUpdate();

                sql1 = "DELETE FROM IEAI_ACTSUCC WHERE IOPERATIONID IN(" + ids + ")";
                ps1 = conn.prepareStatement(sql1);
                ps1.executeUpdate();

                ps2 = conn.prepareStatement(sql2);
                ps2.setString(1, fileName);
                ps2.setString(2, flowName);
                ps2.executeUpdate();

                ps3 = conn.prepareStatement(sql3);
                ps3.setString(1, fileName);
                ps3.setString(2, flowName);
                ps3.executeUpdate();

                ps4 = conn.prepareStatement(sql4);
                ps4.setString(1, fileName);
                ps4.setString(2, flowName);
                ps4.executeUpdate();

                ps5 = conn.prepareStatement(sql5);
                ps5.setString(1, fileName);
                ps5.setString(2, flowName);
                ps5.executeUpdate();
            } else
            {
                ps = conn.prepareStatement(sql);
                ps.setString(1, fileName);
                ps.setString(2, flowName);
                ps.executeUpdate();

                ps1 = conn.prepareStatement(sql1);
                ps1.setString(1, fileName);
                ps1.setString(2, flowName);
                ps1.executeUpdate();

                ps2 = conn.prepareStatement(sql2);
                ps2.setString(1, fileName);
                ps2.setString(2, flowName);
                ps2.executeUpdate();

                ps3 = conn.prepareStatement(sql3);
                ps3.setString(1, fileName);
                ps3.setString(2, flowName);
                ps3.executeUpdate();

                ps4 = conn.prepareStatement(sql4);
                ps4.setString(1, fileName);
                ps4.setString(2, flowName);
                ps4.executeUpdate();

                ps5 = conn.prepareStatement(sql5);
                ps5.setString(1, fileName);
                ps5.setString(2, flowName);
                ps5.executeUpdate();
            }
        } catch (SQLException e)
        {
            log.error("importCopyExcelModelManager EXEC save IEAI_EXCELMODEL IS ERR" + e.getMessage());
            log.error("importCopyExcelModelManager EXEC save IEAI_EXCELMODEL IS ERR", e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rSet, psQuery, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps3, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps5, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * <AUTHOR>
     * @des:保存excel上载跨主线补数据
     * @datea:2017-6-10
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws RepositoryException
     */
    public void saveUpdateSupDataExcelModel ( String prjectName, String flowName, Long iid, Connection conn,
                                              int basicType ) throws  SQLException, RepositoryException
    {

        String sqlInserSupdata = "SELECT T.PROJECTNAME,T.ISYSTEM,T.MAINLINE,T.ACTNAME,T.PREPROJECTNAME,T.PREMAINLINE,T.PREACTNAME FROM IEAI_SUPDATA T WHERE T.IVESIONID=? AND T.MAINLINE=? AND T.PROJECTNAME=?";

        String sqli = "insert into ieai_actfinished_flag_new (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values ( "
                + SQLTOCHARTDATEYYMMDD
                + SQLSTIEAIEXCELMODELWHEMAI;
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqli = "insert into ieai_actfinished_flag_new (IID, IPRJID, IDATADATE, IPRONAME, IFLOWNAME, IACTNAME, IENDTIME,IOPERATIONID) values ( "
                    + SQLTOCHARTDATEYYMMDD
                    + SQLSELSYSDATERTIEAIEXCELMODELWH;
        }

        String sqlq = "select count(t.iid) as cou from ieai_actfinished_flag_new t where t.iproname=? and t.iflowname=? and t.iactname=? and t.idatadate=(to_char(to_date(?, 'YYYYMMDD') - ?, 'YYYYMMDD')) and t.ioperationid in (select t.ioperationid from ieai_excelmodel t where t.imainproname=? and t.isystem=? and t.imainlinename=? and t.iactname=?)";

        String sql5 = "select to_char(max(to_date(t2.idatadate, 'YYYYMMDD')),'YYYYMMDD') as idatadate from ieai_actfinished_flag_new t2 where t2.iproname=? and t2.iflowname=? and t2.iactname=?";

        List<ActSupData> listactSupData = new ArrayList<ActSupData>();
        PreparedStatement ps1 = null;
        PreparedStatement presinsertws = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        ResultSet rs = null;
        ResultSet rsQuery = null;
        try
        {
            presinsertws = conn.prepareStatement(sqli);
            ps4 = conn.prepareStatement(sqlq);
            ps5 = conn.prepareStatement(sql5);
            ps1 = conn.prepareStatement(sqlInserSupdata);
            ps1.setLong(1, iid);
            ps1.setString(2, flowName);
            ps1.setString(3, prjectName);
            rsQuery = ps1.executeQuery();
            while (rsQuery.next())
            {
                ActSupData actSupData = new ActSupData();
                actSupData.setProjectName(rsQuery.getString("PROJECTNAME"));
                actSupData.setSystem(rsQuery.getString("ISYSTEM"));
                actSupData.setMainline(rsQuery.getString("MAINLINE"));
                actSupData.setActname(rsQuery.getString("ACTNAME"));
                actSupData.setPreprojectname(rsQuery.getString("PREPROJECTNAME"));
                actSupData.setPremainline(rsQuery.getString("PREMAINLINE"));
                actSupData.setPreactname(rsQuery.getString("PREACTNAME"));
                listactSupData.add(actSupData);
            }
            if (!listactSupData.isEmpty())
            {
                for (int n = 0; n < listactSupData.size(); n++)
                {
                    ActSupData actSupData = listactSupData.get(n);

                    if (SystemConfig.isInsertFlag())
                    {
                        String datadate = "";
                        ps5.setString(1, actSupData.getPreprojectname());
                        ps5.setString(2, actSupData.getPremainline());
                        ps5.setString(3, actSupData.getPreactname());
                        int forcount = 0;
                        try
                        {
                            rs = ps5.executeQuery();
                            forcount = SystemConfig.getInsertData();
                            while (rs.next())
                            {
                                datadate = rs.getString(CIDATADATE);
                            }
                        } catch (Exception e)
                        {
                            log.info("saveUpdateSupDataExcelModel is get datadate is error:" + e.getMessage());
                        }

                        if (!"".equals(datadate) && null != datadate)
                        {
                            for (int ii = 0; ii <= forcount; ii++)
                            {
                                int count = 0;
                                ps4.setString(1, actSupData.getPreprojectname());
                                ps4.setString(2, actSupData.getPremainline());
                                ps4.setString(3, actSupData.getPreactname());
                                ps4.setString(4, datadate);
                                ps4.setInt(5, ii);
                                ps4.setString(6, actSupData.getProjectName());
                                ps4.setString(7, actSupData.getSystem());
                                ps4.setString(8, actSupData.getMainline());
                                ps4.setString(9, actSupData.getActname());
                                rs = ps4.executeQuery();
                                while (rs.next())
                                {
                                    count = rs.getInt("cou");
                                }
                                if (count <= 0)
                                {
                                    long actFlagId = IdGenerator.createIdNoConnection(IEAIACTFINISHEDFLAGNEW,
                                            basicType);
                                    presinsertws.setLong(1, actFlagId);
                                    presinsertws.setLong(2, ii + 1L);
                                    presinsertws.setString(3, datadate);
                                    presinsertws.setInt(4, ii);
                                    presinsertws.setString(5, actSupData.getPreprojectname());
                                    presinsertws.setString(6, actSupData.getPremainline());
                                    presinsertws.setString(7, actSupData.getPreactname());
                                    presinsertws.setString(8, actSupData.getProjectName());
                                    presinsertws.setString(9, actSupData.getSystem());
                                    presinsertws.setString(10, actSupData.getMainline());
                                    presinsertws.setString(11, actSupData.getActname());
                                    presinsertws.addBatch();
                                }
                            }
                        }
                    }

                }
            }
            if (!listactSupData.isEmpty() && SystemConfig.isInsertFlag())
            {
                presinsertws.executeBatch();
            }
        } catch (SQLException e)
        {
            log.error("saveUpdateSupDataExcelModel EXEC save IEAI_SUPDATA IS ERR" + e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps4, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps5,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(presinsertws, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rs,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closeResultSet(rsQuery, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    public boolean startDbUtilWorkFlowUploadExcel ( Connection conn, long iid, String projectName, String flowName,
                                                    Map<String, List<ActInfoBean>> mapListbean, int basicType )
    {
        String deleteFlaginside = "";
        String deleteLag = "";
        Map<String, ActInfoBean> mapList = new HashMap<String, ActInfoBean>();
        List<ActInfoBean> listbean = new ArrayList<ActInfoBean>();
        boolean flag = true; //
        long startTime = System.currentTimeMillis();
        try
        {
            listbean = mapListbean.get(String.valueOf(iid));
            for (int n = 0; n < listbean.size(); n++)
            {

                ActInfoBean actInfoBean = listbean.get(n);
                if ("2".equals(actInfoBean.getDeleteFlag()))
                {
                    if (flowName.equals(actInfoBean.getMainline()))
                    {
                        deleteFlaginside = "2";
                    } else
                    {
                        if (!"2".equals(deleteFlaginside))
                        {
                            deleteFlaginside = "3";
                        }
                    }

                }
                mapList.put(actInfoBean.getActNo(), actInfoBean);
            }
            if ("2".equals(deleteFlaginside))
            {
                deleteLag = "del";
            }
            if ("3".equals(deleteFlaginside))
            {
                return flag;
            }
            // fileName
            ActInfoBean actInfoBean = new ActInfoBean();
            for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet())
            {
                actInfoBean = entry.getValue();
                break;
            }

            // 保存校验完的数据到excel表中
            long startUploadExcel = System.currentTimeMillis();
            importDbUtilExcelModelManager(mapList,  flowName, deleteLag,
                    conn, basicType);
            log.info("启动工作流导入startDbUtilWorkFlowUploadExcel为：" + ((System.currentTimeMillis()) - startUploadExcel)
                    + " 毫秒" + DESCPROJECTNAME + projectName + DESCFLOWNAME + flowName);
            log.info("Excel导入功能耗时流水：Excel导入成功,操作人 " + actInfoBean.getUserName());
            long endTime = System.currentTimeMillis();

            log.info("***************上传结束*****************共用时：" + (endTime - startTime) + "毫秒");
        } catch (Exception e)
        {
            log.info("startDbUtilWorkFlowUploadExcel is save excelmodel error" + e.getMessage());
            flag = false;
        }
        return flag;
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws ServerException
     * @throws NumberFormatException
     * @throws RepositoryException
     */
    public void importDbUtilExcelModelManager ( Map<String, ActInfoBean> mapList, String flowName,
                                                String del, Connection conn, int basicType )
            throws  SQLException, RepositoryException
    {

        if (!del.equals("del"))
        {
            List<RepWorkflowInstance> addbeanList = new ArrayList<RepWorkflowInstance>();
            List<RepWorkflowInstance> deletebeanList = new ArrayList<RepWorkflowInstance>();
            for (ActInfoBean aif : mapList.values())
            {
                if (flowName.equals(aif.getMainline()))
                {
                    Map returnMap = saveDbUtilUpdateDisableExcelModel(aif);

                    if (returnMap.get(CADDBEAN) != null)
                    {
                        addbeanList.add((RepWorkflowInstance) returnMap.get(CADDBEAN));
                    }
                    if (returnMap.get(CDELETEBEAN) != null)
                    {
                        deletebeanList.add((RepWorkflowInstance) returnMap.get(CDELETEBEAN));
                    }
                }
            }

            try
            {
                if (!addbeanList.isEmpty())
                {
                    this.saveDisableRuleForBatch(addbeanList, conn, basicType);
                }
                if (!deletebeanList.isEmpty())
                {
                    this.deleteDisableRuleForBatch(deletebeanList, conn);
                }
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
            }
        }
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws RepositoryException
     */
    public Map saveDbUtilUpdateDisableExcelModel ( ActInfoBean aif)
    {
        Map returnMap = new HashMap();
        RepWorkflowInstance conBean = new RepWorkflowInstance();

        conBean.setProjectName(aif.getChildProjectName());
        conBean.setFlowName(aif.getActName());
        conBean.set_isystem(aif.getSystem());
        conBean.set_isetUser(aif.getUserName());

        if (null != aif.getDisableFlag() && "1".equals(aif.getDisableFlag()))
        {
            // 新增禁用记录
            returnMap.put(CADDBEAN, conBean);
        } else if (null != aif.getDisableFlag() && "0".equals(aif.getDisableFlag()))
        {
            // 删除禁用记录
            returnMap.put(CDELETEBEAN, conBean);
        }
        return returnMap;
    }

    /**
     * <AUTHOR>
     * @des:保存excel及依赖和触发的信息
     * @datea:2014-4-2
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws RepositoryException
     */
    public Long saveExcelModelCopy ( Map<String, ActInfoBean> mapList, Long iid, Connection conn,
                                     int basicType ) throws  SQLException, RepositoryException
    {
        Long proid = null;
        String sql = "";
        if(Environment.getInstance().getGYBankSwitch()){
            sql ="insert into IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + SQLINSERTIEAIEXCELMODELVALCOPYGY
                    + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        }else if(Environment.getInstance().getDGBankSwitch()){
            sql ="insert into IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + SQLINSERTIEAIEXCELMODELVALCOPYDG
                    + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        }else{
            sql ="insert into IEAI_EXCELMODEL_COPY(IOPERATIONID,IMAINPRONAME,IMAINLINENAME,IHEADTAILFLAG,IAGENTSOURCEGROUP,"
                    + SQLINSERTIEAIEXCELMODELVAL
                    + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        }


        String sql1 = "insert into IEAI_ACTPRE_COPY(IOPERATIONID,IPREACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

        String sql2 = "insert into IEAI_ACTSUCC_COPY(IOPERATIONID,ISUCCACTNAME,ICHILDPROJECTNAME,IPROJECTNAME,IMAINLINENAME) values(?,?,?,?,?)";

        String sql22 = "insert into ieai_supdata (IVESIONID,projectname, isystem, mainline, actname, preprojectname, premainline, preactname) values (?,?, ?, ?, ?, ?, ?, ?) ";
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps6 = null;


        int index=0;
        int index1=0;
        int index2=0;
        try
        {
            ps1 = conn.prepareStatement(sql1);
            ps2 = conn.prepareStatement(sql2);
            ps6 = conn.prepareStatement(sql22);
            for (ActInfoBean aif : mapList.values())
            {
                ps = conn.prepareStatement(sql);
                long id = generCountExcelModel(basicType);
                ps.setLong(1, id);
                ps.setString(2, aif.getProjectName());
                ps.setString(3, aif.getMainline());
                if ("总头".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 1);
                } else if ("主线头".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 2);
                } else if ("总尾".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 6);
                } else if ("主线尾".equals(aif.getSEFlag()))
                {
                    ps.setLong(4, 7);
                } else
                {
                    ps.setLong(4, 0);
                }
                ps.setString(5, aif.getAgentGropName());
                ps.setString(6, aif.getOKFileABPath());
                ps.setString(7, "数据日期");
                ps.setString(8, aif.getChildProjectName());
                ps.setLong(9, Long.valueOf(aif.getDeleteFlag()));
                ps.setString(10, aif.getActName());
                ps.setString(11, aif.getDescribe());
                ps.setString(12, aif.getOutputPamaeter());
                ps.setString(13, aif.getShellCrust());
                ps.setString(14, aif.getShellABPath());
                if (null == aif.getOKFileABPath() || "".equals(aif.getOKFileABPath()))
                {
                    ps.setString(15, "0");
                } else
                {
                    ps.setString(15, "0,nf");
                }
                if (null == aif.getWeights() || "".equals(aif.getWeights()))
                {
                    ps.setLong(16, 1);
                } else
                {
                    ps.setLong(16, Long.valueOf(aif.getWeights()));
                }
                if (null == aif.getPriority() || "".equals(aif.getPriority()))
                {
                    ps.setLong(17, 1);
                } else
                {
                    ps.setLong(17, Long.valueOf(aif.getPriority()));
                }
                if (null == aif.getOKFileFindWeek() || "".equals(aif.getOKFileFindWeek()))
                {
                    ps.setLong(18, 0);
                } else
                {
                    ps.setLong(18, Long.valueOf(aif.getOKFileFindWeek()));
                }
                ps.setLong(19, Long.valueOf("1"));
                ps.setString(20, aif.getCheckAgentGropName());
                // apt
                ps.setString(21, aif.getAptGroupName());
                ps.setString(22, aif.getAptFileName());
                ps.setString(23, aif.getIsDB2());
                ps.setString(24, aif.getDb2IP());
                ps.setString(25, aif.getAptResGroupname());
                // apt
                ps.setString(26, aif.getSystem());

                if ("是".equals(aif.getRedo().trim()))
                {
                    ps.setString(27, "1");
                } else
                {
                    ps.setString(27, "0");
                }
                ps.setInt(28, aif.isAgentGroup() ? 0 : 1);
                ps.setString(29, aif.getDelayTime());
                ps.setString(30, aif.getBranchCondition());
                if (null != aif.getReTryCount() && !"".equals(aif.getReTryCount())
                        && !"null".equals(aif.getReTryCount()))
                {
                    ps.setInt(31, Integer.parseInt(aif.getReTryCount()));// ,IRETRYNUM,ICALENDNAME

                } else
                {
                    ps.setInt(31, 0);
                }
                ps.setString(32, aif.getCalendName());
                // ,IRETRYNUM,ICALENDNAME
                if (null != aif.getReTryTime() && !"".equals(aif.getReTryTime()) && !"null".equals(aif.getReTryTime()))
                {
                    ps.setInt(33, Integer.parseInt(aif.getReTryTime()));//

                } else
                {
                    ps.setInt(33, 0);
                }
                ps.setString(34, aif.getReTryEndTime());
                ps.setString(35, aif.getSkip());
                ps.setString(36, aif.getDelayWarnning());
                if(Environment.getInstance().getGYBankSwitch()){
                    ps.setInt(37, Integer.parseInt(aif.getActNo()));
                }else if(Environment.getInstance().getDGBankSwitch()){
                    ps.setString(37, aif.getActParams());
                }
                ps.addBatch();

                index++;
                if(index%1000==0){
                    ps.executeBatch();
                }
                if (null != ps)
                {
                    try
                    {
                        ps.close();
                    } catch (SQLException e)
                    {
                        log.error("saveExcelModelCopy is error at UpLoadExcelManager  ps.close() " + e.getMessage());
                        ps = null;
                    }
                    ps = null;
                }
                updateExcelModelCopyOpertionId(aif, id, conn);
                List<String> listBefore = aif.getBeforeActList();

                for (int j = 0; j < listBefore.size(); j++)
                {
                    String before = listBefore.get(j);
                    ActInfoBean aifInfoBean = mapList.get(before);
                    ps1.setLong(1, id);
                    ps1.setString(2, aifInfoBean.getActName());
                    ps1.setString(3, aifInfoBean.getChildProjectName());
                    ps1.setString(4, aifInfoBean.getProjectName());
                    ps1.setString(5, aifInfoBean.getMainline());
                    ps1.addBatch();

                    index1++;
                    if(index1%2000==0){
                        ps1.executeBatch();
                    }
                    if (!aif.getMainline().equals(aifInfoBean.getMainline()))
                    {

                        ps6.setLong(1, iid);
                        ps6.setString(2, aif.getProjectName());
                        ps6.setString(3, aif.getSystem());
                        ps6.setString(4, aif.getMainline());
                        ps6.setString(5, aif.getActName());
                        ps6.setString(6, aifInfoBean.getProjectName());
                        ps6.setString(7, aifInfoBean.getMainline());
                        ps6.setString(8, aifInfoBean.getActName());
                        ps6.addBatch();
                        if(index1%2000==0){
                            ps6.executeBatch();
                        }
                    }

                }
                List<String> listAfter = aif.getAfterActList();
                for (int j = 0; j < listAfter.size(); j++)
                {
                    String after = listAfter.get(j);
                    ActInfoBean aifInfoBean = mapList.get(after);
                    ps2.setLong(1, id);
                    ps2.setString(2, aifInfoBean.getActName());
                    ps2.setString(3, aifInfoBean.getChildProjectName());
                    ps2.setString(4, aifInfoBean.getProjectName());
                    ps2.setString(5, aifInfoBean.getMainline());
                    ps2.addBatch();

                    index2++;
                    if(index2%2000==0){
                        ps2.executeBatch();
                    }
                }

                ps.executeBatch();
                ps1.executeBatch();
                ps2.executeBatch();
                ps6.executeBatch();
                updatePreSuccCopyOpertionId(aif, id, conn);



            }
        } catch (SQLException e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps2, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(ps6, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return proid;
    }

    public boolean uploadProjectExcelEnd ( Connection conn, String projectName, String flowName, UserInfo userinfo,
                                           ProjectSaveUtilBean projectSaveUtilBean, int basicType )
            throws RepositoryException
    {
        long startTime = System.currentTimeMillis();
        boolean flag = true; //
        try
        {
            StartCreatePrj oo = new StartCreatePrj();
            Object[] object = oo.getProjectInfo(projectName, conn);
            boolean fag = TaskUploadManager.getInstance().saveStartflowEndFlowUploadExcelEnd(object, userinfo,
                    projectSaveUtilBean, conn, basicType);
            if (!fag)
            {
                log.info("uploadProjectExcel is saveProject is error: " + "组织工程信息入库失败,请重新导入。");
                flag = false;
            }
            long endTime = System.currentTimeMillis();
            log.info("启动工作流导入工程***************上传结束*****************共用时：" + (endTime - startTime) + "毫秒"
                    + DESCPROJECTNAME + projectName + DESCFLOWNAME + flowName);

        } catch (Exception e)
        {
            log.info("uploadProjectExcel is save excelmodel error" + e.getMessage());
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        }
        return flag;
    }

    public boolean startWorkFlowUploadExcel ( Connection conn, long iid, String projectName, String flowName,
                                              Map<String, List<ActInfoBean>> mapListbean, int basicType ) throws RepositoryException
    {
        String deleteFlaginside = "";
        String deleteLag = "";
        Map<String, ActInfoBean> mapList = new HashMap<String, ActInfoBean>();
        List<ActInfoBean> listbean = new ArrayList<ActInfoBean>();
        boolean flag = true; //
        long startTime = System.currentTimeMillis();
        try
        {
            listbean = mapListbean.get(String.valueOf(iid));
            for (int n = 0; n < listbean.size(); n++)
            {
                ActInfoBean actInfoBean = listbean.get(n);
                if ("2".equals(actInfoBean.getDeleteFlag()))
                {
                    if (flowName.equals(actInfoBean.getMainline()))
                    {
                        deleteFlaginside = "2";
                    } else
                    {
                        if (!"2".equals(deleteFlaginside))
                        {
                            deleteFlaginside = "3";
                        }
                    }

                }
                mapList.put(actInfoBean.getActNo(), actInfoBean);
            }
            if ("2".equals(deleteFlaginside))
            {
                deleteLag = "del";
            }
            if ("3".equals(deleteFlaginside))
            {
                return flag;
            }
            // fileName
            ActInfoBean actInfoBean = new ActInfoBean();
            for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet())
            {
                actInfoBean = entry.getValue();
                break;
            }
            // 保存校验完的数据到excel表中
            long startUploadExcel = System.currentTimeMillis();
            importDbUtilExcelModelManager(mapList, flowName, deleteLag,
                    conn, basicType);
            log.info("启动工作流导入importExcelModelManager为：" + ((System.currentTimeMillis()) - startUploadExcel) + " 毫秒"
                    + DESCPROJECTNAME + projectName + DESCFLOWNAME + flowName);
            log.info("Excel导入功能耗时流水：Excel导入成功,操作人 " + actInfoBean.getUserName());
            long endTime = System.currentTimeMillis();
            log.info("***************上传结束*****************共用时：" + (endTime - startTime) + "毫秒");
        } catch (Exception e)
        {
            log.info("startWorkFlowUploadExcel is save excelmodel error" + e.getMessage());
            flag = false;
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        }
        return flag;
    }

    private static final Logger log = Logger.getLogger(UpLoadExcelManager.class);

    public int queryCountForConfig ( String flowname, String actname, String system )
    {
        String sql = "";
        int count = 0;
        PreparedStatement queryProjectsSt = null;
        ResultSet rs = null;
        Connection con =null;
        sql = "SELECT count(*) counts from IEAI_TIME_DELAY_CAOFIG where IFLOWNAME=? and IACTNAME = ? and isystem = ?";
        try
        {
            con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, 1);
            queryProjectsSt = con.prepareStatement(sql);
            queryProjectsSt.setString(1, flowname);
            queryProjectsSt.setString(2, actname);
            queryProjectsSt.setString(3, system);
            rs = queryProjectsSt.executeQuery();
            if (rs.next())
            {
                count=rs.getInt("counts");
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }finally{
            DBResource.closeConn(con, rs, queryProjectsSt, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }

    public int deleteConfig ( String flowname, String actname, String system )
    {
        String sql = "";
        int count = 0;
        PreparedStatement queryProjectsSt = null;
        ResultSet rs = null;
        Connection con =null;
        sql = "delete from IEAI_TIME_DELAY_CAOFIG where IFLOWNAME=? and IACTNAME = ? and isystem = ?";
        try
        {
            con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, 1);
            queryProjectsSt = con.prepareStatement(sql);
            queryProjectsSt.setString(1, flowname);
            queryProjectsSt.setString(2, actname);
            queryProjectsSt.setString(3, system);
            queryProjectsSt.executeUpdate();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }finally{
            DBResource.closeConn(con, rs, queryProjectsSt, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return count;
    }
    /**
     * @Title: getExcelPerformUser
     * @Description: 获取执行用户
     * @return
     * @throws RepositoryException
     */
    public String getExcelPerformUser ( String actName ) throws RepositoryException
    {
        String timeStr = "";
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0; ; i++) {
            Connection con = null;
            PreparedStatement ps1 = null;
            ResultSet rs1 = null;
            con = DBResource.getConnection(method, log, Constants.IEAI_IEAI);
            try {
                ps1 = con.prepareStatement("select PERFORMUSER from IEAI_EXCELMODEL d  WHERE  d.IACTNAME=? ");
                ps1.setString(1, actName);
                rs1 = ps1.executeQuery();
                while (rs1.next()) {
                    timeStr = rs1.getString("PERFORMUSER");
                    break;
                }
                break;
            } catch (SQLException e) {
                log.error(method + " is error at getExcelPerformUser ", e);
                DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
            } finally {
                DBResource.closeConn(con, rs1, ps1, method, log);
            }
        }
        return timeStr;
    }
    public List<Map> getOldIoperationid ( List<Map> existCrossAct, Connection conn,Connection baseConn )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Map> oldOperationidList = new ArrayList();
        long ioldOperationid = 0;
        long inewOperationid = 0;

        StringBuilder sql = new StringBuilder("select ioperationid from ieai_excelmodel where imainproname = ? and imainlinename = ? and iactname = ?");
        StringBuilder newSql = new StringBuilder("select ioperationid from ieai_excelmodel_copy where imainproname = ? and imainlinename = ? and iactname = ?");

        PreparedStatement ps = null;
        ResultSet rs = null;

        PreparedStatement basePs = null;
        ResultSet baseRs = null;
        try
        {
            ps = conn.prepareStatement(sql.toString());
            basePs = baseConn.prepareStatement(newSql.toString());
            for(Map actInfoBean : existCrossAct){
                ps.setString(1, String.valueOf(actInfoBean.get("IMAINPRONAME")));
                ps.setString(2, String.valueOf(actInfoBean.get("IMAINLINENAME")));
                ps.setString(3, String.valueOf(actInfoBean.get("IACTNAME")));
                rs = ps.executeQuery();
                while(rs.next()){
                    ioldOperationid = rs.getLong("ioperationid");
                }
                //如果没有老的id，证明是新的作业
                if(ioldOperationid != 0){
                    basePs.setString(1, String.valueOf(actInfoBean.get("IMAINPRONAME")));
                    basePs.setString(2, String.valueOf(actInfoBean.get("IMAINLINENAME")));
                    basePs.setString(3, String.valueOf(actInfoBean.get("IACTNAME")));
                    baseRs = basePs.executeQuery();
                    while(baseRs.next()){
                        inewOperationid = baseRs.getLong("ioperationid");
                    }
                    actInfoBean.put("IOLDOPERATIONID", ioldOperationid);
                    actInfoBean.put("INEWOPERATIONID", inewOperationid);
                    log.info(actInfoBean.get("IACTNAME")+":oldid:"+ioldOperationid+":newid:"+inewOperationid);
                    if(ioldOperationid != inewOperationid){
                        oldOperationidList.add(actInfoBean);
                    }
                }
            }
        } catch (SQLException e)
        {
            log.error(method + " is error at getOldIoperationid ", e);
        } finally {
            DBResource.closePSRS(rs, ps, "getOldIoperationid is error", log);
            DBResource.closePSRS(baseRs, basePs, "getOldIoperationid is error", log);
        }
        return oldOperationidList;
    }

    public void insertFinishFlag ( Connection csconn,Connection baseConn ,List<Map> changeActList )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        ResultSet rs = null;

        List<Map> insertList = new ArrayList();
        StringBuilder sbsql = new StringBuilder("select iprjid,idatadate,iproname,iflowname,iactname,iendtime,ioperationid from ieai_actfinished_flag_new where"
                + " iproname =? and iflowname = ? and iactname = ? ");
        StringBuilder sbsqlMid = new StringBuilder(" and ioperationid = ? ");
        StringBuilder sbsqlMidTwo = new StringBuilder("and iendtime = (select max(iendtime) from ieai_actfinished_flag_new where iproname =? ");
        StringBuilder sbsqlEnd = new StringBuilder(" and iflowname = ? and  iactname = ?) order by iid desc");

        try
        {
            for(Map bean:changeActList){
                int index = 1;
                ps = csconn.prepareStatement(sbsql.append(sbsqlMid).append(sbsqlMidTwo).append(sbsqlMid).append(sbsqlEnd).toString());
                ps.setString(index++, String.valueOf(bean.get("IDEPMAINPRONAME")));
                ps.setString(index++, String.valueOf(bean.get("IDEPMAINLINENAME")));
                ps.setString(index++, String.valueOf(bean.get("IDEPACTNAME")));
                ps.setLong(index++, Long.parseLong(String.valueOf(bean.get("IOLDOPERATIONID"))));
                ps.setString(index++, String.valueOf(bean.get("IDEPMAINPRONAME")));
                ps.setLong(index++, Long.parseLong(String.valueOf(bean.get("IOLDOPERATIONID"))));
                ps.setString(index++, String.valueOf(bean.get("IDEPMAINLINENAME")));
                ps.setString(index++, String.valueOf(bean.get("IDEPACTNAME")));

                rs = ps.executeQuery();
                while(rs.next()){
                    Map finishBean = new HashMap();
                    finishBean.put("iprjid", rs.getLong("iprjid"));
                    finishBean.put("idatadate", rs.getString("idatadate"));
                    finishBean.put("iproname", rs.getString("iproname"));
                    finishBean.put("iflowname", rs.getString("iflowname"));
                    finishBean.put("iactname", rs.getString("iactname"));
                    finishBean.put("iendtime", rs.getString("iendtime"));
                    finishBean.put("ioperationid", bean.get("INEWOPERATIONID"));
                    insertList.add(finishBean);
                    break;
                }
                rs.close();
                ps.close();
            }
            this.insertFinishFlag(insertList, baseConn);
        } catch (SQLException  e)
        {
            log.error(method + " is error at insertFinishFlag ", e);
        }  finally {
            DBResource.closePSRS(rs, ps, "insertFinishFlag is error", log);
        }
    }

    private void insertFinishFlag(List<Map> insertList,Connection conn){
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        StringBuilder insertSql = new StringBuilder("insert into ieai_actfinished_flag_new values(?,?,?,?,?,?,?,?)");
        PreparedStatement insertPs = null;

        try
        {
            insertPs = conn.prepareStatement(insertSql.toString());
            for(Map bean:insertList){
                long finishFlagId = IdGenerator.createIdNoConnection(IEAIACTFINISHEDFLAGNEW, Constants.IEAI_IEAI);
                insertPs.setLong(1, finishFlagId);
                insertPs.setLong(2, Long.parseLong(String.valueOf(bean.get("iprjid"))));
                insertPs.setString(3, String.valueOf(bean.get("idatadate")));
                insertPs.setString(4, String.valueOf(bean.get("iproname")));
                insertPs.setString(5, String.valueOf(bean.get("iflowname")));
                insertPs.setString(6, String.valueOf(bean.get("iactname")));
                insertPs.setString(7, String.valueOf(bean.get("iendtime")));
                insertPs.setLong(8, Long.parseLong(String.valueOf(bean.get("ioperationid"))));
                insertPs.execute();
                log.info("插入FinishFlagNew表中，ioperationid：" + Long.parseLong(String.valueOf(bean.get("ioperationid"))));
            }
        } catch (SQLException | RepositoryException e)
        {
            log.error(method + " is error at private insertFinishFlag ", e);
        } finally {
            DBResource.closePreparedStatement(insertPs, "private insertFinishFlag is error", log);
        }
    }

}
