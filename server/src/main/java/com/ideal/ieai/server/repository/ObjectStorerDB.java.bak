package com.ideal.ieai.server.repository;

import com.ideal.ieai.commons.AppLogFilter;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.report.ActRunInfoReportCriteria;
import com.ideal.ieai.commons.report.ActRunUseTimeReportCriteria;
import com.ideal.ieai.commons.report.FlowDefReportCriteria;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.pack.PackException;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.ieaikernel.CommonConfigEnv;
import com.ideal.ieai.server.repository.activity.RepActExecTimeConfig;
import com.ideal.ieai.server.repository.activity.RepActivityRuntime;
import com.ideal.ieai.server.repository.calendar.*;
import com.ideal.ieai.server.repository.cluster.RepServiceNode;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.engine.*;
import com.ideal.ieai.server.repository.errortask.RepDelegatingDetail;
import com.ideal.ieai.server.repository.errortask.RepErrorTask;
import com.ideal.ieai.server.repository.errortask.RepErrorTaskOperation;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.lob.TextEntity;
import com.ideal.ieai.server.repository.log.app.RepAppLogRecord;
import com.ideal.ieai.server.repository.log.session.RepSession;
import com.ideal.ieai.server.repository.mailserver.RepMailServerSetting;
import com.ideal.ieai.server.repository.message.RepDestinationInfo;
import com.ideal.ieai.server.repository.notice.RepNotice;
import com.ideal.ieai.server.repository.notice.RepNoticeConfirmedUser;
import com.ideal.ieai.server.repository.notice.RepNoticeOperation;
import com.ideal.ieai.server.repository.permission.RepRolePermission;
import com.ideal.ieai.server.repository.permission.RepUserPermission;
import com.ideal.ieai.server.repository.project.*;
import com.ideal.ieai.server.repository.task.*;
import com.ideal.ieai.server.repository.user.*;
import com.ideal.ieai.server.repository.workflow.RepAttachment;
import com.ideal.ieai.server.repository.workflow.RepFlowPoolNum;
import com.ideal.ieai.server.repository.workflow.RepValidTime;
import com.ideal.util.Security;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapListHandler;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.Serializable;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
public class ObjectStorerDB
{

    private static final Logger _log = Logger.getLogger(ObjectStorerDB.class);

    /**
     * 查找符合条件的记录数
     * 
     * @param tableName
     * @param fieldName
     * @param vaules
     * @return
     */
    public static final int countWithRetry ( String tableName, String fieldName, String vaules )
    {
        int count = 0;
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rset = null;
        String sql = "select count(*) from " + tableName + " info where info." + fieldName + "='" + vaules + "'";

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.prepareStatement(sql);
            rset = stmt.executeQuery();
            while (rset.next())
                count = Integer.parseInt(rset.getString(1));
            stmt.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rset, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return count;
    }

    /**
     * 查找符合条件的记录数
     * 
     * @param tableName
     * @param ids
     * @return
     */
    public static final int countWithRetry ( String tableName, Serializable[] ids )
    {
        int count = 0;
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rset = null;

        StringBuilder sql = new StringBuilder("select count(*) from " + tableName + " info where info.id in (");
        int counts = ids.length;
        for (int i = 0; i < counts; i++)
        {
            sql.append('?');
            if (i < counts - 1)
            {
                sql.append(',');
            }
        }
        sql.append(')');

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.prepareStatement(sql.toString());
            rset = stmt.executeQuery();
            while (rset.next())
                count = Integer.parseInt(rset.getString(1));
            stmt.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rset, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return count;
    }

    /**
     * 分页查找ieai_flowpoolnum表数据
     * 
     * @param sql
     * @return
     */
    public static final List findWithRetryRepFlowPoolNum ( String sql, int type )
    {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rsr = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.prepareStatement(sql);
            rsr = stmt.executeQuery();
            while (rsr.next())
            {
                RepFlowPoolNum bean = new RepFlowPoolNum();
                bean.setId(rsr.getLong("iid"));
                bean.setAgentIP(rsr.getString("iagentIP"));
                bean.setPrjFlowName(rsr.getString("iprjFlowName"));
                bean.setFlowPoolNum(rsr.getString("iflowPoolNum"));
                list.add(bean);
            }
            stmt.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rsr, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 分页查询工作流运行统计信息
     * 
     * @param sql
     * @return
     */
    static public final List findWithRetryRepActRunInfo ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rsr = null;
        List list = new ArrayList();
        Map result = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rsr = stmt.executeQuery(sql);
            while (rsr.next())
            {
                result = new HashMap();
                result.put("count", rsr.getString(1));
                result.put("state", rsr.getString(2));
                list.add(result);
            }
            stmt.close();
            rsr.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rsr, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 查询角色id和name
     * 
     * @param sql
     * @return
     */
    public static final List findWithRetryRepRole ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rsr = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rsr = stmt.executeQuery(sql);
            while (rsr.next())
            {
                RepRole repRole = new RepRole();
                repRole.setId(new Long(rsr.getLong("iid")));
                repRole.setName(rsr.getString("iname"));
                list.add(repRole);
            }
            stmt.close();
            rsr.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rsr, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    public static final List findTaskRuntimeAndTaskConstant ( String sql, int sysType )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map mapBean = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                mapBean = new HashMap();
                RepTaskRuntime repTaskRuntime = new RepTaskRuntime();

                repTaskRuntime.setId(new Long(rs.getLong("runtimeiid")));
                repTaskRuntime.setAcquireTime(rs.getLong("iacquiretime"));
                repTaskRuntime.setDeadLine(rs.getLong("ideadline"));
                repTaskRuntime.setDelegateTime(rs.getLong("idelegatetime"));
                repTaskRuntime.setDelegateUserFullName(rs.getString("idelegateuser"));
                repTaskRuntime.setForwardTime(rs.getLong("iforwordtime"));
                repTaskRuntime.setForwardUserFullName(rs.getString("iforwarduser"));
                repTaskRuntime.setLastAlertTime(rs.getLong("ilastalttime"));
                repTaskRuntime.setLastOperateTime(rs.getLong("ilastoptime"));
                repTaskRuntime.setLastOperation(rs.getString("ilastop"));
                repTaskRuntime.setOldForwardTime(rs.getLong("ioldfwdtime"));
                repTaskRuntime.setOldForwardUserFullName(rs.getString("ioldfwduser"));
                repTaskRuntime.setState(rs.getString("istate"));
                repTaskRuntime.setTaskOwnerFullName(rs.getString("iowner"));
                repTaskRuntime.setAcquired(rs.getLong("iacquired") == 1 ? true : false);
                repTaskRuntime.setTaskEnd(rs.getLong("itaskend") == 1 ? true : false);
                repTaskRuntime.setFailBeginTime(rs.getLong("ifailbegintime"));
                repTaskRuntime.setFailEndTime(rs.getLong("ifailendtime"));
                repTaskRuntime.setDelegateUserId(new Long(rs.getLong("idelegateuserid")));
                repTaskRuntime.setForwardUserId(new Long(rs.getLong("iforwarduserid")));
                repTaskRuntime.setOldForwardUserId(new Long(rs.getLong("ioldfwduserid")));
                repTaskRuntime.setTaskOwnerId(new Long(rs.getLong("iownerid")));
                mapBean.put("taskRuntime", repTaskRuntime);

                RepTaskConstant repTaskConstant = new RepTaskConstant();

                repTaskConstant.setId(new Long(rs.getLong("taskiid")));
                repTaskConstant.setActivityName(rs.getString("iactname"));
                repTaskConstant.setCalendarName(rs.getString("icalendarname"));
                repTaskConstant.setDesc(rs.getString("idesc"));
                repTaskConstant.setFlowId(new Long(rs.getLong("taskflowid")));
                repTaskConstant.setRemarkId(new Long(rs.getLong("iremarkid")));
                repTaskConstant.setName(rs.getString("iname"));
                repTaskConstant.setPriority(rs.getInt("ipriority"));
                repTaskConstant.setStartTime(rs.getLong("taskstarttime"));
                repTaskConstant.setAllowSkip(rs.getLong("iallowskip") == 1 ? true : false);
                repTaskConstant.setDelegateToAssigneeOnly(rs.getLong("ideltoassonly") == 1 ? true : false);
                repTaskConstant.setForwardToAssigneeOnly(rs.getLong("ifwdtoassonly") == 1 ? true : false);
                repTaskConstant.setUsingFlowParticipant(rs.getLong("iusingflowpart") == 1 ? true : false);
                repTaskConstant.setRecovered(rs.getLong("iisrecovered") == 1 ? true : false);
                mapBean.put("taskConstant", repTaskConstant);
                list.add(mapBean);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取用户ID和FullName
     * 
     * @param sql
     * @return
     */
    public static final List findWithRetryRepUser ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rsu = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rsu = stmt.executeQuery(sql);
            while (rsu.next())
            {
                RepUser repUser = new RepUser();
                repUser.setId(new Long(rsu.getLong("iid")));
                repUser.setFullName(rsu.getString("ifullname"));
                list.add(repUser);
            }
            stmt.close();
            rsu.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rsu, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取消息ID
     * 
     * @param sql
     * @return
     */
    static public final List findWithRetryRepNotice ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepNotice repNotice = new RepNotice();
                repNotice.setAuthorFullName(rs.getString("iauthorName"));
                repNotice.setAuthorId(new Long(rs.getLong("iauthorId")));
                repNotice.setBeginSecondOfDay(rs.getInt("ibeginTime"));
                repNotice.setContentId(new Long(rs.getLong("icontentId")));
                repNotice.setCreatedTime(rs.getLong("icreatedTime"));
                repNotice.setEndSecondOfDay(rs.getInt("iendTime"));
                repNotice.setFeedback(rs.getLong("ifeedback") == 1 ? true : false);
                repNotice.setFlowInstanceNamePattern(rs.getString("iflowNamePattern"));
                repNotice.setFlowName(rs.getString("iflowName"));
                repNotice.setFlowStartTimeBegin(rs.getLong("iflowStartTimeBegin"));
                repNotice.setFlowStartTimeEnd(rs.getLong("iflowStartTimeEnd"));
                repNotice.setId(new Long(rs.getLong("iid")));
                repNotice.setInvalidatorFullName(rs.getString("iinvalidatorName"));
                repNotice.setInvalidatorId(new Long(rs.getLong("iinvalidator")));
                repNotice.setNotifyMethod(rs.getInt("inotifyMethod"));
                repNotice.setInvalidTime(rs.getLong("iInvalidTime"));
                repNotice.setProjectName(rs.getString("iprojectName"));
                repNotice.setState(rs.getInt("istate"));
                repNotice.setTaskName(rs.getString("itaskName"));
                repNotice.setTitle(rs.getString("ititle"));
                repNotice.setType(rs.getInt("itype"));
                repNotice.setValidTime(rs.getLong("ivalidTime"));
                list.add(repNotice);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    static public final List findWithRetryRepSession ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepSession repSession = new RepSession();
                repSession.setActTime(rs.getLong("iactTime"));
                repSession.setClient(rs.getString("iclient"));
                repSession.setId(new Long(rs.getLong("iid")));
                repSession.setSessionId(rs.getString("isessId"));
                repSession.setType(rs.getInt("itype"));
                repSession.setUserFullName(rs.getString("ifullName"));
                repSession.setUserId(new Long(rs.getLong("iuserId")));
                list.add(repSession);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    static public final List findRepProject ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepProject repProject = new RepProject();
                repProject.setId(new Long(rs.getLong("IID")));
                repProject.setPkgContentId(new Long(rs.getLong("IPKGCONTENTID")));
                repProject.setName(rs.getString("INAME"));
                repProject.setMajorVersion(new Integer(rs.getInt("IMAJVER")));
                repProject.setMinorVersion(new Integer(rs.getInt("IMINVER")));
                repProject.setFreezed(rs.getBoolean("IFREEZED"));
                repProject.setFreezeUserFullName(rs.getString("IUPLOADUSER"));
                repProject.setComment(rs.getString("ICOMMENT"));
                repProject.setUploadNum(new Integer(rs.getInt("IUPLOADNUM")));
                repProject.setUuid(rs.getString("IUUID"));
                repProject.setUploadTime(rs.getTimestamp("IUPLOADTIME"));
                repProject.setFreezeUserFullName(rs.getString("IFREEZEUSER"));
                repProject.setFreezeUserId(new Long(rs.getLong("IFREEZEUSERID")));
                repProject.setUploadUserId(new Long(rs.getLong("IUPLOADUSERID")));
                list.add(repProject);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 查询任务属性
     * 
     * @param sql
     * @return
     */
    static public final List queryTaskRuntime ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepTaskRuntime repTaskRuntime = new RepTaskRuntime();
                repTaskRuntime.setId(new Long(rs.getLong("iid")));
                repTaskRuntime.setAcquireTime(rs.getLong("iacquiretime"));
                repTaskRuntime.setDeadLine(rs.getLong("ideadline"));
                repTaskRuntime.setDelegateTime(rs.getLong("idelegatetime"));
                repTaskRuntime.setDelegateUserFullName(rs.getString("idelegateuser"));
                repTaskRuntime.setForwardTime(rs.getLong("iforwordtime"));
                repTaskRuntime.setForwardUserFullName(rs.getString("iforwarduser"));
                repTaskRuntime.setLastAlertTime(rs.getLong("ilastalttime"));
                repTaskRuntime.setLastOperateTime(rs.getLong("ilastoptime"));
                repTaskRuntime.setLastOperation(rs.getString("ilastop"));
                repTaskRuntime.setOldForwardTime(rs.getLong("ioldfwdtime"));
                repTaskRuntime.setOldForwardUserFullName(rs.getString("ioldfwduser"));
                repTaskRuntime.setState(rs.getString("istate"));
                repTaskRuntime.setTaskOwnerFullName(rs.getString("iowner"));
                repTaskRuntime.setAcquired(rs.getLong("iacquired") == 1 ? true : false);
                repTaskRuntime.setTaskEnd(rs.getLong("itaskend") == 1 ? true : false);
                repTaskRuntime.setFailBeginTime(rs.getLong("ifailbegintime"));
                repTaskRuntime.setFailEndTime(rs.getLong("ifailendtime"));
                repTaskRuntime.setDelegateUserId(new Long(rs.getLong("idelegateuserid")));
                repTaskRuntime.setForwardUserId(new Long(rs.getLong("iforwarduserid")));
                repTaskRuntime.setOldForwardUserId(new Long(rs.getLong("ioldfwduserid")));
                repTaskRuntime.setTaskOwnerId(new Long(rs.getLong("iownerid")));
                list.add(repTaskRuntime);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    static public final List findRTaskCRW ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepTaskConstant repTaskConstant = new RepTaskConstant();
                repTaskConstant.setId(new Long(rs.getLong("taskiid")));
                repTaskConstant.setActivityName(rs.getString("iactname"));
                repTaskConstant.setAllowSkip(rs.getLong("iallowskip") == 1 ? true : false);
                repTaskConstant.setCalendarName(rs.getString("icalendarname"));
                repTaskConstant.setDelegateToAssigneeOnly(rs.getLong("ideltoassonly") == 1 ? true : false);
                repTaskConstant.setDesc(rs.getString("idesc"));
                repTaskConstant.setFlowId(new Long(rs.getLong("taskflowid")));
                repTaskConstant.setForwardToAssigneeOnly(rs.getLong("ifwdtoassonly") == 1 ? true : false);
                repTaskConstant.setName(rs.getString("iname"));
                repTaskConstant.setPriority(rs.getInt("ipriority"));
                repTaskConstant.setRecovered(rs.getLong("iisrecovered") == 1 ? true : false);
                repTaskConstant.setRemarkId(new Long(rs.getLong("iremarkid")));
                repTaskConstant.setStartTime(rs.getLong("istarttime"));
                repTaskConstant.setUsingFlowParticipant(rs.getLong("iusingflowpart") == 1 ? true : false);
                list.add(repTaskConstant);

                RepTaskRuntime repTaskRuntime = new RepTaskRuntime();
                repTaskRuntime.setId(new Long(rs.getLong("runtimeiid")));
                repTaskRuntime.setAcquireTime(rs.getLong("iacquiretime"));
                repTaskRuntime.setDeadLine(rs.getLong("ideadline"));
                repTaskRuntime.setDelegateTime(rs.getLong("idelegatetime"));
                repTaskRuntime.setDelegateUserFullName(rs.getString("idelegateuser"));
                repTaskRuntime.setForwardTime(rs.getLong("iforwordtime"));
                repTaskRuntime.setForwardUserFullName(rs.getString("iforwarduser"));
                repTaskRuntime.setLastAlertTime(rs.getLong("ilastalttime"));
                repTaskRuntime.setLastOperateTime(rs.getLong("ilastoptime"));
                repTaskRuntime.setLastOperation(rs.getString("ilastop"));
                repTaskRuntime.setOldForwardTime(rs.getLong("ioldfwdtime"));
                repTaskRuntime.setOldForwardUserFullName(rs.getString("ioldfwduser"));
                repTaskRuntime.setState(rs.getString("istate"));
                repTaskRuntime.setTaskOwnerFullName(rs.getString("iowner"));
                repTaskRuntime.setAcquired(rs.getLong("iacquired") == 1 ? true : false);
                repTaskRuntime.setTaskEnd(rs.getLong("itaskend") == 1 ? true : false);
                repTaskRuntime.setFailBeginTime(rs.getLong("ifailbegintime"));
                repTaskRuntime.setFailEndTime(rs.getLong("ifailendtime"));
                repTaskRuntime.setDelegateUserId(new Long(rs.getLong("idelegateuserid")));
                repTaskRuntime.setForwardUserId(new Long(rs.getLong("iforwarduserid")));
                repTaskRuntime.setOldForwardUserId(new Long(rs.getLong("ioldfwduserid")));
                repTaskRuntime.setTaskOwnerId(new Long(rs.getLong("iownerid")));
                list.add(repTaskRuntime);

                RepWorkflowInstance repWorkflowInstance = new RepWorkflowInstance();
                repWorkflowInstance.setFlowId(rs.getLong("iflowid"));
                repWorkflowInstance.setAutoStart(rs.getLong("iisautostart") == 1 ? true : false);
                repWorkflowInstance.setEndTime(rs.getLong("iendtime"));
                repWorkflowInstance.setFlowComment(rs.getString("iflowcomment"));
                repWorkflowInstance.setFlowDefId(rs.getInt("iflowdefid"));
                repWorkflowInstance.setFlowDes(rs.getString("iflowdes"));
                repWorkflowInstance.setFlowInsName(rs.getString("iflowinsname"));
                repWorkflowInstance.setFlowName(rs.getString("iflowname"));
                repWorkflowInstance.setFlowPrior(rs.getInt("iflowprior"));
                repWorkflowInstance.setHostName(rs.getString("ihostname"));
                repWorkflowInstance.setIsCheck(rs.getInt("ischeck"));
                repWorkflowInstance.setMainBranchEnd(rs.getLong("iismainbranchend") == 1 ? true : false);
                repWorkflowInstance.setMainScopeId(rs.getLong("imainscopeid"));
                repWorkflowInstance.setPrjUuid(rs.getString("iprjuuid"));
                repWorkflowInstance.setProjectName(rs.getString("iprojectname"));
                repWorkflowInstance.setRecoverPerson(rs.getString("recoverperson"));
                repWorkflowInstance.setRecoverTime(rs.getLong("recovertime"));
                repWorkflowInstance.setRecoveryAnalyzed(rs.getLong("iisrecoveryanalyzed") == 1 ? true : false);
                repWorkflowInstance.setSafeFirst(rs.getLong("iissafefirst") == 1 ? true : false);
                repWorkflowInstance.setStartTime(rs.getLong("infostarttime"));
                repWorkflowInstance.setStartUserFullName(rs.getString("istartuserfullname"));
                repWorkflowInstance.setStartUserId(new Long(rs.getLong("istartuserid")));
                repWorkflowInstance.setStatus(rs.getInt("istatus"));
                list.add(repWorkflowInstance);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    static public final List findRepAppLogRecord ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepAppLogRecord ralr = new RepAppLogRecord();
                ralr.setDetailId(new Long(rs.getLong("idetailid")));
                ralr.setFlowId(new Long(rs.getLong("iflowid")));
                ralr.setFlowInsName(rs.getString("iflowinsname"));
                ralr.setActName(rs.getString("iactname"));
                ralr.setFlowName(rs.getString("iflowname"));
                ralr.setId(new Long(rs.getLong("iid")));
                ralr.setLogContent(rs.getString("icontent"));
                ralr.setTaskId(new Long(rs.getLong("itaskid")));
                ralr.setPrjName(rs.getString("iprjname"));
                ralr.setTime(rs.getLong("itime"));
                ralr.setType(rs.getInt("itype"));
                ralr.setUserFullName(rs.getString("iuser"));
                ralr.setUserId(new Long(rs.getLong("iuserid")));
                list.add(ralr);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 查询最后一个错误信息
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    static public final List findExecerror ( String sql, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                List list = new ArrayList();

                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        RepExecError ree = new RepExecError();
                        ree.setActId(rs.getInt("iactid"));
                        ree.setActivityName(rs.getString("iactivityname"));
                        ree.setErrorStackTraceId(new Long(rs.getLong("ierrorstacktrace")));
                        ree.setExceptionName(rs.getString("iexceptionname"));
                        ree.setExecActId(rs.getLong("iexecactid"));
                        ree.setFlowId(rs.getLong("iflowid"));
                        ree.setLocation(rs.getString("ilocation"));
                        ree.setMessage(rs.getString("imessage"));
                        ree.setOccurTime(rs.getLong("ioccurTime"));
                        ree.setScopeId(rs.getLong("iscopeid"));
                        list.add(ree);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at DbOpScheduoler ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return list;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 查询StructInfo
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    static public final List findRepStructInfo ( String sql, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                List list = new ArrayList();

                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        RepStructInfo rsi = new RepStructInfo();
                        rsi.setFinished(rs.getLong("ifinished") == 1 ? true : false);
                        rsi.setFlowId(rs.getLong("iflowid"));
                        rsi.setId(rs.getLong("iid"));
                        rsi.setRetryTimes(rs.getInt("iretrytimes"));
                        rsi.setScopeId(rs.getLong("iscopeid"));
                        rsi.setStructEntryId(rs.getInt("istructentryid"));
                        rsi.setStructEntryName(rs.getString("istructentryname"));
                        rsi.setSupportTransaction(rs.getLong("issupporttr") == 1 ? true : false);
                        rsi.setTimeOut(rs.getLong("itimeout") == 1 ? true : false);
                        list.add(rsi);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at DbOpScheduoler ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return list;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 查询BranchInfo
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    static public final List findRepBranchInfo ( String sql, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                List list = new ArrayList();
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        RepBranchInfo rbi = new RepBranchInfo();
                        rbi.setFinish(rs.getLong("iisfinish") == 1 ? true : false);
                        rbi.setFlowId(rs.getLong("iflowid"));
                        rbi.setIndependent(rs.getLong("iisindependent") == 1 ? true : false);
                        rbi.setScopeId(rs.getLong("iscopeid"));
                        rbi.setStructInfoId(rs.getLong("istrutinfoid"));
                        list.add(rbi);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at DbOpScheduoler ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return list;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    static public final List findRepWorkflowInstance ( String sql, int type ) throws RepositoryException
    {

        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                List list = new ArrayList();
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        RepWorkflowInstance rwfi = new RepWorkflowInstance();
                        rwfi.setEndTime(rs.getLong("iendtime"));
                        rwfi.setFlowComment(rs.getString("iflowcomment"));
                        rwfi.setFlowDefId(rs.getInt("iflowdefid"));
                        rwfi.setAutoStart("1".equals(String.valueOf(rs.getLong("iisautostart"))) ? true : false);
                        rwfi.setFlowDes(rs.getString("iflowdes"));
                        rwfi.setFlowId(rs.getLong("iflowid"));
                        rwfi.setFlowInsName(rs.getString("iflowinsname"));
                        rwfi.setFlowName(rs.getString("iflowname"));
                        rwfi.setFlowPrior(rs.getInt("iflowprior"));
                        rwfi.setHostName(rs.getString("ihostname"));
                        rwfi.setIsCheck(rs.getInt("ischeck"));
                        rwfi.setMainBranchEnd(rs.getLong("iismainbranchend") == 1 ? true : false);
                        rwfi.setMainScopeId(rs.getLong("imainscopeid"));
                        rwfi.setPrjUuid(rs.getString("iprjuuid"));
                        rwfi.setProjectName(rs.getString("iprojectname"));
                        rwfi.setRecoverPerson(rs.getString("recoverperson"));
                        rwfi.setRecoverTime(rs.getLong("recovertime"));
                        rwfi.setRecoveryAnalyzed(rs.getLong("iisrecoveryanalyzed") == 1 ? true : false);
                        rwfi.setSafeFirst(rs.getLong("iissafefirst") == 1 ? true : false);
                        rwfi.setStartTime(rs.getLong("istarttime"));
                        rwfi.setStartUserFullName(rs.getString("istartuserfullname"));
                        rwfi.setStartUserId(new Long(rs.getLong("istartuserid")));
                        rwfi.setStatus(rs.getInt("istatus"));
                        list.add(rwfi);
                    }
                    stmt.close();
                    rs.close();
                } catch (Exception e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepositotyJdbc ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return list;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }

    }

    static public final List getNumTasks ( String sql, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                List list = new ArrayList();
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        Object[] obj = { rs.getString("iflowid"), new Integer(rs.getInt("taskid")) };
                        list.add(obj);
                    }
                    stmt.close();
                    rs.close();
                } catch (Exception e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at DbOpScheduoler ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return list;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }

    }

    static public final List findRepActExecTimeConfig ( String sql, int type ) throws RepositoryException
    {
        List list = new ArrayList();
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        RepActExecTimeConfig raetc = new RepActExecTimeConfig();
                        raetc.setActId(rs.getInt("iactid"));
                        raetc.setActState(rs.getString("iactstate"));
                        raetc.setDay(rs.getInt("iday"));
                        raetc.setHour(rs.getInt("ihour"));
                        raetc.setMinute(rs.getInt("iminute"));
                        raetc.setMonth(rs.getInt("imonth"));
                        raetc.setRelFlag(rs.getInt("irelflag"));
                        raetc.setRuntimeId(new Long(rs.getLong("iid")));
                        raetc.setYear(rs.getInt("iyear"));
                        list.add(raetc);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at ObjectStorerDB ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return list;
    }

    static public final List getRepWarningSetting ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rset = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rset = stmt.executeQuery(sql);
            if (rset.next())
            {
                RepWarningSetting bean = new RepWarningSetting();
                bean.setRuleId(new Long(rset.getLong("iruleid")));
                bean.setTaskId(new Long(rset.getLong("itaskid")));
                bean.setType(rset.getInt("itype"));
                bean.setWarningEmailFlag(rset.getInt("iwarningto"));
                bean.setAlertTime(rset.getString("itime"));
                bean.setAlertInterval(rset.getString("iinterval"));
                bean.setTimeoutEmailFlag(rset.getInt("itimeoutto"));
                list.add(bean);
            }
            stmt.close();
            rset.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rset, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    static public final RepWarningSetting getWarningSetting ( Serializable id, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepWarningSetting bean = new RepWarningSetting();
        StringBuilder sql = new StringBuilder("select * from ieai_warningconf where iruleid=").append(id);
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql.toString());
            if (rs.next())
            {
                bean.setRuleId(new Long(rs.getLong("iruleid")));
                bean.setTaskId(new Long(rs.getLong("itaskid")));
                bean.setType(rs.getInt("itype"));
                bean.setWarningEmailFlag(rs.getInt("iwarningto"));
                bean.setAlertTime(rs.getString("itime"));
                bean.setAlertInterval(rs.getString("iinterval"));
                bean.setTimeoutEmailFlag(rs.getInt("itimeoutto"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return bean;
    }

    static public final List queryUserInheritence ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepUserInheritence repUserInheritence = new RepUserInheritence();
                repUserInheritence.setRoleId(new Long("iroleid"));
                repUserInheritence.setUserId(new Long("iuserid"));
                list.add(repUserInheritence);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 查询所有角色
     * 
     * @param sql
     * @return
     */
    static public final List queryAllRole ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepRole repRole = new RepRole();
                repRole.setId(new Long(rs.getInt("iid")));
                repRole.setName(rs.getString("iname"));
                repRole.setCalendarId(rs.getLong("icalId"));
                repRole.setDescription(rs.getString("idescrip"));
                list.add(repRole);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取角色信息
     * 
     * @param sql
     * @return
     */
    static public final RepRole getRepRole ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepRole repRole = new RepRole();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repRole.setId(new Long(rs.getInt("iid")));
                repRole.setName(rs.getString("iname"));
                repRole.setCalendarId(rs.getLong("icalId"));
                repRole.setDescription(rs.getString("idescrip"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repRole;
    }

    /**
     * 删除表记录
     * 
     * @param sql
     * @return
     */
    static public final List<RepUserInHeritBack> querydelUserInheritences ( String sql )
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        RepUserInHeritBack repUserInHeritDb = null;
        List listRepUserInHeritDb = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                repUserInHeritDb = new RepUserInHeritBack();
                repUserInHeritDb.setRoleId(rs.getLong("IROLEID"));
                repUserInHeritDb.setGroupId(rs.getLong("IGROUPID"));
                repUserInHeritDb.setUserId(rs.getLong("IUSERID"));
                repUserInHeritDb.setFlagtype("saveDb");
                listRepUserInHeritDb.add(repUserInHeritDb);
            }
            conn.commit();
            ps.close();
            rs.close();
        } catch (Exception e)
        {
            if (null != conn)
                try
                {
                    conn.rollback();
                } catch (SQLException e1)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
                }
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return listRepUserInHeritDb;
    }

    /**
     * 删除表记录
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    public static final int remove ( String sql ) throws RepositoryException
    {
        int count = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, Constants.IEAI_IEAI_BASIC);
                    stmt = conn.createStatement();
                    count = stmt.executeUpdate(sql);
                    conn.commit();
                    stmt.close();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(conn, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return count;
    }

    public static final int remove ( String sql, int type ) throws RepositoryException
    {
        int count = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;

                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    count = stmt.executeUpdate(sql);
                    conn.commit();
                    stmt.close();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(conn, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return count;
    }

    /**
     * 持久化数据
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    static public final int save ( String sql ) throws RepositoryException
    {
        int count = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, Constants.IEAI_IEAI_BASIC);
                    stmt = conn.createStatement();
                    count = stmt.executeUpdate(sql);
                    conn.commit();
                    stmt.close();

                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(conn, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return count;
    }

    static public final int save ( String sql, int type )
    {
        int count = 0;
        Connection conn = null;
        Statement stmt = null;

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            count = stmt.executeUpdate(sql);
            conn.commit();
            stmt.close();
        } catch (Exception e)
        {
            if (null != conn)
                try
                {
                    conn.rollback();
                } catch (SQLException e1)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
                }
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closePSConn(conn, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return count;
    }

    /**
     * 分页查询所有工程
     * 
     * @param sql
     * @param pageSize
     * @param curPage
     * @return
     */
    public static List findForPageRepProject ( String sql, int pageSize, int curPage )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        StringBuilder pagesql = null;
        List list = new ArrayList();

        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                RepProject repPro = new RepProject();
                repPro.setComment(rs.getString("icomment"));
                repPro.setFreezed(rs.getLong("ifreezed") == 1 ? true : false);
                repPro.setFreezeUserFullName(rs.getString("ifreezeuser"));
                repPro.setFreezeUserId(new Long(rs.getLong("ifreezeuserid")));
                repPro.setGroupId(new Long(rs.getLong("igroupid")));
                repPro.setId(new Long(rs.getLong("iid")));
                repPro.setMajorVersion(new Integer(rs.getInt("imajver")));
                repPro.setMinorVersion(new Integer(rs.getInt("iminver")));
                repPro.setName(rs.getString("iname"));
                repPro.setPkgContentId(new Long(rs.getLong("ipkgcontentId")));
                repPro.setUploadNum(new Integer(rs.getInt("iuploadnum")));
                repPro.setUploadTime(rs.getTimestamp("iuploadtime"));
                repPro.setUploadUserFullName(rs.getString("iuploaduser"));
                repPro.setUploadUserId(new Long(rs.getLong("iuploaduserid")));
                repPro.setUuid(rs.getString("iuuid"));
                list.add(repPro);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 查询所有接口
     * 
     * @param sql
     * @return
     */
    public static List findRepAdaptor ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepAdaptor repAdaptor = new RepAdaptor();
                repAdaptor.setComment(rs.getString("icomment"));
                repAdaptor.setFreezed(rs.getLong("ifreezed") == 1 ? true : false);
                repAdaptor.setFreezeUserFullName(rs.getString("ifreezeUser"));
                repAdaptor.setFreezeUserId(new Long(rs.getLong("ifreezeuserid")));
                repAdaptor.setId(new Long(rs.getLong("iid")));
                repAdaptor.setMajorVersion(new Integer(rs.getInt("majver")));
                repAdaptor.setMinorVersion(new Integer(rs.getInt("iminver")));
                repAdaptor.setName(rs.getString("iname"));
                repAdaptor.setPkgContentId(new Long(rs.getLong("ipkgcontentId")));
                repAdaptor.setUploadNum(new Integer(rs.getInt("iuploadnum")));
                repAdaptor.setUploadTime(rs.getTimestamp("iuploadTime"));
                repAdaptor.setUploadUserFullName(rs.getString("iuploaduser"));
                repAdaptor.setUploadUserId(new Long(rs.getLong("iuploaduserid")));
                repAdaptor.setUuid(rs.getString("iuuid"));
                list.add(repAdaptor);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * 分页查询所有接口
     * 
     * @param sql
     * @param pageSize
     * @param curPage
     * @return
     */
    public static List findForPageRepAdaptor ( String sql, int pageSize, int curPage )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        StringBuilder pagesql = null;
        List list = new ArrayList();

        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                RepAdaptor repAdaptor = new RepAdaptor();
                repAdaptor.setComment(rs.getString("icomment"));
                repAdaptor.setFreezed(rs.getLong("ifreezed") == 1 ? true : false);
                repAdaptor.setFreezeUserFullName(rs.getString("ifreezeUser"));
                repAdaptor.setFreezeUserId(new Long(rs.getLong("ifreezeuserid")));
                repAdaptor.setId(new Long(rs.getLong("iid")));
                repAdaptor.setMajorVersion(new Integer(rs.getInt("majver")));
                repAdaptor.setMinorVersion(new Integer(rs.getInt("iminver")));
                repAdaptor.setName(rs.getString("iname"));
                repAdaptor.setPkgContentId(new Long(rs.getLong("ipkgcontentId")));
                repAdaptor.setUploadNum(new Integer(rs.getInt("iuploadnum")));
                repAdaptor.setUploadTime(rs.getTimestamp("iuploadTime"));
                repAdaptor.setUploadUserFullName(rs.getString("iuploaduser"));
                repAdaptor.setUploadUserId(new Long(rs.getLong("iuploaduserid")));
                repAdaptor.setUuid(rs.getString("iuuid"));

                list.add(repAdaptor);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * 分页查询session信息
     * 
     * @param sql
     * @param pageSize
     * @param curPage
     * @return
     */
    public static List findForPageRepSession ( String sql, int pageSize, int curPage, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                RepSession repSession = new RepSession();
                repSession.setActTime(rs.getLong("iactTime"));
                repSession.setClient(rs.getString("iclient"));
                repSession.setId(new Long(rs.getLong("iid")));
                repSession.setSessionId(rs.getString("isessId"));
                repSession.setType(rs.getInt("itype"));
                repSession.setUserFullName(rs.getString("ifullName"));
                repSession.setUserId(new Long(rs.getLong("iuserId")));
                list.add(repSession);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * 分页查询日历信息
     * 
     * @param sql
     * @param pageSize
     * @param curPage
     * @return
     */
    public static List findForPageRepCalendar ( String sql, int pageSize, int curPage )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                RepCalendar rc = new RepCalendar();
                rc.setDefault(rs.getLong("idefault") == 1 ? true : false);
                rc.setDescription(rs.getString("idesc"));
                rc.setId(new Long(rs.getLong("iid")));
                rc.setName(rs.getString("iname"));
                list.add(rc);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * 分页查询Notice
     * 
     * @param sql
     * @param pageSize
     * @param curPage
     * @return
     */
    public static List findForPageRepNotice ( String sql, int pageSize, int curPage, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                RepNotice repNotice = new RepNotice();
                repNotice.setAuthorFullName(rs.getString("iauthorName"));
                repNotice.setAuthorId(new Long(rs.getLong("iauthorId")));
                repNotice.setBeginSecondOfDay(rs.getInt("ibeginTime"));
                repNotice.setContentId(new Long(rs.getLong("icontentId")));
                repNotice.setCreatedTime(rs.getLong("icreatedTime"));
                repNotice.setEndSecondOfDay(rs.getInt("iendTime"));
                repNotice.setFeedback(rs.getLong("ifeedback") == 1 ? true : false);
                repNotice.setFlowInstanceNamePattern(rs.getString("iflowNamePattern"));
                repNotice.setFlowName(rs.getString("iflowName"));
                repNotice.setFlowStartTimeBegin(rs.getLong("iflowStartTimeBegin"));
                repNotice.setFlowStartTimeEnd(rs.getLong("iflowStartTimeEnd"));
                repNotice.setId(new Long(rs.getLong("iid")));
                repNotice.setInvalidatorFullName(rs.getString("iinvalidatorName"));
                repNotice.setInvalidatorId(new Long(rs.getLong("iinvalidator")));
                repNotice.setNotifyMethod(rs.getInt("inotifyMethod"));
                repNotice.setInvalidTime(rs.getLong("iInvalidTime"));
                repNotice.setProjectName(rs.getString("iprojectName"));
                repNotice.setState(rs.getInt("istate"));
                repNotice.setTaskName(rs.getString("itaskName"));
                repNotice.setTitle(rs.getString("ititle"));
                repNotice.setType(rs.getInt("itype"));
                repNotice.setValidTime(rs.getLong("ivalidTime"));
                list.add(repNotice);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * 查询工作流
     * 
     * @param sql
     * @return
     */
    public static List queryRepWorkflow ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepWorkflow repWorkflow = new RepWorkflow();
                repWorkflow.setCalendarName(rs.getString("icalendarname"));
                repWorkflow.setFlowDesc(rs.getString("iflowdesc"));
                repWorkflow.setFlowName(rs.getString("iflowname"));
                repWorkflow.setId(new Long(rs.getLong("iid")));
                repWorkflow.setPrjId(new Long(rs.getLong("iprjid")));
                list.add(repWorkflow);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageRepProjectAndRepWorkflow ( String sql, FlowDefReportCriteria criteria, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map result = null;
        List list = new ArrayList();
        String querySql = "";
        if (criteria.canPage)
        {
            querySql = sql + " and t.rn between " + ((criteria.curPage - 1) * criteria.pageSize + 1) + " and "
                    + ((criteria.curPage - 1) * criteria.pageSize + criteria.pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(querySql);
            while (rs.next())
            {
                result = new HashMap();
                result.put("prjName", rs.getString("iname"));
                result.put("version", rs.getString("imajver"));
                result.put("flowName", rs.getString("iflowname"));
                result.put("minver", rs.getString("iminver"));
                result.put("uplaodNum", rs.getString("iuploadnum"));
                result.put("calendarName", rs.getString("icalendarname"));
                result.put("flowDesc", rs.getString("iflowdesc"));
                result.put("uuid", rs.getString("IUUID"));
                list.add(result);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * <li>Description:</li>
     * 
     * <AUTHOR> Dec 17, 2012
     * @param sql
     * @param criteria
     * @return return List
     */
    public static Object[] getFlowGroupUser ( String uuid, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map result = null;
        long info = 0;
        Object[] object = new Object[2];
        List list = new ArrayList();
        List list2 = new ArrayList();
        String querySql = "select distinct(c.IROLEID) from ieai_workflowinstance a ,ieai_actruntime b,IEAI_TASKROLES c where c.ITASKID=b.ITASKID  and  a.IFLOWID=b.IFLOWID and b.IREADYTIME>0 and a.IPRJUUID='"
                + uuid + "'";
        String querySql2 = "select distinct(d.IUSERId) from ieai_workflowinstance a ,ieai_actruntime b,IEAI_TASKUSERS d where  d.ITASKID=b.ITASKID and  a.IFLOWID=b.IFLOWID and b.IREADYTIME>0 and a.IPRJUUID='"
                + uuid + "'";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(querySql);
            while (rs.next())
            {
                info = rs.getLong("IROLEID");
                list.add(info);
                info = 0;
            }
            stmt = null;
            rs = null;
            stmt = conn.createStatement();
            rs = stmt.executeQuery(querySql2);
            while (rs.next())
            {
                info = rs.getLong("IUSERId");
                list2.add(info);
                info = 0;
            }
            object[0] = list;
            object[1] = list2;
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return object;
    }

    public static List findForPageWorkflowinstanceActruninfo ( String sql, ActRunUseTimeReportCriteria criteria,
            int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map mapBean = null;
        List list = new ArrayList();
        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((criteria.curPage - 1) * criteria.pageSize + 1)
                    .append(" and ").append(((criteria.curPage - 1) * criteria.pageSize) + criteria.pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((criteria.curPage - 1) * criteria.pageSize + 1)
                    .append(" and ").append(((criteria.curPage - 1) * criteria.pageSize) + criteria.pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                mapBean = new HashMap();
                mapBean.put("prjName", rs.getString("iprojectname"));
                mapBean.put("flowName", rs.getString("iflowname"));
                mapBean.put("actName", rs.getString("iactname"));
                mapBean.put("time", new Integer(rs.getInt("avgTime")));
                mapBean.put("maxIid", new Long(rs.getLong("maxIid")));
                list.add(mapBean);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageWorkflowInstanceAndActRunInfo ( String sql, ActRunInfoReportCriteria criteria,
            int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map result = null;
        List list = new ArrayList();
        String querySql = "";
        if (criteria.canPage)
        {
            querySql = sql + " and t.rn between " + ((criteria.curPage - 1) * criteria.pageSize + 1) + " and "
                    + ((criteria.curPage - 1) * criteria.pageSize + criteria.pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(querySql);
            while (rs.next())
            {
                result = new HashMap();
                result.put("prjName", rs.getString("iprojectname"));
                result.put("flowName", rs.getString("iflowname"));
                result.put("flowInsName", rs.getString("iflowinsname"));
                result.put("flowId", new Long(rs.getLong("iflowid")));
                result.put("actName", rs.getString("iactname"));
                result.put("actDefName", rs.getString("iactdefname"));
                result.put("shouldStartTime", new Long(rs.getLong("ishouldstarttime")));
                result.put("startTimeOne", new Long(rs.getLong("istarttime")));
                result.put("endTimeOne", new Long(rs.getLong("iendtime")));
                result.put("state", rs.getString("istate"));
                result.put("actDesc", rs.getString("iactdesc"));
                result.put("id", new Long(rs.getLong("iid")));
                result.put("isStruct", rs.getString("iistruct"));
                list.add(result);

            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageRepFlowPoolNum ( String sql, int pageSize, int curPage, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                Object[] obj = { rs.getString("iprjflowname"), rs.getString("iflowpoolnum") };
                list.add(obj);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageRepUser ( String sql, int pageSize, int curPage )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                RepUser repUser = new RepUser();
                repUser.setId(new Long(rs.getLong("iid")));
                repUser.setCalendarId(new Long(rs.getLong("icalId")));
                repUser.setDepartment(rs.getString("idepartment"));
                repUser.setEmail(rs.getString("iemail"));
                repUser.setFullName(rs.getString("ifullname"));
                repUser.setLastUpdatePasswordTime(rs.getLong("ipwuptime"));
                repUser.setLocale(rs.getString("ilocale"));
                repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                repUser.setLoginName(rs.getString("iloginName"));
                repUser.setPassword(rs.getString("ipassword"));
                repUser.setTelephone(rs.getString("itelephone"));
                list.add(repUser);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageRepRole ( String sql, int pageSize, int curPage )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                RepRole repRole = new RepRole();
                repRole.setId(new Long(rs.getLong("iid")));
                repRole.setName(rs.getString("iname"));
                repRole.setCalendarId(rs.getLong("icalId"));
                repRole.setDescription(rs.getString("idescrip"));
                list.add(repRole);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageRepAppLogRecord ( String sql, AppLogFilter appLogFilter, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (appLogFilter.canPage)
        {
            if (DBManager.Orcl_Faimily())
            {
                pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                        .append(") d) t where t.r1 between ")
                        .append((appLogFilter.curPage - 1) * appLogFilter.pageSize + 1).append(" and ")
                        .append(((appLogFilter.curPage - 1) * appLogFilter.pageSize) + appLogFilter.pageSize);
            } else
            {
                pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                        .append(") as d) t where t.r1 between ")
                        .append((appLogFilter.curPage - 1) * appLogFilter.pageSize + 1).append(" and ")
                        .append(((appLogFilter.curPage - 1) * appLogFilter.pageSize) + appLogFilter.pageSize);
            }
        } else
        {
            pagesql = new StringBuilder(sql);
        }

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {
                Map mapBean = new HashMap();
                RepAppLogRecord reprecord = new RepAppLogRecord();
                reprecord.setId(new Long(rs.getLong("iid")));
                reprecord.setActName(rs.getString("iactname"));
                reprecord.setFlowId(new Long(rs.getLong("iflowid")));
                reprecord.setFlowName(rs.getString("iflowname"));
                reprecord.setLogContent(rs.getString("aicontent"));
                reprecord.setDetailId(new Long(rs.getLong("idetailid")));
                reprecord.setTaskId(new Long(rs.getLong("itaskid")));
                reprecord.setPrjName(rs.getString("iprjname"));
                reprecord.setTime(rs.getLong("itime"));
                reprecord.setType(rs.getInt("itype"));
                reprecord.setUserId(new Long(rs.getLong("iuserid")));
                reprecord.setUserFullName(rs.getString("iuser"));
                reprecord.setFlowInsName(rs.getString("iflowinsname"));
                mapBean.put("reprecord", reprecord);

                TextEntity textEntity = new TextEntity();
                textEntity.setText(rs.getString("cicontent"));
                textEntity.setNull(rs.getInt("inull") == 1 ? true : false);
                mapBean.put("textEntity", textEntity);
                list.add(mapBean);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * 查询任务信息
     * 
     * @param sql
     * @return
     */
    public static List findWithRetryRCW ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map mapBean = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql.toString());
            while (rs.next())
            {

                mapBean = new HashMap();
                RepTaskConstant taskDef = new RepTaskConstant();
                taskDef.setId(new Long(rs.getLong("taskiid")));
                taskDef.setActivityName(rs.getString("iactname"));
                taskDef.setAllowSkip(rs.getLong("iallowskip") == 1 ? true : false);
                taskDef.setCalendarName(rs.getString("icalendarname"));
                taskDef.setDelegateToAssigneeOnly(rs.getLong("ideltoassonly") == 1 ? true : false);
                taskDef.setDesc(rs.getString("idesc"));
                taskDef.setFlowId(new Long(rs.getLong("taskflowid")));
                taskDef.setForwardToAssigneeOnly(rs.getLong("ifwdtoassonly") == 1 ? true : false);
                taskDef.setName(rs.getString("iname"));
                taskDef.setPriority(rs.getInt("ipriority"));
                taskDef.setRecovered(rs.getLong("iisrecovered") == 1 ? true : false);
                taskDef.setRemarkId(new Long(rs.getLong("iremarkid")));
                taskDef.setStartTime(rs.getLong("taskstarttime"));
                taskDef.setUsingFlowParticipant(rs.getLong("iusingflowpart") == 1 ? true : false);
                mapBean.put("taskDef", taskDef);

                RepTaskRuntime taskRuntime = new RepTaskRuntime();
                taskRuntime.setId(new Long(rs.getLong("runtimeiid")));
                taskRuntime.setAcquireTime(rs.getLong("iacquiretime"));
                taskRuntime.setDeadLine(rs.getLong("ideadline"));
                taskRuntime.setDelegateTime(rs.getLong("idelegatetime"));
                taskRuntime.setDelegateUserFullName(rs.getString("idelegateuser"));
                taskRuntime.setForwardTime(rs.getLong("iforwordtime"));
                taskRuntime.setForwardUserFullName(rs.getString("iforwarduser"));
                taskRuntime.setLastAlertTime(rs.getLong("ilastalttime"));
                taskRuntime.setLastOperateTime(rs.getLong("ilastoptime"));
                taskRuntime.setLastOperation(rs.getString("ilastop"));
                taskRuntime.setOldForwardTime(rs.getLong("ioldfwdtime"));
                taskRuntime.setOldForwardUserFullName(rs.getString("ioldfwduser"));
                taskRuntime.setState(rs.getString("istate"));
                taskRuntime.setTaskOwnerFullName(rs.getString("iowner"));
                taskRuntime.setAcquired(rs.getLong("iacquired") == 1 ? true : false);
                taskRuntime.setTaskEnd(rs.getLong("itaskend") == 1 ? true : false);
                taskRuntime.setFailBeginTime(rs.getLong("ifailbegintime"));
                taskRuntime.setFailEndTime(rs.getLong("ifailendtime"));
                taskRuntime.setDelegateUserId(new Long(rs.getLong("idelegateuserid")));
                taskRuntime.setForwardUserId(new Long(rs.getLong("iforwarduserid")));
                taskRuntime.setOldForwardUserId(new Long(rs.getLong("ioldfwduserid")));
                taskRuntime.setTaskOwnerId(new Long(rs.getLong("iownerid")));
                mapBean.put("taskRuntime", taskRuntime);

                RepWorkflowInstance flowInfo = new RepWorkflowInstance();
                flowInfo.setFlowId(rs.getLong("iflowid"));
                flowInfo.setAutoStart(rs.getLong("iisautostart") == 1 ? true : false);
                flowInfo.setEndTime(rs.getLong("iendtime"));
                flowInfo.setFlowComment(rs.getString("iflowcomment"));
                flowInfo.setFlowDefId(rs.getInt("iflowdefid"));
                flowInfo.setFlowDes(rs.getString("iflowdes"));
                flowInfo.setFlowInsName(rs.getString("iflowinsname"));
                flowInfo.setFlowName(rs.getString("iflowname"));
                flowInfo.setFlowPrior(rs.getInt("iflowprior"));
                flowInfo.setHostName(rs.getString("ihostname"));
                flowInfo.setIsCheck(rs.getInt("ischeck"));
                flowInfo.setMainBranchEnd(rs.getLong("iismainbranchend") == 1 ? true : false);
                flowInfo.setMainScopeId(rs.getLong("imainscopeid"));
                flowInfo.setPrjUuid(rs.getString("iprjuuid"));
                flowInfo.setProjectName(rs.getString("iprojectname"));
                flowInfo.setRecoverPerson(rs.getString("recoverperson"));
                flowInfo.setRecoverTime(rs.getLong("recovertime"));
                flowInfo.setRecoveryAnalyzed(rs.getLong("iisrecoveryanalyzed") == 1 ? true : false);
                flowInfo.setSafeFirst(rs.getLong("iissafefirst") == 1 ? true : false);
                flowInfo.setStartTime(rs.getLong("infostarttime"));
                flowInfo.setStartUserFullName(rs.getString("istartuserfullname"));
                flowInfo.setStartUserId(new Long(rs.getLong("istartuserid")));
                flowInfo.setStatus(rs.getInt("istatus"));
                mapBean.put("flowInfo", flowInfo);

                list.add(mapBean);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageRCW ( String sql, int pageSize, int curPage, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map mapBean = null;
        List list = new ArrayList();

        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(pagesql.toString());
            while (rs.next())
            {

                mapBean = new HashMap();
                RepTaskConstant taskDef = new RepTaskConstant();
                taskDef.setId(new Long(rs.getLong("taskiid")));
                taskDef.setActivityName(rs.getString("iactname"));
                taskDef.setAllowSkip(rs.getLong("iallowskip") == 1 ? true : false);
                taskDef.setCalendarName(rs.getString("icalendarname"));
                taskDef.setDelegateToAssigneeOnly(rs.getLong("ideltoassonly") == 1 ? true : false);
                taskDef.setDesc(rs.getString("idesc"));
                taskDef.setFlowId(new Long(rs.getLong("taskflowid")));
                taskDef.setForwardToAssigneeOnly(rs.getLong("ifwdtoassonly") == 1 ? true : false);
                taskDef.setName(rs.getString("iname"));
                taskDef.setPriority(rs.getInt("ipriority"));
                taskDef.setRecovered(rs.getLong("iisrecovered") == 1 ? true : false);
                taskDef.setRemarkId(new Long(rs.getLong("iremarkid")));
                taskDef.setStartTime(rs.getLong("taskstarttime"));
                taskDef.setUsingFlowParticipant(rs.getLong("iusingflowpart") == 1 ? true : false);
                mapBean.put("taskDef", taskDef);

                RepTaskRuntime taskRuntime = new RepTaskRuntime();
                taskRuntime.setId(new Long(rs.getLong("runtimeiid")));
                taskRuntime.setAcquireTime(rs.getLong("iacquiretime"));
                taskRuntime.setDeadLine(rs.getLong("ideadline"));
                taskRuntime.setDelegateTime(rs.getLong("idelegatetime"));
                taskRuntime.setDelegateUserFullName(rs.getString("idelegateuser"));
                taskRuntime.setForwardTime(rs.getLong("iforwordtime"));
                taskRuntime.setForwardUserFullName(rs.getString("iforwarduser"));
                taskRuntime.setLastAlertTime(rs.getLong("ilastalttime"));
                taskRuntime.setLastOperateTime(rs.getLong("ilastoptime"));
                taskRuntime.setLastOperation(rs.getString("ilastop"));
                taskRuntime.setOldForwardTime(rs.getLong("ioldfwdtime"));
                taskRuntime.setOldForwardUserFullName(rs.getString("ioldfwduser"));
                taskRuntime.setState(rs.getString("istate"));
                taskRuntime.setTaskOwnerFullName(rs.getString("iowner"));
                taskRuntime.setAcquired(rs.getLong("iacquired") == 1 ? true : false);
                taskRuntime.setTaskEnd(rs.getLong("itaskend") == 1 ? true : false);
                taskRuntime.setFailBeginTime(rs.getLong("ifailbegintime"));
                taskRuntime.setFailEndTime(rs.getLong("ifailendtime"));
                taskRuntime.setDelegateUserId(new Long(rs.getLong("idelegateuserid")));
                taskRuntime.setForwardUserId(new Long(rs.getLong("iforwarduserid")));
                taskRuntime.setOldForwardUserId(new Long(rs.getLong("ioldfwduserid")));
                taskRuntime.setTaskOwnerId(new Long(rs.getLong("iownerid")));
                mapBean.put("taskRuntime", taskRuntime);

                RepWorkflowInstance flowInfo = new RepWorkflowInstance();
                flowInfo.setFlowId(rs.getLong("iflowid"));
                flowInfo.setAutoStart(rs.getLong("iisautostart") == 1 ? true : false);
                flowInfo.setEndTime(rs.getLong("iendtime"));
                flowInfo.setFlowComment(rs.getString("iflowcomment"));
                flowInfo.setFlowDefId(rs.getInt("iflowdefid"));
                flowInfo.setFlowDes(rs.getString("iflowdes"));
                flowInfo.setFlowInsName(rs.getString("iflowinsname"));
                flowInfo.setFlowName(rs.getString("iflowname"));
                flowInfo.setFlowPrior(rs.getInt("iflowprior"));
                flowInfo.setHostName(rs.getString("ihostname"));
                flowInfo.setIsCheck(rs.getInt("ischeck"));
                flowInfo.setMainBranchEnd(rs.getLong("iismainbranchend") == 1 ? true : false);
                flowInfo.setMainScopeId(rs.getLong("imainscopeid"));
                flowInfo.setPrjUuid(rs.getString("iprjuuid"));
                flowInfo.setProjectName(rs.getString("iprojectname"));
                flowInfo.setRecoverPerson(rs.getString("recoverperson"));
                flowInfo.setRecoverTime(rs.getLong("recovertime"));
                flowInfo.setRecoveryAnalyzed(rs.getLong("iisrecoveryanalyzed") == 1 ? true : false);
                flowInfo.setSafeFirst(rs.getLong("iissafefirst") == 1 ? true : false);
                flowInfo.setStartTime(rs.getLong("infostarttime"));
                flowInfo.setStartUserFullName(rs.getString("istartuserfullname"));
                flowInfo.setStartUserId(new Long(rs.getLong("istartuserid")));
                flowInfo.setStatus(rs.getInt("istatus"));
                mapBean.put("flowInfo", flowInfo);

                list.add(mapBean);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static List findForPageRepWorkflowInstance ( String sql, int pageSize, int curPage, int type )
            throws RepositoryException
    {
        StringBuilder pagesql = null;
        if (DBManager.Orcl_Faimily())
        {
            pagesql = new StringBuilder("select t.* from (select rownum r1 ,d.* from (").append(sql)
                    .append(") d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        } else if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            pagesql = new StringBuilder(
                    sql + " limit " + (curPage - 1) * pageSize + "," + ((curPage - 1) * pageSize + pageSize));
        } else
        {
            pagesql = new StringBuilder("select t.* from (select rownumber() over() as r1 ,d.* from (").append(sql)
                    .append(") as d) t where t.r1 between ").append((curPage - 1) * pageSize + 1).append(" and ")
                    .append(((curPage - 1) * pageSize) + pageSize);
        }

        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                List list = new ArrayList();
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(pagesql.toString());
                    while (rs.next())
                    {

                        RepWorkflowInstance rwfi = new RepWorkflowInstance();
                        rwfi.setEndTime(rs.getLong("iendtime"));
                        rwfi.setFlowComment(rs.getString("iflowcomment"));
                        rwfi.setFlowDefId(rs.getInt("iflowdefid"));
                        rwfi.setAutoStart("1".equals(String.valueOf(rs.getLong("iisautostart"))) ? true : false);
                        rwfi.setFlowDes(rs.getString("iflowdes"));
                        rwfi.setFlowId(rs.getLong("iflowid"));
                        rwfi.setFlowInsName(rs.getString("iflowinsname"));
                        rwfi.setFlowName(rs.getString("iflowname"));
                        rwfi.setFlowPrior(rs.getInt("iflowprior"));
                        rwfi.setHostName(rs.getString("ihostname"));
                        rwfi.setIsCheck(rs.getInt("ischeck"));
                        rwfi.setMainBranchEnd(rs.getLong("iismainbranchend") == 1 ? true : false);
                        rwfi.setMainScopeId(rs.getLong("imainscopeid"));
                        rwfi.setPrjUuid(rs.getString("iprjuuid"));
                        rwfi.setProjectName(rs.getString("iprojectname"));
                        rwfi.setRecoverPerson(rs.getString("recoverperson"));
                        rwfi.setRecoverTime(rs.getLong("recovertime"));
                        rwfi.setRecoveryAnalyzed(rs.getLong("iisrecoveryanalyzed") == 1 ? true : false);
                        rwfi.setSafeFirst(rs.getLong("iissafefirst") == 1 ? true : false);
                        rwfi.setStartTime(rs.getLong("istarttime"));
                        rwfi.setStartUserFullName(rs.getString("istartuserfullname"));
                        rwfi.setStartUserId(new Long(rs.getLong("istartuserid")));
                        rwfi.setStatus(rs.getInt("istatus"));
                        list.add(rwfi);
                    }
                    stmt.close();
                    rs.close();
                } catch (Exception e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepositotyJdbc ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return list;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    static public final List queryAll ( String sql, int type )
    {

        List list = new ArrayList();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            list.add(rs);
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static final void update ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            stmt.executeUpdate(sql);
            conn.commit();
            stmt.close();
        } catch (Exception e)
        {
            if (conn != null)
                try
                {
                    conn.rollback();
                } catch (SQLException e1)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
                }
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closePSConn(conn, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

    }

    /**
     * 统计记录个数
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    public static final int count ( String sql ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                int inte = 0;
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, Constants.IEAI_IEAI_BASIC);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        inte = rs.getInt(1);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at DbOpScheduoler ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return inte;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public static final int count ( String sql, int type ) throws RepositoryException
    {
        int inte = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;

                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        inte = rs.getInt(1);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return inte;
    }

    public static final void removeAll ( String sql ) throws RepositoryException
    {

        Connection conn = null;
        Statement stmt = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            stmt.execute(sql);
            conn.commit();
            stmt.close();
        } catch (Exception e)
        {
            if (conn != null)
                try
                {
                    conn.rollback();
                } catch (SQLException e1)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
                }
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closePSConn(conn, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
    }

    public static final void removeAllWithRetry ( String sql ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                removeAll(sql);
                return;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 获取用户信息
     * 
     * @param sql
     * @return
     */
    public static final RepUser queryUserBasicInfo ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepUser repUser = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repUser = new RepUser();
                repUser.setId(new Long(rs.getLong("iid")));
                repUser.setCalendarId(new Long(rs.getLong("icalId")));
                repUser.setDepartment(rs.getString("idepartment"));
                repUser.setEmail(rs.getString("iemail"));
                repUser.setFullName(rs.getString("ifullname"));
                repUser.setLastUpdatePasswordTime(rs.getLong("ipwuptime"));
                repUser.setLocale(rs.getString("ilocale"));
                repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                repUser.setLoginName(rs.getString("iloginName"));
                repUser.setPassword(rs.getString("ipassword"));
                repUser.setTelephone(rs.getString("itelephone"));
                repUser.setUserType(rs.getInt("ITYPE"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repUser;
    }
    
    /**
     * 
     * <li>Description:预编译写法</li> 
     * <AUTHOR>
     * 2022年6月28日 
     * @param sql
     * @param params
     * @return
     * return RepUser
     */
    public static final RepUser queryUserBasicInfo ( String sql,Object[] params )
    {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        RepUser repUser = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.prepareStatement(sql);
            if(params!=null && params.length>0) {
                for(int i=0;i<params.length;i++) {
                    stmt.setObject(i + 1, params[i]);
                    
                }
            }
            rs = stmt.executeQuery();
            while (rs.next())
            {
                repUser = new RepUser();
                repUser.setId(new Long(rs.getLong("iid")));
                repUser.setCalendarId(new Long(rs.getLong("icalId")));
                repUser.setDepartment(rs.getString("idepartment"));
                repUser.setEmail(rs.getString("iemail"));
                repUser.setFullName(rs.getString("ifullname"));
                repUser.setLastUpdatePasswordTime(rs.getLong("ipwuptime"));
                repUser.setLocale(rs.getString("ilocale"));
                repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                repUser.setLoginName(rs.getString("iloginName"));
                repUser.setPassword(rs.getString("ipassword"));
                repUser.setTelephone(rs.getString("itelephone"));
                repUser.setUserType(rs.getInt("ITYPE"));
            }
//            stmt.close();
//            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repUser;
    }

    /**
     * 获取工程信息
     * 
     * @param sql
     * @return
     */
    public static final RepProject queryProject ( String sql )
    {
        RepProject repProject = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        repProject = new RepProject();
                        repProject.setId(new Long(rs.getLong("IID")));
                        repProject.setPkgContentId(new Long(rs.getLong("IPKGCONTENTID")));
                        repProject.setName(rs.getString("INAME"));
                        repProject.setMajorVersion(new Integer(rs.getInt("IMAJVER")));
                        repProject.setMinorVersion(new Integer(rs.getInt("IMINVER")));
                        repProject.setFreezed(rs.getBoolean("IFREEZED"));
                        repProject.setFreezeUserFullName(rs.getString("IUPLOADUSER"));
                        repProject.setComment(rs.getString("ICOMMENT"));
                        repProject.setUploadNum(new Integer(rs.getInt("IUPLOADNUM")));
                        repProject.setUuid(rs.getString("IUUID"));
                        repProject.setUploadTime(rs.getTimestamp("IUPLOADTIME"));
                        repProject.setFreezeUserFullName(rs.getString("IFREEZEUSER"));
                        repProject.setFreezeUserId(new Long(rs.getLong("IFREEZEUSERID")));
                        repProject.setUploadUserId(new Long(rs.getLong("IUPLOADUSERID")));
                    }
                    stmt.close();
                    rs.close();
                    break;
                } catch (Exception e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, method, _log);
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, method, _log);
                }
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error("DBRetryUtil error!", e);
                }
            }
            return repProject;
        }
        return repProject;
    }

    /**
     * 获取角色所有权限
     * 
     * @param sql
     * @return
     */
    public static final List queryRolePermission ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List rolePermissions = new ArrayList();
        try
        {

            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepRolePermission rrp = new RepRolePermission();
                rrp.setEnabled(rs.getLong("ienabled") == 1 ? true : false);
                rrp.setId(new Long(rs.getLong("iid")));
                rrp.setPermName(rs.getString("ipermname"));
                rrp.setPrjAdpName(rs.getString("iprjadpname"));
                rrp.setRoleId(new Long(rs.getLong("iroleId")));
                rrp.setType(rs.getShort("itype"));
                rolePermissions.add(rrp);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return rolePermissions;
    }

    /**
     * 获取用户所有权限
     * 
     * @param sql
     * @return
     */
    static public final List queryUserPermissions ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List listToPS = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepUserPermission rup = new RepUserPermission();
                rup.setEnabled(rs.getLong("ienabled") == 1 ? true : false);
                rup.setId(new Long(rs.getLong("iid")));
                rup.setPermName(rs.getString("ipermname"));
                rup.setPrjAdpName(rs.getString("iprjadpname"));
                rup.setType(rs.getShort("itype"));
                rup.setUserId(new Long(rs.getLong("iuserid")));
                listToPS.add(rup);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return listToPS;
    }

    /**
     * 获取目标
     * 
     * @param sql
     * @return
     */
    public static final List queryDestination ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List result = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepDestinationInfo rdi = new RepDestinationInfo();
                rdi.setClientId(rs.getString("iclientid"));
                rdi.setCreateTime(rs.getLong("icreatetime"));
                rdi.setDurable(rs.getLong("idurable") == 1 ? true : false);
                rdi.setId(new Long(rs.getLong("iid")));
                rdi.setName(rs.getString("iname"));
                rdi.setType(rs.getInt("itype"));
                result.add(rdi);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return result;
    }

    public static final List queryRepTimeLimit ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepTimeLimit repTimeLimit = new RepTimeLimit();
                repTimeLimit.setAbstractDefine(rs.getString("iabsdefe"));
                repTimeLimit.setRelatedTaskActivityId(rs.getString("iactid"));
                repTimeLimit.setRelateToTaskActState(rs.getString("ireltaskstate"));
                repTimeLimit.setRelativeDefine(rs.getString("irelatdef"));
                repTimeLimit.setRuleId(new Long(rs.getLong("iruleid")));
                repTimeLimit.setSelfTaskState(rs.getString("iselftaskstate"));
                repTimeLimit.setTaskId(new Long(rs.getLong("itaskid")));
                repTimeLimit.setType(rs.getInt("itype"));
                list.add(repTimeLimit);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    public static final Object getRepTimeLimit ( Serializable id )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepTimeLimit repTimeLimit = null;
        String sql = " select * from ieai_timelimit where iruleid = " + id;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repTimeLimit = new RepTimeLimit();
                repTimeLimit.setAbstractDefine(rs.getString("iabsdefe"));
                repTimeLimit.setRelatedTaskActivityId(rs.getString("iactid"));
                repTimeLimit.setRelateToTaskActState(rs.getString("ireltaskstate"));
                repTimeLimit.setRelativeDefine(rs.getString("irelatdef"));
                repTimeLimit.setRuleId(new Long(rs.getLong("iruleid")));
                repTimeLimit.setSelfTaskState(rs.getString("iselftaskstate"));
                repTimeLimit.setTaskId(new Long(rs.getLong("itaskid")));
                repTimeLimit.setType(rs.getInt("itype"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return repTimeLimit;
    }

    /**
     * 获取NoticeOperation
     * 
     * @param sql
     * @return
     */
    public final static List getNoticeOperation ( String sql, int type )
    {

        List repOperations = new ArrayList();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                RepNoticeOperation rno = new RepNoticeOperation();
                rno.setFeedback(rs.getString("ifeedback"));
                rno.setFlowInstanceId(new Long(rs.getLong("iflowInstanceId")));
                rno.setFlowInstanceName(rs.getString("iflowInstanceName"));
                rno.setFlowName(rs.getString("iflowName"));
                rno.setId(new Long(rs.getLong("iid")));
                rno.setNoticeId(new Long(rs.getLong("inoticeId")));
                rno.setOccurTime(rs.getLong("itime"));
                rno.setOperation(rs.getInt("ioperation"));
                rno.setTaskId(new Long(rs.getLong("itaskId")));
                rno.setTaskName(rs.getString("itaskName"));
                rno.setTaskRecord(rs.getLong("iisTaskRecord") == 1 ? true : false);
                rno.setUserFullName(rs.getString("iuserFullName"));
                rno.setUserId(new Long(rs.getLong("iuserId")));
                repOperations.add(rno);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repOperations;
    }

    /**
     * 获取DelegatedRecord
     * 
     * @param sql
     * @return
     */
    public final static List queryDelegatedRecord ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepDelegatedRecord repDelegatedRecord = new RepDelegatedRecord();
                repDelegatedRecord.setDelegatedTime(rs.getLong("idelegattime"));
                repDelegatedRecord.setDelegatedUserFullName(rs.getString("ideleteuser"));
                repDelegatedRecord.setDelegatedUserId(new Long(rs.getLong("ideleteuserid")));
                repDelegatedRecord.setOldState(rs.getString("ioldstate"));
                repDelegatedRecord.setOperateTime(rs.getLong("ioperatetime"));
                repDelegatedRecord.setPerformedOperationId(rs.getInt("ioperateid"));
                repDelegatedRecord.setTaskId(new Long(rs.getLong("itaskid")));
                repDelegatedRecord.setTaskState(rs.getString("istate"));
                list.add(repDelegatedRecord);
            }

            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取TaskConstant
     * 
     * @param sql
     * @return
     */
    public final static List queryTaskConstant ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                RepTaskConstant repTaskConstant = new RepTaskConstant();
                repTaskConstant.setId(new Long(rs.getLong("iid")));
                repTaskConstant.setActivityName(rs.getString("iactname"));
                repTaskConstant.setAllowSkip(rs.getLong("iallowskip") == 1 ? true : false);
                repTaskConstant.setCalendarName(rs.getString("icalendarname"));
                repTaskConstant.setDelegateToAssigneeOnly(rs.getLong("ideltoassonly") == 1 ? true : false);
                repTaskConstant.setDesc(rs.getString("idesc"));
                repTaskConstant.setFlowId(new Long(rs.getLong("iflowid")));
                repTaskConstant.setForwardToAssigneeOnly(rs.getLong("ifwdtoassonly") == 1 ? true : false);
                repTaskConstant.setName(rs.getString("iname"));
                repTaskConstant.setPriority(rs.getInt("ipriority"));
                repTaskConstant.setRecovered(rs.getLong("iisrecovered") == 1 ? true : false);
                repTaskConstant.setRemarkId(new Long(rs.getLong("iremarkid")));
                repTaskConstant.setStartTime(rs.getLong("istarttime"));
                repTaskConstant.setUsingFlowParticipant(rs.getLong("iusingflowpart") == 1 ? true : false);
                list.add(repTaskConstant);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取TaskConstant
     * 
     * @param sql
     * @return
     */
    public final static RepTaskConstant getTaskConstant ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepTaskConstant repTaskConstant = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                repTaskConstant = new RepTaskConstant();
                repTaskConstant.setId(new Long(rs.getLong("iid")));
                repTaskConstant.setActivityName(rs.getString("iactname"));
                repTaskConstant.setAllowSkip(rs.getLong("iallowskip") == 1 ? true : false);
                repTaskConstant.setCalendarName(rs.getString("icalendarname"));
                repTaskConstant.setDelegateToAssigneeOnly(rs.getLong("ideltoassonly") == 1 ? true : false);
                repTaskConstant.setDesc(rs.getString("idesc"));
                repTaskConstant.setFlowId(new Long(rs.getLong("iflowid")));
                repTaskConstant.setForwardToAssigneeOnly(rs.getLong("ifwdtoassonly") == 1 ? true : false);
                repTaskConstant.setName(rs.getString("iname"));
                repTaskConstant.setPriority(rs.getInt("ipriority"));
                repTaskConstant.setRecovered(rs.getLong("iisrecovered") == 1 ? true : false);
                repTaskConstant.setRemarkId(new Long(rs.getLong("iremarkid")));
                repTaskConstant.setStartTime(rs.getLong("istarttime"));
                repTaskConstant.setUsingFlowParticipant(rs.getLong("iusingflowpart") == 1 ? true : false);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repTaskConstant;
    }

    /**
     * 获取NoticeConfirmedUser
     * 
     * @param sql
     * @return
     */
    public final static List getNoticeConfirmedUser ( String sql )
    {

        List repNoticeConfirmedUser = new ArrayList();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepNoticeConfirmedUser rncu = new RepNoticeConfirmedUser();
                rncu.setId(new Long(rs.getLong("iid")));
                rncu.setLastConfirmTime(rs.getLong("ilastCfmTime"));
                rncu.setNoticeId(new Long(rs.getLong("iuserid")));
                rncu.setUserFullName(rs.getString("iusername"));
                rncu.setUserId(new Long(rs.getLong("iuserid")));
                repNoticeConfirmedUser.add(rncu);
            }

            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repNoticeConfirmedUser;
    }

    /**
     * 获取日历
     * 
     * @param sql
     * @return
     */
    public final static RepCalendar getCalendar ( String sql )
    {
        RepCalendar repCalendar = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                repCalendar = new RepCalendar();
                repCalendar.setId(new Long(rs.getLong("iid")));
                repCalendar.setDescription(rs.getString("idesc"));
                repCalendar.setName(rs.getString("iname"));
                repCalendar.setDefault(rs.getInt("idefault") == 1 ? true : false);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repCalendar;
    }

    /**
     * 获取所有日历
     * 
     * @param sql
     * @return
     */
    public final static List queryAllCalendar ( String sql )
    {
        RepCalendar repCalendar = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List result = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                repCalendar = new RepCalendar();
                repCalendar.setId(new Long(rs.getLong("iid")));
                repCalendar.setDescription(rs.getString("idesc"));
                repCalendar.setName(rs.getString("iname"));
                repCalendar.setDefault(rs.getInt("idefault") == 1 ? true : false);
                result.add(repCalendar);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return result;
    }

    /**
     * 获取目标
     * 
     * @param sql
     * @return
     */
    public final static RepServiceNode getServiceNode ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepServiceNode snode = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                snode = new RepServiceNode();
                snode.setId(new Long(rs.getLong("iid")));
                snode.setPort(new Integer(rs.getInt("iport")));
                snode.setHost(rs.getString("ihost"));
                snode.setState(new Integer(rs.getInt("istate")));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return snode;
    }

    /**
     * 获取MailServerSetting
     * 
     * @param sql
     * @return
     */
    public final static List getMailServerSetting ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List settings = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                RepMailServerSetting rss = new RepMailServerSetting();
                rss.setFromUser(rs.getString("ifromuser"));
                rss.setId(new Long(rs.getLong("iid")));
                rss.setPassword(rs.getString("ipassword"));
                rss.setProxyPassword(rs.getString("iproxypassword"));
                rss.setProxyPort(new Integer(rs.getInt("iproxyport")));
                rss.setProxyRequired(rs.getLong("iproxyrequired") == 1 ? true : false);
                rss.setProxyServerAddr(rs.getString("iproxyaddress"));
                rss.setProxyType(rs.getString("iproxytype"));
                rss.setProxyUserName(rs.getString("iproxyuser"));
                rss.setSmtpServerAddr(rs.getString("iserveraddr"));
                rss.setSmtpServerPort(new Integer(rs.getInt("iserverport")));
                rss.setUser(rs.getString("iuser"));
                rss.setUserName(rs.getString("iusername"));
                settings.add(rss);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return settings;
    }

    /**
     * 获取AppLogRecord
     * 
     * @param sql
     * @return
     */
    public final static RepAppLogRecord getAppLogRecord ( String sql, int type )
    {
        RepAppLogRecord applog = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                applog = new RepAppLogRecord();
                applog.setFlowId(new Long(rs.getLong("iflowid")));
                applog.setFlowName(rs.getString("iflowname"));
                applog.setFlowInsName(rs.getString("iflowinsname"));
                applog.setLogContent(rs.getString("icontent"));
                applog.setPrjName(rs.getString("iprjname"));
                applog.setTime(rs.getLong("itime"));
                applog.setType(rs.getInt("itype"));
                applog.setUserFullName(rs.getString("iuser"));
                applog.setUserId(new Long(rs.getLong("iuserid")));
                applog.setTaskId(new Long(rs.getLong("itaskid")));
                applog.setId(new Long(rs.getLong("iid")));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return applog;
    }

    /**
     * 获取工作流线程设置
     * 
     * @param sql
     * @return
     */
    static public final List queryFlowPoolNum ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List result = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepFlowPoolNum bean = new RepFlowPoolNum();
                bean.setId(rs.getLong("iid"));
                bean.setAgentIP(rs.getString("iagentip"));
                bean.setPrjFlowName(rs.getString("iprjflowname"));
                bean.setFlowPoolNum(rs.getString("iflowpoolnum"));
                result.add(bean);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return result;
    }

    /**
     * 获取最大线程占用数
     * 
     * @param sql
     * @return
     */
    static public final List getFlowPoolNum ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List result = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                Object obj = rs.getString("iflowpoolnum");
                result.add(obj);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return result;
    }

    /**
     * 获取 工作流线程设置信息
     * 
     * @param sql
     * @return
     */
    static public final List queryPrjFlowPoolNum ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List result = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                Object[] obj = { rs.getString("iprjflowname"), rs.getString("iflowpoolnum") };
                result.add(obj);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return result;
    }

    /**
     * 获取所有用户
     * 
     * @param sql
     * @return
     */
    static public final List queryAllUser ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List users = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepUser repUser = new RepUser();
                repUser.setId(new Long(rs.getLong("iid")));
                repUser.setCalendarId(new Long(rs.getLong("icalId")));
                repUser.setDepartment(rs.getString("idepartment"));
                repUser.setEmail(rs.getString("iemail"));
                repUser.setFullName(rs.getString("ifullname"));
                repUser.setLastUpdatePasswordTime(rs.getLong("ipwuptime"));
                repUser.setLocale(rs.getString("ilocale"));
                repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                repUser.setLoginName(rs.getString("iloginName"));
                repUser.setPassword(rs.getString("ipassword"));
                repUser.setTelephone(rs.getString("itelephone"));
                repUser.setUserType(rs.getInt("itype"));
                users.add(repUser);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return users;
    }
    
    //预编译写法
    public static  final List queryAllUser ( String sql, int type,Object[] params )
    {

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List users = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.prepareStatement(sql);
            if(params!=null && params.length>0) {
                for(int i=0;i<params.length;i++) {
                    stmt.setObject(i + 1, params[i]);
                    
                }
            }
            rs = stmt.executeQuery();
            while (rs.next())
            {
                RepUser repUser = new RepUser();
                repUser.setId(new Long(rs.getLong("iid")));
                repUser.setCalendarId(new Long(rs.getLong("icalId")));
                repUser.setDepartment(rs.getString("idepartment"));
                repUser.setEmail(rs.getString("iemail"));
                repUser.setFullName(rs.getString("ifullname"));
                repUser.setLastUpdatePasswordTime(rs.getLong("ipwuptime"));
                repUser.setLocale(rs.getString("ilocale"));
                repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                repUser.setLoginName(rs.getString("iloginName"));
                repUser.setPassword(rs.getString("ipassword"));
                repUser.setTelephone(rs.getString("itelephone"));
                repUser.setUserType(rs.getInt("itype"));
                users.add(repUser);
            }
//            stmt.close();
//            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return users;
    }

    // add by le_wang
    static public final List queryAllUser ( String sql, int startRow, int endRow,List<String> ls )
    {
        List users = new ArrayList();
        QueryRunner qr = new QueryRunner();
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement stmt = null;
            Connection conn = null;
            ResultSet rs = null;

            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                for (int j = 0; j < ls.size(); j++) {
                    stmt.setString(j+1,ls.get(j));
                }
                stmt.setInt(ls.size()+1, startRow);
                stmt.setInt(ls.size()+2, endRow);
                rs = stmt.executeQuery();
                String sqlGroup = "SELECT UG.INAME GROUPNAME FROM IEAI_USER_GROUP_RELATION UR,IEAI_USER_GROUP UG WHERE UR.IGROUPID=UG.IID AND UR.IUSERID=?";
                while (rs.next())
                {
                    RepUser repUser = new RepUser();
                    repUser.setId(new Long(rs.getLong("iid")));
                    repUser.setCalendarId(new Long(rs.getLong("icalId")));

                    //获取用户组 用逗号分号
                    List<Map<String, Object>> groupList = qr.query(conn, sqlGroup, new MapListHandler(), repUser.getId());
                    List<String> groupname = groupList.stream().map(stringObjectMap -> (String) stringObjectMap.get("GROUPNAME")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    repUser.setUserGroupName(String.join("，", groupname));

                    if (rs.getString("idepartment") == null)
                    {
                        repUser.setDepartment("");
                    } else
                    {
                        repUser.setDepartment(rs.getString("idepartment"));
                    }
                    if (rs.getString("IidentityID") == null)
                    {
                        repUser.setIdentityID("");
                    } else
                    {
                        repUser.setIdentityID(rs.getString("IidentityID"));
                    }
                    if (rs.getString("iemail") == null)
                    {
                        repUser.setEmail("");
                    } else
                    {
                        repUser.setEmail(rs.getString("iemail"));
                    }
                    repUser.setFullName(rs.getString("ifullname"));
                    repUser.setLastUpdatePasswordTime(rs.getLong("ipwuptime"));
                    repUser.setLocale(rs.getString("ilocale"));
                    repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                    repUser.setLoginName(rs.getString("iloginName"));
                    if (rs.getString("ipassword") == null)
                    {
                        repUser.setPassword("");
                        repUser.setrepassword("");
                    } else
                    {
                        Security.encrypt(rs.getString("ipassword").toCharArray());
                        repUser.setPassword("********");
                        repUser.setrepassword("********");
                    }
                    if (rs.getString("itelephone") == null)
                    {
                        repUser.setTelephone("");
                    } else
                    {
                        repUser.setTelephone(rs.getString("itelephone"));
                    }
                    repUser.setUserType(rs.getInt("iusertype"));
                    repUser.setiisadmin(rs.getInt("IISADMIN"));
                    repUser.setIid(new Long(rs.getLong("iid")));
                    repUser.setIscheck(rs.getInt("ITYPE"));
                    
                    repUser.setCreateTime(parseStrToTime(String.valueOf(rs.getLong("ICREATETIME"))));
                    repUser.setModifyTime(parseStrToTime(String.valueOf(rs.getLong("IMODIFYTIME"))));
                    repUser.setIqyWechat(rs.getString("IQYWECHAT"));
                    //南京银行增加
                    boolean njSwitch= Environment.getInstance().getNjOrgmanagementUpdateSwitch();
                    if(njSwitch){
                        repUser.setIdepartmentid(rs.getLong("idepartmentid"));
                        //查询组织表是否有数据
                        int num= ObjectStorerDB.getDeptCount(conn);
                        List<Map> dataList = new ArrayList<>();
                        /*if(num>0){
                            dataList = getDeptTreeList(conn);
                            getDeptTreeChildList(conn, dataList);
                        }else{
                            _log.info("组织管理没有顶级节点数据");
                        }
*/
                        Map mp = new HashMap();
                        mp.put("children",dataList);
                        Map map = new HashMap();
                        map.put("value",rs.getString("idepartment")==null?"":rs.getString("idepartment"));
                        map.put("data",mp);
                        repUser.setDept(map);
                    }
                    users.add(repUser);
                }
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return users;
    }
    
    
    static public final List queryAllUserHD ( String sql,String scriptQueryName,String scriptQueryDepartment,String usestate,String userGroupId,String utype,String condition,  int startRow, int endRow )
    {
        List users = new ArrayList();
        QueryRunner qr = new QueryRunner();
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement stmt = null;
            Connection conn = null;
            ResultSet rs = null;

            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                int parameterIndex = 1;
                if (usestate != null && !"".equals(usestate) && !"null".equals(usestate) && !"-1".equals(usestate)) {
                    stmt.setInt(parameterIndex++, Integer.parseInt(usestate));
                }

                if (StringUtils.isNotBlank(userGroupId) && !StringUtils.equals(userGroupId, "0")) {
                    stmt.setInt(parameterIndex++, Integer.parseInt(userGroupId));
                }

                stmt.setString(parameterIndex++, utype);

                if (scriptQueryName.isEmpty() && scriptQueryDepartment.isEmpty()) {
                    /*if (condition != null && (condition.contains("_") || condition.contains("%"))) {
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                    }*/
                    if (condition == null || "".equals(condition))
                    {

                    } else if (condition.contains("_") || condition.contains("%"))
                    {
                        if(JudgeDB.IEAI_DB_TYPE==3){
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        }else{
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                            stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        }

                    } else
                    {
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                        stmt.setString(parameterIndex++, "%" + escapeSqlSpecialChar(condition) + "%");
                    }/*else {
                        stmt.setString(parameterIndex++, "%" + condition + "%");
                        stmt.setString(parameterIndex++, "%" + condition + "%");
                        stmt.setString(parameterIndex++, "%" + condition + "%");
                        stmt.setString(parameterIndex++, "%" + condition + "%");
                        stmt.setString(parameterIndex++, "%" + condition + "%");
                    }*/
                } else {
                    if (!scriptQueryName.isEmpty()) {
                        stmt.setString(parameterIndex++, "%" + scriptQueryName + "%");
                    }
                    if (!scriptQueryDepartment.isEmpty()) {
                        stmt.setString(parameterIndex++, "%" + scriptQueryDepartment + "%");
                    }
                }
                if (JudgeDB.IEAI_DB_TYPE != 3)
                {
                    stmt.setInt(parameterIndex++, startRow);
                    stmt.setInt(parameterIndex++, endRow);
                }else {
                    stmt.setInt(parameterIndex++, endRow);
                    stmt.setInt(parameterIndex++, startRow);
                    
                }
                
                rs = stmt.executeQuery();
                String sqlGroup = "SELECT UG.INAME GROUPNAME FROM IEAI_USER_GROUP_RELATION UR,IEAI_USER_GROUP UG WHERE UR.IGROUPID=UG.IID AND UR.IUSERID=?";
                while (rs.next())
                {
                    RepUser repUser = new RepUser();
                    repUser.setId(new Long(rs.getLong("iid")));
                    repUser.setCalendarId(new Long(rs.getLong("icalId")));

                    //获取用户组 用逗号分号
                    List<Map<String, Object>> groupList = qr.query(conn, sqlGroup, new MapListHandler(), repUser.getId());
                    List<String> groupname = groupList.stream().map(stringObjectMap -> (String) stringObjectMap.get("GROUPNAME")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    repUser.setUserGroupName(String.join("，", groupname));

                    if (rs.getString("idepartment") == null)
                    {
                        repUser.setDepartment("");
                    } else
                    {
                        repUser.setDepartment(rs.getString("idepartment"));
                    }
                    if (rs.getString("IidentityID") == null)
                    {
                        repUser.setIdentityID("");
                    } else
                    {
                        repUser.setIdentityID(rs.getString("IidentityID"));
                    }
                    if (rs.getString("iemail") == null)
                    {
                        repUser.setEmail("");
                    } else
                    {
                        repUser.setEmail(rs.getString("iemail"));
                    }
                    repUser.setFullName(rs.getString("ifullname"));
                    repUser.setLastUpdatePasswordTime(rs.getLong("ipwuptime"));
                    repUser.setLocale(rs.getString("ilocale"));
                    repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                    repUser.setLoginName(rs.getString("iloginName"));
                    if (rs.getString("ipassword") == null)
                    {
                        repUser.setPassword("");
                        repUser.setrepassword("");
                    } else
                    {
                        Security.encrypt(rs.getString("ipassword").toCharArray());
                        repUser.setPassword("********");
                        repUser.setrepassword("********");
                    }
                    if (rs.getString("itelephone") == null)
                    {
                        repUser.setTelephone("");
                    } else
                    {
                        repUser.setTelephone(rs.getString("itelephone"));
                    }
                    repUser.setUserType(rs.getInt("iusertype"));
                    repUser.setiisadmin(rs.getInt("IISADMIN"));
                    repUser.setIid(new Long(rs.getLong("iid")));
                    repUser.setIscheck(rs.getInt("ITYPE"));
                    
                    repUser.setCreateTime(parseStrToTime(String.valueOf(rs.getLong("ICREATETIME"))));
                    repUser.setModifyTime(parseStrToTime(String.valueOf(rs.getLong("IMODIFYTIME"))));
                    repUser.setIqyWechat(rs.getString("IQYWECHAT"));
                    //南京银行增加
                    boolean njSwitch= Environment.getInstance().getNjOrgmanagementUpdateSwitch();
                    if(njSwitch){
                        repUser.setIdepartmentid(rs.getLong("idepartmentid"));
                        //查询组织表是否有数据
                        int num= ObjectStorerDB.getDeptCount(conn);
                        List<Map> dataList = new ArrayList<>();
                        /*if(num>0){
                            dataList = getDeptTreeList(conn);
                            getDeptTreeChildList(conn, dataList);
                        }else{
                            _log.info("组织管理没有顶级节点数据");
                        }
*/
                        Map mp = new HashMap();
                        mp.put("children",dataList);
                        Map map = new HashMap();
                        map.put("value",rs.getString("idepartment")==null?"":rs.getString("idepartment"));
                        map.put("data",mp);
                        repUser.setDept(map);
                    }
                    users.add(repUser);
                }
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return users;
    }
    
    public static String escapeSqlSpecialChar ( String str )
    {
        //先把一个\反斜线替换成两个\\
        str = str.replaceAll("\\\\", "\\\\\\\\");
        //再次转义
        str= str.trim().replace("_", "\\_").replace("'", "''").replace("%", "\\%")
                .replace("\\\\","\\\\\\\\").replace("\"", "\\\"");

        return str;
    }

    /**
     * 查询组织顶级
     * @param conn
     * @return
     */
    public static List<Map> getDeptTreeList(Connection conn) throws Exception{
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select a.iid, a.iname, a.iparentid, ( select count(1) from ieai_orgmanagement b where b.iparentid = a.iid ) as leaf from ieai_orgmanagement a where a.iparentid = 0 order by a.iid asc";

        List<Map> list = new ArrayList();
        try {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                Map map = new HashMap();
                map.put("iid",rs.getLong("iid"));
                map.put("text",rs.getString("iname"));
                map.put("iparentid",rs.getLong("iparentid"));
                if(rs.getLong("leaf")==0){
                    map.put("leaf",true);
                }
                map.put("children",new ArrayList<Map>());
                list.add(map);
            }
        } catch (Exception e) {
            throw new Exception("组织查询异常", e);
        } finally {
            DBResource.closePSRS(rs, ps, "getDeptTreeList", _log);
        }
        return list;
    }


    /**
     * 查询部门子级
     * @param conn
     * @param list
     */
    public static  List<Map> getDeptTreeChildList(Connection conn, List<Map> list) {
        String sql = "select count(iid) from ieai_orgmanagement where iparentid=?";
        String childSql = "select a.iid, a.iname, a.iparentid, ( select count(1) from ieai_orgmanagement b where b.iparentid = a.iid ) as leaf from ieai_orgmanagement a where a.iparentid = ? order by a.iid asc";
        try {
            for (int i = 0; i < list.size(); i++) {
                List<Map> childList = (List<Map>) list.get(i).get("children");
                List<Map> childList2 = new ArrayList<>();
                int count = 0;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try{
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, Long.parseLong(list.get(i).get("iid").toString()));
                    rs = ps.executeQuery();
                    while (rs.next()) {
                        count = rs.getInt(1);
                    }
                }catch (Exception e) {
                    _log.error("递归查询部门count数 异常", e);
                } finally {
                    DBResource.closePSRS(rs, ps, "getDeptTreeChildList", _log);
                }

                if (count > 0) {
                    PreparedStatement ps1 = null;
                    ResultSet rs1 = null;
                    try{
                        ps1 = conn.prepareStatement(childSql);
                        ps1.setLong(1, Long.parseLong(list.get(i).get("iid").toString()));
                        rs1 = ps1.executeQuery();
                        while (rs1.next()) {
                            Map map = new HashMap();
                            map.put("iid",rs1.getLong("iid"));
                            map.put("text",rs1.getString("iname"));
                            map.put("iparentid",rs1.getLong("iparentid"));
                            if(rs1.getLong("leaf")==0){
                                map.put("leaf",true);
                            }else{
                                map.put("children",new ArrayList<Map>());
                            }


                            childList.add(map);
                        }

                    }catch (Exception e) {
                        _log.error("递归查询部门 异常", e);
                    } finally {
                        DBResource.closePSRS(rs1, ps1, "getDeptTreeChildList", _log);
                    }
                    childList2 = getDeptTreeChildList(conn, childList);
                    if(childList2.size() == 0){
                        return childList;
                    }
                }
            }
        } catch (Exception e) {
            _log.error("递归查询部门 异常", e);
        }
        return list;
    }

    /**
     * 查询是否有子节点
     * @param conn
     * @param iid
     * @return
     * @throws RepositoryException
     */
    private static int getIsParent(Connection conn,long iid) throws RepositoryException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int num=0;
        try{
            String sql = " select count(*) from IEAI_ORGMANAGEMENT where IPARENTID=? ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                num = rs.getInt(1);
            }
        }catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getIsParent", _log);
        }
        return num;
    }
    public static final int getTotalCount ( String sql,List<String> list )
    {
        int count = 0;
        for (int i = 0; i < 10; i++)
        {
            Connection conn = null;
            PreparedStatement stmt = null;
            ResultSet rs = null;
            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                for (int j = 0; j < list.size(); j++) {
                    stmt.setString(j+1,list.get(j));
                }
                rs = stmt.executeQuery();
                rs.next();
                count = Integer.parseInt(rs.getString(1));
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return count;
    }
    public static final int getTotalCount ( String sql )
    {
        int count = 0;
        for (int i = 0; i < 10; i++)
        {
            Connection conn = null;
            Statement stmt = null;
            ResultSet rs = null;
            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.createStatement();
                rs = stmt.executeQuery(sql);
                rs.next();
                count = Integer.parseInt(rs.getString(1));
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return count;
    }
    public static boolean updatePwd ( String logId, String oldPwd, String newPwd ) throws RepositoryException, ServerException
    {
        String msg = "";
        boolean flag = false;
            Map<String, Connection> mapCon = new HashMap<String, Connection>();
            Map<String, String> mapSuccess = new HashMap<String, String>();
            Connection conn = null;
            PreparedStatement ps = null;
            ResultSet rs = null;
            try
            {
                mapCon = ProjectManager.getInstance().getMapDBsourceList();
            } catch (PackException e2)
            {
                msg = "多写获取数据源失败";
            }

            if (mapCon.isEmpty())
            {
            } else
            {
                for (Map.Entry<String, Connection> mapConnection : mapCon.entrySet())
                {
                    try
                    {
                        conn = mapConnection.getValue();
                        updatePwd(logId, newPwd, conn);
                        conn.commit();
                        flag = true;
                        mapSuccess.put(mapConnection.getKey(), mapConnection.getKey());
                    } catch (Exception e)
                    {
                        _log.info("saveUserPasswordHis is error messages " + e.getMessage());
                        EngineRepositotyJdbc.getInstance().updatePwdResource(logId, oldPwd, mapSuccess);
                        flag = false;
                        break;
                    } finally
                    {
                        try
                        {
                            if (conn != null)
                            {
                                conn.close();
                            }
                        } catch (SQLException e)
                        {
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                        }
                    }
                }
            }
        
        return flag;
    }
    public static final boolean updatePwd ( String loginname, String pwd , Connection conn) throws DBException, RepositoryException
    {
        boolean flag = false;
        PreparedStatement ps = null;
        String sql = "";
        try
        {
            if(CommonConfigEnv.isCheckFirstLoginSwitchDefault()){
                sql = "update  ieai_user set  ipassword=?,iinitpwd=null, ipwuptime=FUN_GET_DATE_NUMBER_NEW( "
                        + Constants.getCurrentSysDate() + ", 8) " + " where ILOGINNAME=?";
               
            }else{
                sql = "update  ieai_user set  ipassword=?, ipwuptime=FUN_GET_DATE_NUMBER_NEW( "
                        + Constants.getCurrentSysDate() + ", 8) " + " where ILOGINNAME=?";
               
            }
             ps = conn.prepareStatement(sql);
            ps.setString(1, Security.encrypt(pwd.toCharArray()));
            ps.setString(2, loginname);
            ps.executeUpdate();
            conn.commit();
            flag = true;
            ps.close();
        } catch (Exception e)
        {
            if (conn != null)
                try
                {
                    conn.rollback();
                } catch (SQLException e1)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
                }
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement( ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return flag;
    }

    public static final List<RepUserBack> saveUser ( RepUser user, boolean fla, List<RepUserBack> listrep,
            List userlist, Connection con ) throws RepositoryException
    {
        boolean flag = false;
        String msg = "";

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rset = null;
        ResultSet rset1 = null;
        String sql = "";
        int count = 0;
        boolean njSwitch=Environment.getInstance().getNjOrgmanagementUpdateSwitch();
        sql = "select count(iid) as cou from ieai_user where iid=?";
        try
        {
            ps1 = con.prepareStatement(sql);
            ps1.setLong(1, user.getId());
            rset1 = ps1.executeQuery();
            while (rset1.next())
            {
                count = rset1.getInt("cou");
            }
            ps1.close();
            rset1.close();
            if (count > 0)
            {

                if (!fla)
                {
                    sql = "select iid,ifullname,iloginName,idepartment,ipassword,IidentityID,iemail,iusertype,itelephone,ipwuptime from ieai_user where iid=?";
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, user.getId());
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        RepUserBack repUser = new RepUserBack();
                        repUser.setFullName(rset.getString("ifullname"));
                        repUser.setLoginName(rset.getString("iloginName"));
                        repUser.setDepartMent(rset.getString("idepartment"));
                        repUser.setPassword(rset.getString("ipassword"));
                        repUser.setIdentityID(rset.getString("IidentityID"));
                        repUser.setEmail(rset.getString("iemail"));
                        repUser.setUserType(rset.getInt("iusertype"));
                        repUser.setTelephone(rset.getString("itelephone"));
                        repUser.setPwuptime(rset.getLong("ipwuptime"));
                        repUser.setIid(rset.getLong("iid"));
                        repUser.setFlagtype("updatedb");
                        listrep.add(repUser);
                    }
                    ps.close();
                    rset.close();
                }

                if ("********".equals(user.getPassword()))
                {
                    if(njSwitch){
                        sql = "update  ieai_user set ifullname=?,iloginName=?,idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,iqywechat=? ,IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8),idepartmentid=?"
                                + " where iid=?";
                        ps = con.prepareStatement(sql);
                        ps.setString(1, user.getFullName());
                        ps.setString(2, user.getLoginName());
                        ps.setString(3, user.getDepartment());
                        ps.setString(4, user.getIdentityID());
                        ps.setString(5, user.getEmail());
                        ps.setInt(6, user.getUserType());
                        ps.setString(7, user.getTelephone());
                        ps.setString(8, user.getIqyWechat());
                        ps.setLong(9, user.getIdepartmentid());
                        ps.setLong(10, user.getId());
                    }else{
                        sql = "update  ieai_user set ifullname=?,iloginName=?,idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,iqywechat=? ,IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8)"
                                + " where iid=?";
                        ps = con.prepareStatement(sql);
                        ps.setString(1, user.getFullName());
                        ps.setString(2, user.getLoginName());
                        ps.setString(3, user.getDepartment());
                        ps.setString(4, user.getIdentityID());
                        ps.setString(5, user.getEmail());
                        ps.setInt(6, user.getUserType());
                        ps.setString(7, user.getTelephone());
                        ps.setString(8, user.getIqyWechat());
                        ps.setLong(9, user.getId());
                    }
                } else
                {
                    if("".equals(user.getPassword())){
                        sql = "update  ieai_user set ifullname=?,iloginName=?,idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,iqywechat=? ,ipwuptime=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8) , IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8)" + "  where iid=?";
                        ps = con.prepareStatement(sql);
                        ps.setString(1, user.getFullName());
                        ps.setString(2, user.getLoginName());
                        ps.setString(3, user.getDepartment());
                        ps.setString(4, user.getIdentityID());
                        ps.setString(5, user.getEmail());
                        ps.setInt(6, user.getUserType());
                        ps.setString(7, user.getTelephone());
                        ps.setString(8, user.getIqyWechat());
                        ps.setLong(9, user.getId());
                    }else{
                        if(njSwitch){
                            sql = "update  ieai_user set ifullname=?,iloginName=?, ipassword=?, idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,iqywechat=? ,ipwuptime=FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate() + ", 8) , IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate() + ", 8)" + " , idepartmentid=?  where iid=?";
                            ps = con.prepareStatement(sql);
                            ps.setString(1, user.getFullName());
                            ps.setString(2, user.getLoginName());
                            if ("".equals(user.getPassword()))
                            {
                                ps.setString(3, user.getPassword());
                            } else
                            {
                                ps.setString(3, Security.encrypt(user.getPassword().toCharArray()));
                            }
                            ps.setString(4, user.getDepartment());
                            ps.setString(5, user.getIdentityID());
                            ps.setString(6, user.getEmail());
                            ps.setInt(7, user.getUserType());
                            ps.setString(8, user.getTelephone());
                            ps.setString(9, user.getIqyWechat());
                            ps.setLong(10, user.getIdepartmentid());
                            ps.setLong(11, user.getId());
                        }else {
                            sql = "update  ieai_user set ifullname=?,iloginName=?, ipassword=?, idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,iqywechat=? ,ipwuptime=FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate() + ", 8) , IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate() + ", 8)" + "  where iid=?";
                            ps = con.prepareStatement(sql);
                            ps.setString(1, user.getFullName());
                            ps.setString(2, user.getLoginName());
                            if ("".equals(user.getPassword())) {
                                ps.setString(3, user.getPassword());
                            } else {
                                ps.setString(3, Security.encrypt(user.getPassword().toCharArray()));
                            }
                            ps.setString(4, user.getDepartment());
                            ps.setString(5, user.getIdentityID());
                            ps.setString(6, user.getEmail());
                            ps.setInt(7, user.getUserType());
                            ps.setString(8, user.getTelephone());
                            ps.setString(9, user.getIqyWechat());
                            ps.setLong(10, user.getId());
                        }
                    }
                    
                }
                ps.executeUpdate();
                msg = "update";
                flag = true;
                ps.close();
                //南京银行用户和组织关系
                if(njSwitch){
                    int num = 0;
                    PreparedStatement ps3 = null;
                    ResultSet rs3 = null;
                    //如果用户存在，关系表不存在，则插入
                    String sql1 = "select count(iid) as count from IEAI_ORGMANAGEMENT_USER where USERID=?";
                    try {
                        ps3 = con.prepareStatement(sql1);
                        ps3.setLong(1, user.getId());
                        rs3 = ps3.executeQuery();
                        while (rs3.next())
                        {
                            num = rs3.getInt("count");
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        _log.info("更新南京银行用户和组织关系错误");
                    }finally {
                        DBResource.closePSRS(rs3,ps3,"saveUser select IEAI_ORGMANAGEMENT_USER error",_log);
                    }
                    if(num>0){
                        PreparedStatement ps2 = null;
                        try {
                            String sql2 = "UPDATE IEAI_ORGMANAGEMENT_USER SET MANAGEMENTID=? WHERE USERID=?";
                            ps2 = con.prepareStatement(sql2);
                            ps2.setLong(1, user.getIdepartmentid());
                            ps2.setLong(2, user.getId());
                            ps2.executeUpdate();
                        }catch (Exception e){
                            e.printStackTrace();
                            _log.info("更新南京银行用户和组织关系错误");
                        }finally {
                            DBResource.closePreparedStatement(ps2,"saveUser UPDATE IEAI_ORGMANAGEMENT_USER error",_log);
                        }
                    }else{
                        PreparedStatement ps2 = null;
                        try {
                            String sql2 = "INSERT INTO IEAI_ORGMANAGEMENT_USER (IID, MANAGEMENTID, USERID) VALUES (?,?,?)";
                            ps2 = con.prepareStatement(sql2);
                            long id = IdGenerator.createId("IEAI_ORGMANAGEMENT_USER", con);
                            ps2.setLong(1, id);
                            ps2.setLong(2, user.getIdepartmentid());
                            ps2.setLong(3, user.getId());
                            ps2.executeUpdate();
                        }catch (Exception e){
                            e.printStackTrace();
                            _log.info("新增南京银行用户和组织关系错误");
                        }finally {
                            DBResource.closePreparedStatement(ps2,"saveUser INSERT IEAI_ORGMANAGEMENT_USER error",_log);
                        }
                    }

                }
            } else
            {
                if(CommonConfigEnv.isCheckFirstLoginSwitchDefault()){
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        if(njSwitch){
                            sql = "INSERT INTO ieai_user (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat,idepartmentid) SELECT ?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),?,? FROM dual WHERE not exists (select * from ieai_user where ILOGINNAME=?)";
                        }else{
                            sql = "INSERT INTO ieai_user (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat) SELECT ?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),? FROM dual WHERE not exists (select * from ieai_user where ILOGINNAME=?)";
                        }
                    } else
                    {
                        if(njSwitch){
                            sql = "merge into ieai_user "
                                    + " using (select count(*) usercount from ieai_user where ILOGINNAME=? ) aa "
                                    + " on (aa.usercount>0) " + " when not matched then "
                                    + " INSERT (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat,idepartmentid) "
                                    + " values(?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),?,?)";
                        }else{
                            sql = "merge into ieai_user "
                                    + " using (select count(*) usercount from ieai_user where ILOGINNAME=? ) aa "
                                    + " on (aa.usercount>0) " + " when not matched then "
                                    + " INSERT (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat) "
                                    + " values(?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),?)";
                        }
                    }
                }else{
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        if(njSwitch){
                            sql = "INSERT INTO ieai_user (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat,idepartmentid) SELECT ?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),?,? FROM dual WHERE not exists (select * from ieai_user where ILOGINNAME=?)";
                        }else{
                            sql = "INSERT INTO ieai_user (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat) SELECT ?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( "
                                    + Constants.getCurrentSysDate()
                                    + ", 8),? FROM dual WHERE not exists (select * from ieai_user where ILOGINNAME=?)";
                        }

                    } else
                    {

                        if(njSwitch){
                            sql = "merge into ieai_user "
                                    + " using (select count(*) usercount from ieai_user where ILOGINNAME=? ) aa "
                                    + " on (aa.usercount>0) " + " when not matched then "
                                    + " INSERT (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat,idepartmentid) "
                                    + " values(?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),?,?)";
                        }else{
                            sql = "merge into ieai_user "
                                    + " using (select count(*) usercount from ieai_user where ILOGINNAME=? ) aa "
                                    + " on (aa.usercount>0) " + " when not matched then "
                                    + " INSERT (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime,iqywechat) "
                                    + " values(?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                    + ", 8),?)";
                        }
                    }
                }
                long iid = 0;
                if (!fla)
                {
                    iid = IdGenerator.createIdForExecAct(RepUser.class, con);
                    user.setId(iid);
                }
                ps = con.prepareStatement(sql);
                if (JudgeDB.IEAI_DB_TYPE == 3)
                {
                    ps.setString(1, user.getDepartment());
                    ps.setString(2, user.getIdentityID());
                    ps.setString(3, user.getEmail());
                    ps.setString(4, user.getFullName());
                    ps.setLong(5, user.getId());//
                    ps.setString(6, user.getLoginName());
                    if ("".equals(user.getPassword()))
                    {
                        ps.setString(7, user.getPassword());
                    } else
                    {
                        ps.setString(7, Security.encrypt(user.getPassword().toCharArray()));
                    }
                    ps.setString(8, user.getTelephone());
                    ps.setString(9, user.getLocale());// null
                    if (user.getCalendarId() == null)
                    {
                        ps.setLong(10, -1);// -1
                    } else
                    {
                        ps.setLong(10, user.getCalendarId().longValue());// -1
                    }
                    ps.setLong(11, (user.isLocked() == true ? 1 : 0));

                    ps.setInt(12, user.getUserType());
                    ps.setString(13, user.getIqyWechat());
                    ps.setString(14, user.getLoginName());
                    if(njSwitch){
                        ps.setLong(14, user.getIdepartmentid());
                        ps.setString(15, user.getLoginName());
                    }else{
                        ps.setString(14, user.getLoginName());
                    }
                } else
                {
                    ps.setString(1, user.getLoginName());
                    ps.setString(2, user.getDepartment());
                    ps.setString(3, user.getIdentityID());
                    ps.setString(4, user.getEmail());
                    ps.setString(5, user.getFullName());
                    ps.setLong(6, user.getId());//
                    ps.setString(7, user.getLoginName());
                    if ("".equals(user.getPassword()))
                    {
                        ps.setString(8, user.getPassword());
                    } else
                    {
                        ps.setString(8, Security.encrypt(user.getPassword().toCharArray()));
                    }
                    ps.setString(9, user.getTelephone());
                    ps.setString(10, user.getLocale());// null
                    if (user.getCalendarId() == null)
                    {
                        ps.setLong(11, -1);// -1
                    } else
                    {
                        ps.setLong(11, user.getCalendarId().longValue());// -1
                    }
                    ps.setLong(12, (user.isLocked() == true ? 1 : 0));
                    ps.setInt(13, user.getUserType());
                    ps.setString(14, user.getIqyWechat());
                    if(njSwitch){
                        ps.setLong(15,user.getIdepartmentid());
                    }
                }
                int nn = ps.executeUpdate();
                ps.close();
                flag = true;
                if (nn == 0)
                {
                    msg = "登陆ID：" + user.getLoginName() + "已经存在";
                } else
                {
                    msg = "save";
                    if (!fla)
                    {
                        RepUserBack repUser = new RepUserBack();
                        repUser.setIid(iid);
                        repUser.setFlagtype("deldb");
                        listrep.add(repUser);
                    }
                }
                //南京银行用户和组织关系
                if(njSwitch){
                    PreparedStatement ps2 = null;
                    try {
                        String sql2 = "INSERT INTO IEAI_ORGMANAGEMENT_USER (IID, MANAGEMENTID, USERID) VALUES (?,?,?)";
                        ps2 = con.prepareStatement(sql2);
                        long id = IdGenerator.createId("IEAI_ORGMANAGEMENT_USER", con);
                        ps2.setLong(1, id);
                        ps2.setLong(2, user.getIdepartmentid());
                        ps2.setLong(3, user.getId());
                        ps2.executeUpdate();
                    }catch (Exception e){
                        e.printStackTrace();
                        _log.info("新增南京银行用户和组织关系错误");
                    }finally {
                        DBResource.closePreparedStatement(ps2,"saveUser INSERT IEAI_ORGMANAGEMENT_USER error",_log);
                    }
                }
            }

        } catch (SQLException e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
            }
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePSRS(rset1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        if (!fla)
        {
            RepUserBack repUser = new RepUserBack();
            repUser.setFlag(flag);
            repUser.setMsg(msg);
            repUser.setIid(user.getId());
            listrep.add(repUser);
        }
        return listrep;
    }
    
  //光大单点用户同步：密码使用银行的加密，非本系统加密
    public static final List<RepUserBack> saveUserNoSecurity ( RepUser user, boolean fla, List<RepUserBack> listrep,
            List userlist, Connection con ) throws RepositoryException
    {
        boolean flag = false;
        String msg = "";

        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rset = null;
        ResultSet rset1 = null;
        String sql = "";
        int count = 0;
        sql = "select count(iid) as cou from ieai_user where iid=?";
        try
        {
            ps1 = con.prepareStatement(sql);
            ps1.setLong(1, user.getId());
            rset1 = ps1.executeQuery();
            while (rset1.next())
            {
                count = rset1.getInt("cou");
            }
            ps1.close();
            rset1.close();
            if (count > 0)
            {

                if (!fla)
                {
                    sql = "select iid,ifullname,iloginName,idepartment,ipassword,IidentityID,iemail,iusertype,itelephone,ipwuptime from ieai_user where iid=?";
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, user.getId());
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        RepUserBack repUser = new RepUserBack();
                        repUser.setFullName(rset.getString("ifullname"));
                        repUser.setLoginName(rset.getString("iloginName"));
                        repUser.setDepartMent(rset.getString("idepartment"));
                        repUser.setPassword(rset.getString("ipassword"));
                        repUser.setIdentityID(rset.getString("IidentityID"));
                        repUser.setEmail(rset.getString("iemail"));
                        repUser.setUserType(rset.getInt("iusertype"));
                        repUser.setTelephone(rset.getString("itelephone"));
                        repUser.setPwuptime(rset.getLong("ipwuptime"));
                        repUser.setIid(rset.getLong("iid"));
                        repUser.setFlagtype("updatedb");
                        listrep.add(repUser);
                    }
                    ps.close();
                    rset.close();
                }

                if ("********".equals(user.getPassword()))
                {
                    sql = "update  ieai_user set ifullname=?,iloginName=?,idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                            + Constants.getCurrentSysDate() + ", 8)"
                            + " where iid=?";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, user.getFullName());
                    ps.setString(2, user.getLoginName());
                    ps.setString(3, user.getDepartment());
                    ps.setString(4, user.getIdentityID());
                    ps.setString(5, user.getEmail());
                    ps.setInt(6, user.getUserType());
                    ps.setString(7, user.getTelephone());
                    ps.setLong(8, user.getId());
                } else
                {
                    if("".equals(user.getPassword())){
                        sql = "update  ieai_user set ifullname=?,iloginName=?,idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,ipwuptime=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8) , IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8)" + "  where iid=?";
                        ps = con.prepareStatement(sql);
                        ps.setString(1, user.getFullName());
                        ps.setString(2, user.getLoginName());
                        ps.setString(3, user.getDepartment());
                        ps.setString(4, user.getIdentityID());
                        ps.setString(5, user.getEmail());
                        ps.setInt(6, user.getUserType());
                        ps.setString(7, user.getTelephone());
                        ps.setLong(8, user.getId());
                    }else{
                        sql = "update  ieai_user set ifullname=?,iloginName=?, ipassword=?, idepartment=?,IidentityID=?, iemail=?, iusertype=?, itelephone=? ,ipwuptime=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8) , IMODIFYTIME=FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate() + ", 8)" + "  where iid=?";
                        ps = con.prepareStatement(sql);
                        ps.setString(1, user.getFullName());
                        ps.setString(2, user.getLoginName());
                        if ("".equals(user.getPassword()))
                        {
                            ps.setString(3, user.getPassword());
                        } else
                        {
                            ps.setString(3, user.getPassword());
                        }
                        ps.setString(4, user.getDepartment());
                        ps.setString(5, user.getIdentityID());
                        ps.setString(6, user.getEmail());
                        ps.setInt(7, user.getUserType());
                        ps.setString(8, user.getTelephone());
                        ps.setLong(9, user.getId());
                    }
                    
                }
                ps.executeUpdate();
                msg = "update";
                flag = true;
                ps.close();
            } else
            {
                if(CommonConfigEnv.isCheckFirstLoginSwitchDefault()){
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        sql = "INSERT INTO ieai_user (idepartment,IidentityID,IINITPWD,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime) SELECT ?,?,'1',?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate()
                                + ", 8) FROM dual WHERE not exists (select * from ieai_user where ILOGINNAME=?)";
                    } else
                    {
                        sql = "merge into ieai_user "
                                + " using (select count(*) usercount from ieai_user where ILOGINNAME=? ) aa "
                                + " on (aa.usercount>0) " + " when not matched then "
                                + " INSERT (idepartment,IidentityID,IINITPWD,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime) "
                                + " values(?,?,'1',?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                + ", 8))";
                    }
                }else{
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        sql = "INSERT INTO ieai_user (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime) SELECT ?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate()
                                + ", 8),FUN_GET_DATE_NUMBER_NEW( "
                                + Constants.getCurrentSysDate()
                                + ", 8) FROM dual WHERE not exists (select * from ieai_user where ILOGINNAME=?)";
                    } else
                    {
                        sql = "merge into ieai_user "
                                + " using (select count(*) usercount from ieai_user where ILOGINNAME=? ) aa "
                                + " on (aa.usercount>0) " + " when not matched then "
                                + " INSERT (idepartment,IidentityID,iemail,ifullname,iid,iloginName,ipassword,itelephone,ilocale,icalId,ilocked,iusertype,ipwuptime,icreatetime) "
                                + " values(?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                + ", 8),FUN_GET_DATE_NUMBER_NEW( " + Constants.getCurrentSysDate()
                                + ", 8))";
                    }
                }
                long iid = 0;
                if (!fla)
                {
                    iid = IdGenerator.createIdForExecAct(RepUser.class, con);
                    user.setId(iid);
                }
                ps = con.prepareStatement(sql);
                if (JudgeDB.IEAI_DB_TYPE == 3)
                {
                    ps.setString(1, user.getDepartment());
                    ps.setString(2, user.getIdentityID());
                    ps.setString(3, user.getEmail());
                    ps.setString(4, user.getFullName());
                    ps.setLong(5, user.getId());//
                    ps.setString(6, user.getLoginName());
                    if ("".equals(user.getPassword()))
                    {
                        ps.setString(7, user.getPassword());
                    } else
                    {
                        ps.setString(7, user.getPassword());
                    }
                    ps.setString(8, user.getTelephone());
                    ps.setString(9, user.getLocale());// null
                    if (user.getCalendarId() == null)
                    {
                        ps.setLong(10, -1);// -1
                    } else
                    {
                        ps.setLong(10, user.getCalendarId().longValue());// -1
                    }
                    ps.setLong(11, (user.isLocked() == true ? 1 : 0));

                    ps.setInt(12, user.getUserType());
                    ps.setString(13, user.getLoginName());
                } else
                {
                    ps.setString(1, user.getLoginName());
                    ps.setString(2, user.getDepartment());
                    ps.setString(3, user.getIdentityID());
                    ps.setString(4, user.getEmail());
                    ps.setString(5, user.getFullName());
                    ps.setLong(6, user.getId());//
                    ps.setString(7, user.getLoginName());
                    if ("".equals(user.getPassword()))
                    {
                        ps.setString(8, user.getPassword());
                    } else
                    {
                        ps.setString(8, user.getPassword());
                    }
                    ps.setString(9, user.getTelephone());
                    ps.setString(10, user.getLocale());// null
                    if (user.getCalendarId() == null)
                    {
                        ps.setLong(11, -1);// -1
                    } else
                    {
                        ps.setLong(11, user.getCalendarId().longValue());// -1
                    }
                    ps.setLong(12, (user.isLocked() == true ? 1 : 0));
                    ps.setInt(13, user.getUserType());
                }
                int nn = ps.executeUpdate();
                ps.close();
                flag = true;
                if (nn == 0)
                {
                    msg = "登陆ID：" + user.getLoginName() + "已经存在";
                } else
                {
                    msg = "save";
                    if (!fla)
                    {
                        RepUserBack repUser = new RepUserBack();
                        repUser.setIid(iid);
                        repUser.setFlagtype("deldb");
                        listrep.add(repUser);
                    }
                }
            }

        } catch (SQLException e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
            }
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePSRS(rset1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        if (!fla)
        {
            RepUserBack repUser = new RepUserBack();
            repUser.setFlag(flag);
            repUser.setMsg(msg);
            repUser.setIid(user.getId());
            listrep.add(repUser);
        }
        return listrep;
    }

    public static int getRoleId ( String sql, String iname )
    {
        int roleid = 0;
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement stmt = null;
            Connection conn = null;

            ResultSet rs = null;
            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                stmt.setString(1, iname);
                rs = stmt.executeQuery();
                rs.next();
                roleid = Integer.parseInt(rs.getString(1));
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return roleid;
    }

    public static int getMaxIid ()
    {
        int iid = -1;
        for (int i = 0; i < 10; i++)
        {
            String sql = "select max (iid) from ieai_user ";
            PreparedStatement stmt = null;
            Connection conn = null;
            ResultSet rs = null;
            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                rs = stmt.executeQuery();
                rs.next();
                iid = Integer.parseInt(rs.getString(1));
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return iid;
    }

    static public final List queryRoleList ( String sql, int userid )
    {
        List users = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement stmt = null;
            Connection conn = null;
            ResultSet rs = null;
            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                stmt.setInt(1, userid);
                rs = stmt.executeQuery();
                while (rs.next())
                {
                    RepUser repUser = new RepUser();
                    repUser.setIroleId(new Integer(rs.getInt("IID")));// ROLEID
                    repUser.setRoleName(rs.getString("INAME"));// ROLENAME
                    repUser.setIscheck(rs.getInt("IROLEID"));// roleid是否被选中0，否
                    users.add(repUser);
                }
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return users;
    }

    static public final List queryUserList ( String sql, int roleid )
    {
        List userlist = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement stmt = null;
            Connection conn = null;
            ResultSet rs = null;
            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                stmt.setInt(1, roleid);
                rs = stmt.executeQuery();
                while (rs.next())
                {
                    RepUser repUser = new RepUser();
                    repUser.setIid(new Integer(rs.getInt("IID")));// USERID
                    repUser.setFullName(rs.getString("IFULLNAME"));// FULLNAME
                    repUser.setLoginName(rs.getString("ILOGINNAME"));// LOGINNAME
                    repUser.setIscheck(rs.getInt("IISEXTEND"));
                    repUser.setEmail(rs.getString("IEMAIL"));
                    userlist.add(repUser);
                }
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return userlist;
    }

    public static Map saveUserInheritence ( int userid, List<Integer> roleid ) throws RepositoryException
    {
        boolean flag = false;
        Map<String, Connection> mapCon = new HashMap<String, Connection>();
        Map<String, String> mapConSuccess = new HashMap<String, String>();
        Map mapmsg = new HashMap();
        String msg = "";
        List<RepUserInHeritBack> list = UserManager.getInstance().querydelUserInheritence(userid);
        List<RepUserInHeritBack> listdel = new ArrayList<RepUserInHeritBack>();
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        String sql = "INSERT INTO IEAI_USERINHERIT (IROLEID,IUSERID,IISEXTEND) VALUES(?,?,?)";
        String isextendSql="select max(iisextend) iisextend from IEAI_USERINHERIT where iroleId=?";
        //不删除业务赋权的角色
        String delSql = "DELETE FROM IEAI_USERINHERIT WHERE IUSERID=? AND IROLEID NOT IN (SELECT IID FROM IEAI_ROLE WHERE INAME = '"+userid+"')";
        try
        {
            mapCon = ProjectManager.getInstance().getMapDBsourceList();
        } catch (PackException e)
        {
            msg = "获取数据源失败";
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        if (mapCon.isEmpty())
        {
        } else
        {

            for (Map.Entry<String, Connection> mapConnection : mapCon.entrySet())
            {
                try
                {
                    con = mapConnection.getValue();
                    ps = con.prepareStatement(delSql);
                    ps.setLong(1, userid);
                    ps.executeUpdate();
                    ps = con.prepareStatement(sql);
                    for (int j = 0; j < roleid.size(); j++)
                    {
                        if (!flag)
                        {
                            RepUserInHeritBack repUserInHeritBack = new RepUserInHeritBack();
                            repUserInHeritBack.setUserId(userid);
                            repUserInHeritBack.setFlagtype("delsave");
                            listdel.add(repUserInHeritBack);
                        }
                        int iisextend=0;
                        long rolid = roleid.get(j);
                        ps1 = con.prepareStatement(isextendSql);
                        ps1.setLong(1, rolid);
                        rs1=ps1.executeQuery();
                        if(rs1.next()) {
                            iisextend=rs1.getInt("iisextend");
                        }
                        ps.setLong(1, rolid);
                        ps.setLong(2, userid);
                        ps.setInt(3, iisextend);
                        ps.executeUpdate();
                    }
                    con.commit();
                    flag = true;
                    mapConSuccess.put(mapConnection.getKey(), mapConnection.getKey());
                } catch (Exception e)
                {
                    try
                    {
                        if (null != con)
                            con.rollback();
                    } catch (SQLException e1)
                    {
                        _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " error ", e);

                    }
                    msg = "多写数据源失败";
                    flag = false;
                    EngineRepositotyJdbc.getInstance().removeUserHierDbResource(list, listdel, mapConSuccess);
                    break;
                } finally
                {
                    try
                    {
                        DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                        DBResource.closeConn(con, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                    } catch (Exception e)
                    {
                        _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    }

                }
            }

        }
        mapmsg.put("msg", msg);
        mapmsg.put("flag", flag);
        return mapmsg;
    }

    // end by le_wang

    /**
     * 获取出错任务
     * 
     * @param sql
     * @return
     */
    static public final RepErrorTask getErrorTask ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepErrorTask repErrorTask = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repErrorTask = new RepErrorTask();
                repErrorTask.setActivatedTime(new Long(rs.getLong("iactivatedtime")));
                repErrorTask.setCreatedTime(new Long(rs.getLong("icreatedtime")));
                repErrorTask.setEndTime(new Long(rs.getLong("iendtime")));
                repErrorTask.setActId(new Long(rs.getLong("iactid")));
                repErrorTask.setActName(rs.getString("iactname"));
                repErrorTask.setDefaultTaskOwnerId(new Long(rs.getLong("idefaulttaskownerid")));
                repErrorTask.setDefaultTaskOwnerName(rs.getString("idefaulttaskownername"));
                repErrorTask.setDelegateState(rs.getByte("idelegatestate"));
                if (rs.getInt("idelegatetoassigneesonly") == 0)
                {
                    repErrorTask.setDelegateToAssigneesOnly(false);
                } else
                {
                    repErrorTask.setDelegateToAssigneesOnly(true);
                }
                repErrorTask.setDescription(rs.getString("idescription"));
                repErrorTask.setFlowId(new Long(rs.getLong("iflowid")));
                repErrorTask.setFlowInstanceName(rs.getString("iflowinstancename"));
                repErrorTask.setFlowName(rs.getString("iflowname"));
                if (rs.getInt("iisforwardtoassigneesonly") == 0)
                {
                    repErrorTask.setForwardToAssigneesOnly(false);
                } else
                {
                    repErrorTask.setForwardToAssigneesOnly(true);
                }
                repErrorTask.setId(new Long(rs.getLong("iid")));
                repErrorTask.setProjectName(rs.getString("iprojectname"));
                repErrorTask.setTaskOwnerId(new Long(rs.getLong("itaskownerid")));
                repErrorTask.setTaskOwnerName(rs.getString("itaskownername"));
                repErrorTask.setTaskState(rs.getByte("itaskstate"));
                if (rs.getInt("isuseflowdefineassigness") == 0)
                {
                    repErrorTask.setUseFlowDefineAssignees(false);
                } else
                {
                    repErrorTask.setUseFlowDefineAssignees(true);
                }
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repErrorTask;
    }

    /**
     * 获取授权详细信息
     * 
     * @param sql
     * @return
     */
    public static final RepDelegatingDetail getDelegatingDetail ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepDelegatingDetail delegatingDetail = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                delegatingDetail = new RepDelegatingDetail();
                delegatingDetail.setId(new Long(rs.getLong("iid")));
                delegatingDetail.setDelegateDescription(rs.getString("idelegatedescription"));
                delegatingDetail.setDelegatedTime(new Long(rs.getLong("idelegatedtime")));
                delegatingDetail.setDelegatorId(new Long(rs.getLong("idelegatorid")));
                delegatingDetail.setDelegatorFullName(rs.getString("idelegatorfullname"));
                delegatingDetail.setFinishedTime(new Long(rs.getLong("ifinishedtime")));
                delegatingDetail.setDelegateeFullName(rs.getString("idelegateefullname"));
                delegatingDetail.setDelegateeId(new Long(rs.getLong("idelegateeid")));
                delegatingDetail.setOperationId(new Long(rs.getLong("ioperationId")));
                delegatingDetail.setErrorTaskId(new Long(rs.getLong("ierrortaskid")));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return delegatingDetail;
    }

    /**
     * 获取授权详细信息
     * 
     * @param sql
     * @return
     */
    public static final RepErrorTaskOperation getErrorTaskOperation ( String sql, int type )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepErrorTaskOperation repOperation = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repOperation = new RepErrorTaskOperation();
                repOperation.setId(new Long(rs.getLong("iid")));
                repOperation.setDescription(rs.getString("idescription"));
                repOperation.setFlowState(rs.getInt("iflowstate"));
                repOperation.setHandlMethod(rs.getInt("ihandlmethod"));
                repOperation.setTargetActId(rs.getInt("itargetactid"));
                repOperation.setTargetActName(rs.getString("itargetactname"));
                repOperation.setWaitingSecond(rs.getLong("iwaitingsecond"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repOperation;
    }

    /**
     * 获取所有活动
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    static public final List findAllActivityRuntime ( String sql ) throws RepositoryException
    {

        List result = new ArrayList();
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;

                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, Constants.IEAI_IEAI_BASIC);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        RepActivityRuntime bean = new RepActivityRuntime();
                        bean.setId(new Long(rs.getLong("iid")));
                        if (rs.getInt("iismonitoract") == 0)
                        {
                            bean.setMonitorAct(false);
                        } else
                        {
                            bean.setMonitorAct(true);
                        }
                        bean.setActivityId((rs.getLong("iactid")));
                        bean.setActivityName(rs.getString("iactname"));
                        bean.setActType(rs.getString("iacttype"));
                        bean.setArrivedTime(new Long(rs.getLong("iarrivedtime")).longValue());
                        bean.setBeginExcTime(new Long(rs.getLong("ibeginexctime")).longValue());
                        bean.setEndTime(new Long(rs.getLong("iendtime")).longValue());
                        bean.setExceptionStrcutId(rs.getLong("iexcstructId"));
                        bean.setFlowId(new Long(rs.getLong("iflowid")));
                        bean.setReadyTime(new Long(rs.getLong("ireadytime")).longValue());
                        bean.setShouldStartTime(new Long(rs.getLong("ishouldstarttime")).longValue());
                        bean.setState(rs.getString("istate"));
                        bean.setTaskId(new Long(rs.getLong("itaskid")));
                        bean.setErrorTaskId(new Long(rs.getLong("ierrortaskid")));
                        if (rs.getInt("disappear") == 0)
                        {
                            bean.setDisappear(false);
                        } else
                        {
                            bean.setDisappear(true);
                        }
                        if (rs.getInt("ihastimeconfig") == 0)
                        {
                            bean.setHasStartTimeCfg(false);
                        } else
                        {
                            bean.setHasStartTimeCfg(true);
                        }
                        bean.setDesc(rs.getString("idesc"));
                        bean.setShouldEndTime(new Long(rs.getLong("ishouldendtime")).longValue());
                        bean.setIsIgnore(new Long(rs.getLong("iisIgnore")).longValue());
                        bean.setDotTime(new Long(rs.getLong("idotTime")).longValue());
                        bean.setShouldDelayTime(new Long(rs.getLong("ishouldDelayTime")).longValue());
                        bean.setChangeUser(rs.getString("ichangeUser"));
                        result.add(bean);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at ObjectStorerDB ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return result;
    }

    static public final List findAllActivityRuntime ( String sql, int type ) throws RepositoryException
    {
        List result = new ArrayList();
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, type);
                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        RepActivityRuntime bean = new RepActivityRuntime();
                        bean.setId(new Long(rs.getLong("iid")));
                        if (rs.getInt("iismonitoract") == 0)
                        {
                            bean.setMonitorAct(false);
                        } else
                        {
                            bean.setMonitorAct(true);
                        }
                        bean.setActivityId(rs.getLong("iactid"));
                        bean.setActivityName(rs.getString("iactname"));
                        bean.setActType(rs.getString("iacttype"));
                        bean.setArrivedTime(new Long(rs.getLong("iarrivedtime")).longValue());
                        bean.setBeginExcTime(new Long(rs.getLong("ibeginexctime")).longValue());
                        bean.setEndTime(new Long(rs.getLong("iendtime")).longValue());
                        bean.setExceptionStrcutId(rs.getLong("iexcstructId"));
                        bean.setFlowId(new Long(rs.getLong("iflowid")));
                        bean.setReadyTime(new Long(rs.getLong("ireadytime")).longValue());
                        bean.setShouldStartTime(new Long(rs.getLong("ishouldstarttime")).longValue());
                        bean.setState(rs.getString("istate"));
                        bean.setTaskId(new Long(rs.getLong("itaskid")));
                        bean.setErrorTaskId(new Long(rs.getLong("ierrortaskid")));
                        if (rs.getInt("disappear") == 0)
                        {
                            bean.setDisappear(false);
                        } else
                        {
                            bean.setDisappear(true);
                        }
                        if (rs.getInt("ihastimeconfig") == 0)
                        {
                            bean.setHasStartTimeCfg(false);
                        } else
                        {
                            bean.setHasStartTimeCfg(true);
                        }
                        bean.setDesc(rs.getString("idesc"));
                        bean.setShouldEndTime(new Long(rs.getLong("ishouldendtime")).longValue());
                        bean.setIsIgnore(new Long(rs.getLong("iisIgnore")).longValue());
                        bean.setDotTime(new Long(rs.getLong("idotTime")).longValue());
                        bean.setShouldDelayTime(new Long(rs.getLong("ishouldDelayTime")).longValue());
                        bean.setChangeUser(rs.getString("ichangeUser"));
                        result.add(bean);
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at ObjectStorerDB ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return result;
    }

    /**
     * get all related RepTaskItem in db
     * 
     * @param sql
     * @return
     */
    static public final List queryRepTaskItem ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepTaskItem repTaskItem = new RepTaskItem();
                repTaskItem.setFinished(rs.getLong("ifinish") == 1 ? true : false);
                repTaskItem.setId(new Long(rs.getLong("iid")));
                repTaskItem.setName(rs.getString("iname"));
                repTaskItem.setTaskId(new Long(rs.getLong("itaskId")));
                list.add(repTaskItem);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * get all related RepOperation in db
     * 
     * @param sql
     * @return
     */
    public static final List queryRepOperation ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepOperation repOperation = new RepOperation();
                repOperation.setCurrentPerformCount(rs.getInt("iperfnum"));
                repOperation.setDesc(rs.getString("idesc"));
                repOperation.setId(new Long(rs.getLong("iid")));
                repOperation.setName(rs.getString("iname"));
                repOperation.setOperationId(rs.getInt("iopid"));
                repOperation.setReturnStateId(rs.getString("istateid"));
                repOperation.setTaskId(new Long(rs.getLong("itaskid")));
                repOperation.setThreshold(rs.getInt("ithreshold"));
                list.add(repOperation);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取WorkingTime
     * 
     * @param sql
     * @return
     */
    public static final List queryWorkingTime ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List workingtimeList = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepWorkingTime workingtime = new RepWorkingTime();
                workingtime.setId(new Long(rs.getLong("iid")));
                workingtime.setStartTime(rs.getLong("istarttime"));
                workingtime.setEndTime(rs.getLong("iendtime"));
                workingtime.setDescription(rs.getString("idesc"));
                workingtime.setType(rs.getString("itype"));
                workingtime.setParentId(rs.getLong("iparentid"));
                workingtimeList.add(workingtime);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return workingtimeList;
    }

    /**
     * get workingTimes in db
     * 
     * @param sql
     * @return
     */
    public static final RepWorkingTime getWorkingTime ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepWorkingTime workingtime = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                workingtime = new RepWorkingTime();
                workingtime.setId(new Long(rs.getLong("iid")));
                workingtime.setStartTime(rs.getLong("istarttime"));
                workingtime.setEndTime(rs.getLong("iendtime"));
                workingtime.setDescription(rs.getString("idesc"));
                workingtime.setType(rs.getString("itype"));
                workingtime.setParentId(rs.getLong("iparentid"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return workingtime;
    }

    /**
     * get all related workingDays in db
     * 
     * @param sql
     * @return
     */
    public static final List queryWorkingDays ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List workDays = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepWorkDay workday = new RepWorkDay();
                workday.setCalendarId(new Long(rs.getLong("icalid")));
                workday.setDescription(rs.getString("idesc"));
                workday.setId(new Long(rs.getLong("iid")));
                workday.setWorkDay(rs.getLong("iworkday"));
                workDays.add(workday);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return workDays;
    }

    /**
     * get all related workingDays in db
     * 
     * @param sql
     * @return
     */
    public static final RepWorkDay getWorkDay ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepWorkDay workday = new RepWorkDay();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {

                workday.setCalendarId(new Long(rs.getLong("icalid")));
                workday.setDescription(rs.getString("idesc"));
                workday.setId(new Long(rs.getLong("iid")));
                workday.setWorkDay(rs.getLong("iworkday"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return workday;
    }

    /**
     * get all related weeklyday in db
     * 
     * @param sql
     * @return
     */
    public static final List queryWeeklyDay ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List worklyDays = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepWeeklyDay repDay = new RepWeeklyDay();
                repDay.setId(new Long(rs.getLong("iid")));
                repDay.setCalendarId(new Long(rs.getLong("icalid")));
                repDay.setDayOfWeek(rs.getInt("idayoweek"));
                if (0 == rs.getInt("iworkday"))
                {
                    repDay.setWorkDay(false);
                } else
                {
                    repDay.setWorkDay(true);
                }
                worklyDays.add(repDay);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return worklyDays;
    }

    /**
     * get WeeklyDay in db
     * 
     * @param sql
     * @return
     */
    public static final RepWeeklyDay getWeeklyDay ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepWeeklyDay repDay = new RepWeeklyDay();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {

                repDay.setId(new Long(rs.getLong("iid")));
                repDay.setCalendarId(new Long(rs.getLong("icalid")));
                repDay.setDayOfWeek(rs.getInt("idayoweek"));
                if (0 == rs.getInt("iworkday"))
                {
                    repDay.setWorkDay(false);
                } else
                {
                    repDay.setWorkDay(true);
                }
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repDay;
    }

    /**
     * get all related Holidays in db
     * 
     * @param sql
     * @return
     */
    public static final List queryHolidays ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List holidays = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepHoliday repHoliday = new RepHoliday();
                repHoliday.setId(new Long(rs.getLong("iid")));
                repHoliday.setCalendarId(new Long(rs.getLong("icalid")));
                repHoliday.setDescription(rs.getString("idesc"));
                repHoliday.setEndDate(rs.getLong("ienddate"));
                repHoliday.setName(rs.getString("iname"));
                repHoliday.setStartDate(rs.getLong("istartdate"));
                holidays.add(repHoliday);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return holidays;
    }

    /**
     * get Holidays in db
     * 
     * @param sql
     * @return
     */
    public static final RepHoliday getHolidays ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepHoliday repHoliday = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repHoliday = new RepHoliday();
                repHoliday.setId(new Long(rs.getLong("iid")));
                repHoliday.setCalendarId(new Long(rs.getLong("icalid")));
                repHoliday.setDescription(rs.getString("idesc"));
                repHoliday.setEndDate(rs.getLong("ienddate"));
                repHoliday.setName(rs.getString("iname"));
                repHoliday.setStartDate(rs.getLong("istartdate"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repHoliday;
    }

    /**
     * 获取ExecError
     * 
     * @param sql
     * @return
     * @throws RepositoryException 
     */
    public static final RepExecError getExecError ( String sql ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                RepExecError repError = null;

                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log, Constants.IEAI_IEAI_BASIC);

                    stmt = conn.createStatement();
                    rs = stmt.executeQuery(sql);
                    while (rs.next())
                    {
                        repError = new RepExecError();
                        repError.setActId(rs.getInt("iactid"));
                        repError.setFlowId(rs.getLong("iflowid"));
                        repError.setActivityName(rs.getString("iactivityname"));
                        repError.setExceptionName(rs.getString("iexceptionname"));
                        repError.setMessage(rs.getString("imessage"));
                        repError.setOccurTime(rs.getLong("ioccurtime"));
                        repError.setLocation(rs.getString("ilocation"));
                        repError.setScopeId(rs.getLong("iscopeid"));
                        repError.setExecActId(rs.getLong("iexecactid"));
                        repError.setErrorStackTraceId(new Long(rs.getLong("ierrorstacktrace")));
                    }
                    stmt.close();
                    rs.close();
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at ObjectStorerDB ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                return repError;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 获取Destination
     * 
     * @param sql
     * @return
     */
    public static final RepDestinationInfo getDestination ( String sql )
    {

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepDestinationInfo rdi = new RepDestinationInfo();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                rdi.setClientId(rs.getString("iclientid"));
                rdi.setCreateTime(rs.getLong("icreatetime"));
                rdi.setDurable(rs.getLong("idurable") == 1 ? true : false);
                rdi.setId(new Long(rs.getLong("iid")));
                rdi.setName(rs.getString("iname"));
                rdi.setType(rs.getInt("itype"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return rdi;
    }

    /**
     * 获取Timeoutmailrole
     * 
     * @param sql
     * @return
     */
    public final static List getTimeoutmailrole ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                RepTimeoutMailRole repTimeoutMailRole = new RepTimeoutMailRole();
                repTimeoutMailRole.setId(new Long(rs.getLong("iid")));
                repTimeoutMailRole.setRoleId(new Long(rs.getLong("iroleid")));
                repTimeoutMailRole.setRuleId(new Long(rs.getLong("iruleid")));
                repTimeoutMailRole.setType(rs.getShort("itype"));
                list.add(repTimeoutMailRole);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取Timeoutmailuser
     * 
     * @param sql
     * @return
     */
    public final static List getTimeoutmailuser ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepTimeoutMailUser repTimeoutMailUser = new RepTimeoutMailUser();
                repTimeoutMailUser.setId(new Long(rs.getLong("iid")));
                repTimeoutMailUser.setUserId(new Long(rs.getLong("iuserid")));
                repTimeoutMailUser.setRuleId(new Long(rs.getLong("iruleid")));
                repTimeoutMailUser.setType(rs.getShort("itype"));
                list.add(repTimeoutMailUser);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }

        return list;
    }

    /**
     * 获取Taskparam
     * 
     * @param sql
     * @return
     */
    public final static List queryTaskparam ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepTaskProperty repTaskProperty = new RepTaskProperty();
                repTaskProperty.setId(new Long(rs.getLong("iid")));
                repTaskProperty.setDescription(rs.getString("idesc"));
                repTaskProperty.setName(rs.getString("iname"));
                repTaskProperty.setReadOnly(rs.getLong("ireadonly") == 1 ? true : false);
                repTaskProperty.setTaskId(new Long(rs.getLong("itaskId")));
                repTaskProperty.setType(rs.getInt("itype"));
                repTaskProperty.setValue(rs.getBlob("ivalue"));
                list.add(repTaskProperty);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取Taskparam
     * 
     * @param sql
     * @return
     */
    public final static RepTaskProperty getTaskparam ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepTaskProperty repTaskProperty = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                repTaskProperty = new RepTaskProperty();
                repTaskProperty.setId(new Long(rs.getLong("iid")));
                repTaskProperty.setDescription(rs.getString("idesc"));
                repTaskProperty.setName(rs.getString("iname"));
                repTaskProperty.setReadOnly(rs.getLong("ireadonly") == 1 ? true : false);
                repTaskProperty.setTaskId(new Long(rs.getLong("itaskId")));
                repTaskProperty.setType(rs.getInt("itype"));
                repTaskProperty.setValue(rs.getBlob("ivalue"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repTaskProperty;
    }

    /**
     * 获取NoticeCfmUser
     * 
     * @param sql
     * @return
     */
    public final static List getNoticeCfmUser ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepNoticeConfirmedUser repUser = new RepNoticeConfirmedUser();
                repUser.setId(new Long(rs.getLong("iid")));
                repUser.setLastConfirmTime(rs.getLong("ilastCfmTime"));
                repUser.setNoticeId(new Long(rs.getLong("inoticeId")));
                repUser.setUserFullName(rs.getString("iusername"));
                repUser.setUserId(new Long(rs.getLong("iuserid")));
                list.add(repUser);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取Timeoutrule
     * 
     * @param sql
     * @return
     */
    public final static List getTimeoutrule ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepTimeoutRule repTimeoutRule = new RepTimeoutRule();
                repTimeoutRule.setLastAlertTime(new Long(rs.getLong("ilastalerttime")));
                repTimeoutRule.setRuleId(new Long(rs.getLong("iruled")));
                repTimeoutRule.setRuleName(rs.getString("irulename"));
                repTimeoutRule.setTaskId(new Long(rs.getLong("itaskid")));
                list.add(repTimeoutRule);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取工程历史文件
     * 
     * @param sql
     * @return
     */
    public final static List queryProjectHistory ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List result = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                RepProjectHistory history = new RepProjectHistory();
                history.setAction(rs.getString("iaction"));
                history.setActionTime(rs.getLong("iactionTime"));
                history.setId(new Long(rs.getLong("iid")));
                history.setProjectName(rs.getString("iproject"));
                history.setUploadNum(new Integer(rs.getInt("iuploadnum")));
                history.setUserFullName(rs.getString("iuser"));
                history.setUserId(new Long(rs.getLong("iuserid")));
                history.setUuid(rs.getString("iuuid"));
                result.add(history.toCommons());
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return result;
    }

    /**
     * 获取接口历时文件
     * 
     * @param sql
     * @return
     */
    public final static List queryAdaptorHistory ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List result = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepAdaptorHistory history = new RepAdaptorHistory();
                history.setAction(rs.getString("iaction"));
                history.setActionTime(rs.getLong("iactionTime"));
                history.setAdaptorName(rs.getString("iadpname"));
                history.setId(new Long(rs.getLong("iid")));
                history.setUploadNum(new Integer(rs.getInt("iuploadnum")));
                history.setUserFullName(rs.getString("iuser"));
                history.setUserId(new Long(rs.getLong("iuserid")));
                history.setUuid(rs.getString("iuuid"));
                result.add(history.toCommons());
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return result;
    }

    /**
     * 获取有效时间
     * 
     * @param sql
     * @return
     */
    public final static List queryValidTime ( String sql )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List repRet = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                RepValidTime bean = new RepValidTime();
                bean.setId(new Long(rs.getLong("iid")));
                bean.setBenchmarkFlow(("0".equals(String.valueOf(rs.getLong("iisbenchmarkflow"))) ? false : true));
                bean.setDayAll(("0".equals(String.valueOf(rs.getLong("iisdayall"))) ? false : true));
                bean.setDayExc(("0".equals(String.valueOf(rs.getLong("iisdayexc"))) ? false : true));
                bean.setHourAll(("0".equals(String.valueOf(rs.getLong("iishourall"))) ? false : true));
                bean.setHourExc(("0".equals(String.valueOf(rs.getLong("iishourexc"))) ? false : true));
                bean.setMinuteAll(("0".equals(String.valueOf(rs.getLong("iisminuteall"))) ? false : true));
                bean.setMinuteExc(("0".equals(String.valueOf(rs.getLong("iisminuteexc"))) ? false : true));
                bean.setMonthAll(("0".equals(String.valueOf(rs.getLong("iismonthall"))) ? false : true));
                bean.setMonthExc(("0".equals(String.valueOf(rs.getLong("iismonthexc"))) ? false : true));
                bean.setWeekdayAll(("0".equals(String.valueOf(rs.getLong("iisweekdayall"))) ? false : true));
                bean.setWeekdayExc(("0".equals(String.valueOf(rs.getLong("iisweekdayexc"))) ? false : true));
                bean.setActInfoId(new Long(rs.getLong("iactinfoid")));
                bean.setCalName(rs.getString("icalname"));
                bean.setCalType(rs.getInt("icaltype"));
                bean.setUseFlowCal(("0".equals(String.valueOf(rs.getLong("iuseflowcal"))) ? false : true));
                repRet.add(bean);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repRet;
    }

    /**
     * 获取附件
     * 
     * @param sql
     * @return
     */
    public final static RepAttachment getAttment ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepAttachment attachment = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                attachment = new RepAttachment();
                attachment.setContentId(new Long(rs.getLong("icontentid")));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return attachment;
    }

    /**
     * 获取附件
     * 
     * @param sql
     * @return
     */
    public final static RepAttachment getAttachment ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        RepAttachment attachment = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);

            while (rs.next())
            {
                attachment = new RepAttachment();
                attachment.setId(new Long(rs.getLong("iid")));
                attachment.setContentId(new Long(rs.getLong("icontentid")));
                attachment.setFlowId(new Long(rs.getLong("iflowid")));
                attachment.setName(rs.getString("iname"));
                attachment.setSize(rs.getLong("isize"));
                attachment.setUpdateTime(rs.getLong("iupdatetime"));
                attachment.setUpdateUserFullName(rs.getString("iupdateuser"));
                attachment.setUpdateUserId(new Long(rs.getLong("iupdateuserid")));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return attachment;
    }

    /**
     * 获取AttachmentList
     * 
     * @param sql
     * @return
     */
    public final static List getAttachmentList ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepAttachment attachment = new RepAttachment();
                attachment.setId(new Long(rs.getLong("iid")));
                attachment.setContentId(new Long(rs.getLong("icontentid")));
                attachment.setFlowId(new Long(rs.getLong("iflowid")));
                attachment.setName(rs.getString("iname"));
                attachment.setSize(rs.getLong("isize"));
                attachment.setUpdateTime(rs.getLong("iupdatetime"));
                attachment.setUpdateUserFullName(rs.getString("iupdateuser"));
                attachment.setUpdateUserId(new Long(rs.getLong("iupdateuserid")));
                list.add(attachment);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    static public final List findRepAppLogTextEntity ( String sql, int type )
    {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List list = new ArrayList();
        Map result = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                RepAppLogRecord ralr = new RepAppLogRecord();
                ralr.setDetailId(new Long(rs.getLong("idetailid")));
                ralr.setFlowId(new Long(rs.getLong("iflowid")));
                ralr.setFlowInsName(rs.getString("iflowinsname"));
                ralr.setActName(rs.getString("iactname"));
                ralr.setFlowName(rs.getString("iflowname"));
                ralr.setId(new Long(rs.getLong("aiid")));
                ralr.setLogContent(rs.getString("aicontent"));
                ralr.setTaskId(new Long(rs.getLong("itaskid")));
                ralr.setPrjName(rs.getString("iprjname"));
                ralr.setTime(rs.getLong("itime"));
                ralr.setType(rs.getInt("itype"));
                ralr.setUserFullName(rs.getString("iuser"));
                ralr.setUserId(new Long(rs.getLong("iuserid")));
                result.put("appLogRecord", ralr);

                TextEntity textEntity = new TextEntity();
                textEntity.setId(new Long(rs.getLong("tiid")));
                textEntity.setText(rs.getString("cicontent"));
                textEntity.setNull(rs.getInt("inull") == 1 ? true : false);
                result.put("textEntity", textEntity);
                list.add(result);
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return list;
    }

    /**
     * 获取出错任务
     * 
     * @param sql
     * @return
     */
    static public final RepErrorTask getErrorTask ( String sql, Connection conn )
    {

        Statement stmt = null;
        ResultSet rs = null;
        RepErrorTask repErrorTask = null;
        try
        {
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repErrorTask = new RepErrorTask();
                repErrorTask.setActivatedTime(new Long(rs.getLong("IACTIVATEDTIME")));
                repErrorTask.setCreatedTime(new Long(rs.getLong("ICREATEDTIME")));
                repErrorTask.setEndTime(new Long(rs.getLong("IENDTIME")));
                repErrorTask.setActId(new Long(rs.getLong("IACTID")));
                repErrorTask.setActName(rs.getString("IACTNAME"));
                repErrorTask.setDefaultTaskOwnerId(new Long(rs.getLong("IDEFAULTTASKOWNERID")));
                repErrorTask.setDefaultTaskOwnerName(rs.getString("IDEFAULTTASKOWNERNAME"));
                repErrorTask.setDelegateState(rs.getByte("IDELEGATESTATE"));
                if (rs.getInt("IDELEGATETOASSIGNEESONLY") == 0)
                {
                    repErrorTask.setDelegateToAssigneesOnly(false);
                } else
                {
                    repErrorTask.setDelegateToAssigneesOnly(true);
                }
                repErrorTask.setDescription(rs.getString("IDESCRIPTION"));
                repErrorTask.setFlowId(new Long(rs.getLong("IFLOWID")));
                repErrorTask.setFlowInstanceName(rs.getString("IFLOWINSTANCENAME"));
                repErrorTask.setFlowName(rs.getString("IFLOWNAME"));
                if (rs.getInt("IISFORWARDTOASSIGNEESONLY") == 0)
                {
                    repErrorTask.setForwardToAssigneesOnly(false);
                } else
                {
                    repErrorTask.setForwardToAssigneesOnly(true);
                }
                repErrorTask.setId(new Long(rs.getLong("IID")));
                repErrorTask.setLastOperationId(new Long(rs.getLong("IOPERATIONID")));
                repErrorTask.setDelegatingDetailId(new Long(rs.getLong("IDELEGATINGID")));
                repErrorTask.setProjectName(rs.getString("IPROJECTNAME"));
                repErrorTask.setTaskOwnerId(new Long(rs.getLong("ITASKOWNERID")));
                repErrorTask.setTaskOwnerName(rs.getString("ITASKOWNERNAME"));
                repErrorTask.setTaskState(rs.getByte("ITASKSTATE"));
                if (rs.getInt("ISUSEFLOWDEFINEASSIGNESS") == 0)
                {
                    repErrorTask.setUseFlowDefineAssignees(false);
                } else
                {
                    repErrorTask.setUseFlowDefineAssignees(true);
                }
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repErrorTask;
    }

    /**
     * 获取授权详细信息
     * 
     * @param sql
     * @return
     */
    public static final RepDelegatingDetail getDelegatingDetail ( String sql, Connection conn )
    {

        Statement stmt = null;
        ResultSet rs = null;
        RepDelegatingDetail delegatingDetail = null;
        try
        {
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                delegatingDetail = new RepDelegatingDetail();
                delegatingDetail.setId(new Long(rs.getLong("IID")));
                delegatingDetail.setDelegateDescription(rs.getString("IDELEGATEDESCRIPTION"));
                delegatingDetail.setDelegatedTime(new Long(rs.getLong("IDELEGATEDTIME")));
                delegatingDetail.setDelegatorId(new Long(rs.getLong("IDELEGATORID")));
                delegatingDetail.setDelegatorFullName(rs.getString("IDELEGATORFULLNAME"));
                delegatingDetail.setFinishedTime(new Long(rs.getLong("IFINISHEDTIME")));
                delegatingDetail.setDelegateeFullName(rs.getString("IDELEGATEEFULLNAME"));
                delegatingDetail.setDelegateeId(new Long(rs.getLong("IDELEGATEEID")));
                delegatingDetail.setOperationId(new Long(rs.getLong("IOPERATIONID")));
                delegatingDetail.setErrorTaskId(new Long(rs.getLong("IERRORTASKID")));
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return delegatingDetail;
    }

    public static final RepErrorTaskOperation getErrorTaskOperation ( String sql, Connection conn )
    {

        Statement stmt = null;
        ResultSet rs = null;
        RepErrorTaskOperation repOperation = null;
        try
        {
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next())
            {
                repOperation = new RepErrorTaskOperation();
                repOperation.setId(new Long(rs.getLong("IID")));
                repOperation.setDescription(rs.getString("IDESCRIPTION"));
                repOperation.setFlowState(rs.getInt("IFLOWSTATE"));
                repOperation.setHandlMethod(rs.getInt("IHANDLMETHOD"));
                repOperation.setTargetActId(rs.getInt("ITARGETACTID"));
                repOperation.setTargetActName(rs.getString("ITARGETACTNAME"));
                repOperation.setWaitingSecond(rs.getLong("IWAITINGSECOND"));
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            try
            {
                DBResource.closePSRS(rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        return repOperation;
    }

    // add by le_wang
    public static final List queryAllRoleUser ()
    {
        List roles = new ArrayList();
        String sql = "SELECT T1.IID,T2.INAME FROM IEAI_USER T1,IEAI_ROLE T2,IEAI_USERINHERIT T3  WHERE T1.IID=T3.IUSERID AND T2.IID=T3.IROLEID";
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement stmt = null;
            Connection conn = null;
            ResultSet rs = null;

            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                stmt = conn.prepareStatement(sql);
                rs = stmt.executeQuery();

                while (rs.next())
                {
                    RepRole repRole = new RepRole();
                    repRole.setId(new Long(rs.getLong("iid")));
                    repRole.setName(rs.getString("INAME"));
                    roles.add(repRole);
                }
                stmt.close();
                rs.close();
                break;
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            } finally
            {
                try
                {
                    DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                } catch (Exception e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
            }
        }
        return roles;
    }
    
    /**
     * 
     * <li>Description:解析毫秒字符串为日期格式</li> 
     * <AUTHOR>
     * 2019年5月17日 
     * @param str
     * @return
     * return String
     */
    public static String parseStrToTime(String str){
        String time = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if( null != str && !"".equals(str) && !"0".equals(str) && !"null".equals(str) ){
            Date date = new Date(Long.parseLong(str));
            time = sdf.format(date);
        }
        return time;
    }
    

    /**
     * 
     * <li>Description:对接esm 追加用户绑定角色</li> 
     * <AUTHOR>
     * 2021年3月5日 
     * @param userid
     * @param roleid
     * @return
     * @throws RepositoryException
     * return Map
     */
    public static Map saveUserInheritenceESM ( int userid, Integer roleid ) throws RepositoryException
    {
        boolean flag = false;
        Map<String, Connection> mapCon = new HashMap<String, Connection>();
        Map<String, String> mapConSuccess = new HashMap<String, String>();
        Map mapmsg = new HashMap();
        String msg = "";
        List<RepUserInHeritBack> list = UserManager.getInstance().querydelUserInheritence(userid);
        List<RepUserInHeritBack> listdel = new ArrayList<RepUserInHeritBack>();
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        String insertSql = "INSERT INTO IEAI_USERINHERIT (IROLEID,IUSERID,IISEXTEND) VALUES(?,?,?)";
        String isextendSql="select max(iisextend) iisextend from IEAI_USERINHERIT where iroleId=?";
        //查询本条记录是否已存在
        String queryCount = "SELECT COUNT(1) AS COUNT FROM IEAI_USERINHERIT WHERE IUSERID=? AND IROLEID =?";
        long count=0;
        try
        {
            mapCon = ProjectManager.getInstance().getMapDBsourceList();
        } catch (PackException e)
        {
            msg = "获取数据源失败";
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        if (mapCon.isEmpty())
        {
        } else
        {

            for (Map.Entry<String, Connection> mapConnection : mapCon.entrySet())
            {
                try
                {
                    con = mapConnection.getValue();
                    ps = con.prepareStatement(queryCount);
                    ps.setLong(1, userid);
                    ps.setLong(2, roleid);
                    rs=ps.executeQuery();
                    while(rs.next()) {
                        count=rs.getLong("COUNT");
                    }
                    ps = con.prepareStatement(isextendSql);
                    ps.setLong(1, roleid);
                    rs=ps.executeQuery();
                    int iisextend=0;
                    while(rs.next()) {
                        iisextend=rs.getInt("iisextend");
                    }
                    if(count==0) {
                        ps = con.prepareStatement(insertSql);
                        ps.setLong(1, roleid);
                        ps.setLong(2, userid);
                        ps.setInt(3, iisextend);
                        ps.executeUpdate();
                    }
                    
                    con.commit();
                    flag = true;
                    mapConSuccess.put(mapConnection.getKey(), mapConnection.getKey());
                } catch (Exception e)
                {
                    try
                    {
                        if (null != con)
                            con.rollback();
                    } catch (SQLException e1)
                    {
                        _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " error ", e);

                    }
                    msg = "多写数据源失败";
                    flag = false;
                    EngineRepositotyJdbc.getInstance().removeUserHierDbResource(list, listdel, mapConSuccess);
                    break;
                } finally
                {
                    try
                    {
                        DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                        DBResource.closeConn(con, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                    } catch (Exception e)
                    {
                        _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    }

                }
            }

        }
        mapmsg.put("msg", msg);
        mapmsg.put("flag", flag);
        return mapmsg;
    }
    
    /**
     * 
     * <li>Description:cz查询用户信息</li> 
     * <AUTHOR>
     * 2021年5月21日 
     * @param sql
     * @param type
     * @return
     * return RepUser
     */
    static public final RepUser queryCzUser ( String sql, String loginName,int type)
    {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        RepUser repUser = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(type);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, loginName);
            rs = stmt.executeQuery();
            while (rs.next())
            {
                repUser =new RepUser();
                repUser.setId(new Long(rs.getLong("iid")));
                repUser.setLoginName(rs.getString("iloginName"));
                repUser.setFullName(rs.getString("ifullname"));
                repUser.setEmail(rs.getString("iemail"));
                repUser.setTelephone(rs.getString("itelephone"));
                repUser.setDepartment(rs.getString("idepartment"));
                repUser.setLocked(rs.getInt("ilocked") == 0 ? false : true);
                repUser.setUserType(rs.getInt("itype"));
            }
            stmt.close();
            rs.close();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }finally{
            try{
                DBResource.closeConn(conn, rs, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log); 
            } catch (Exception e)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);  
            } 
        }
        return repUser;
        
    }

    /**
     * 查询组织表是否有数据
     *
     * @param conn
     * @return
     */
    static public int getDeptCount(Connection conn) {
        int num = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select count(*) from ieai_orgmanagement where iparentid=0";
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                num = rs.getInt(1);
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDeptCount", _log);
        }

        return num;
    }
}