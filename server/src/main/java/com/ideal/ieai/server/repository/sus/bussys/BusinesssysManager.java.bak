package com.ideal.ieai.server.repository.sus.bussys;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.DataCenterOperationManage;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.model.ProjectBean;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class BusinesssysManager
{
    static Logger _logger = Logger.getLogger(BusinesssysManager.class);

    public Map getBusinesssys ( Businesssys businesssys ) throws RepositoryException
    {
        Map map = new LinkedHashMap();
        List<Businesssys> resultList = new ArrayList<Businesssys>();
        Connection conn = null;
        PreparedStatement instanceStat = null;
        ResultSet instanceRS = null;
        PreparedStatement countStat = null;
        ResultSet countRS = null;

        String sql = null;
        String sqlCount = null;
        int intCount = 0;
        StringBuffer buff = new StringBuffer();
        String sqlWhere = "ISVALIDATE=1"; // 没有被逻辑删除的sql

        if (DBManager.Orcl_Faimily())
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        } else if (JudgeDB.IEAI_DB_TYPE==2)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        } else        if (JudgeDB.IEAI_DB_TYPE==3)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        } 

        buff.append(" SELECT IID ,INAME ,ISVALIDATE ");
        buff.append("   FROM IEAI_PROJECT");
        sql = buff.toString();

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getBusinesssys", _logger, Constants.IEAI_SUS);
                    instanceStat = conn.prepareStatement(sql);
                    instanceRS = instanceStat.executeQuery();

                    while (instanceRS.next())
                    {
                        Businesssys bean = new Businesssys();
                        bean.setIid(instanceRS.getString("IID"));
                        bean.setIbusnes_sys_name(instanceRS.getString("INAME"));
                        bean.setIsvalidate(instanceRS.getString("ISVALIDATE"));
                        resultList.add(bean);
                    }

                    countStat = conn.prepareStatement(sqlCount);
                    countRS = countStat.executeQuery();
                    while (countRS.next())
                    {
                        intCount = countRS.getInt("CNT");
                    }
                    map.put("total", intCount);
                    map.put("dataList", resultList);
                } catch (SQLException sqlexception)
                {
                    sqlexception.printStackTrace();
                } finally
                {
                    try
                    {
                        if (null != instanceRS)
                        {
                            instanceRS.close();
                            instanceRS = null;
                        }
                        if (null != instanceStat)
                        {
                            instanceStat.close();
                            instanceStat = null;
                        }
                        if (null != countRS)
                        {
                            countRS.close();
                            countRS = null;
                        }
                        if (null != countStat)
                        {
                            countStat.close();
                            countStat = null;
                        }
                        if (null != conn)
                        {
                            conn.close();
                            conn = null;
                        }
                        conn = null;
                    } catch (SQLException sqlexception)
                    {
                        sqlexception.printStackTrace();
                    }
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                repositoryexception.printStackTrace();
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    /**
     * 
     * @Title: getBusinesssys
     * @Description: TODO(查询业务系统列表)
     * @param businesssys 查询参数集合
     * @param start
     * @param limit
     * @param returnMap 权限过滤所需参数map
     * @param needFilter 是否需要权限过滤
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     */
    public Map getBusinesssys ( Businesssys businesssys, int start, int limit, Map returnMap, Integer needFilter, long userId ) throws RepositoryException
    {
        Map map = new LinkedHashMap();
        List<Businesssys> resultList = new ArrayList<Businesssys>();
        Connection conn = null;
        PreparedStatement instanceStat = null;
        ResultSet instanceRS = null;
        PreparedStatement countStat = null;
        ResultSet countRS = null;

        String sql = null;
        String sqlCount = null;
        int intCount = 0;
        StringBuffer buff = new StringBuffer();
        
        String orderBy = " ORDER BY INAME ASC ";
        // String sqlWhere =
        // "WHERE IID in (SELECT MAX(IID) FROM   IEAI_PROJECT WHERE  IID IS NOT NULL AND ISVALIDATE=1 AND    IGROUPID=3 AND  INAME !='所有变更管理业务系统' AND    UPPER(INAME) !='EXCELACTEXECMODELSUS' GROUP BY INAME) ";
        // // 没有被逻辑删除的sql";
        boolean flag = ServerEnv.getInstance().getBooleanConfig(Environment.RESOURCEGROUP_ISFILTERSUS, false);

        String sqlWhere = "";
        if (flag)
        {
            sqlWhere = "WHERE A.IGROUPID=B.GROUPID AND IID in (SELECT MAX(IID) FROM   IEAI_PROJECT WHERE  IID IS NOT NULL AND ISVALIDATE=? AND    IGROUPID in (SELECT IGROUPMESID FROM IEAI_RESGROUP_MODEL) AND INAME NOT LIKE '所有%业务系统' AND UPPER(INAME) NOT LIKE 'EXCELACTEXECMODEL%'  GROUP BY INAME) "; // 没有被逻辑删除的sql";
        } else
        {
            sqlWhere = "WHERE IID in (SELECT MAX(IID) FROM   IEAI_PROJECT WHERE  IID IS NOT NULL AND ISVALIDATE=? AND    IGROUPID=? AND  INAME !=? AND  INAME !=? AND    UPPER(INAME) !=? GROUP BY INAME) "; // 没有被逻辑删除的sql";
        }
        if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
        {
            // sqlWhere += " AND IID LIKE '%" + businesssys.getIid() + "%'";
            sqlWhere += " AND IID LIKE ? ";
        }
        if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
        {
            // sqlWhere += " AND INAME LIKE '%" + businesssys.getIbusnes_sys_name() + "%'";
            sqlWhere += " AND INAME LIKE ? ";
        }
        if ((businesssys.getSys_name_or_short_name() != null)
                && !"".equals(businesssys.getSys_name_or_short_name()))
        {
            // sqlWhere += " AND (INAME LIKE '%" + businesssys.getSys_name_or_short_name()
            // + "%' OR  ISYSTEMCODE LIKE '%" + businesssys.getSys_name_or_short_name()
            // + "%')";
            if (businesssys.getSys_name_or_short_name().contains("%"))
            {
                sqlWhere += " AND (INAME LIKE ?escape '/' OR  ISYSTEMCODE LIKE ?escape '/' )";
            } else
            {
                sqlWhere += " AND (INAME LIKE ? OR  ISYSTEMCODE LIKE ? )";
            }
        }
        // 是否需要权限过滤
        if (needFilter != null && needFilter == 1)
        {
            
            /**百信获取权限方式调整 **/
            List<String> sysNames = new ArrayList<String>();
            List<ProjectBean> listPb;
            boolean hasAllSkill = false;
            try
            {
                listPb = DataCenterOperationManage.getInstance().getProject(null, userId, Constants.IEAI_SUS);
                for (ProjectBean projectBean : listPb)
                {
                    if (projectBean.getLastId() < 0)
                    {
                        hasAllSkill = true;
                        break;
                    }
                    sysNames.add("'" + projectBean.getiName() + "'");
                }
            } catch (RepositoryException e2)
            {
                e2.printStackTrace();
            }
            /**百信获取权限方式调整 **/
            if (!hasAllSkill)
            {
                String instanceName=StringUtils.join(sysNames, ",");
                if("".equals(instanceName))
                {
                    instanceName="''";
                }
                sqlWhere += " AND INAME IN (" + instanceName + ")  ";
            }
        }
        
        if (DBManager.Orcl_Faimily())
        {
            if (flag)
            {
                sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT a ,IEAI_GROUPMESSAGE B " + sqlWhere);
            } else
            {
                sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            }
            buff.append(" SELECT *    FROM (SELECT ROWNUM AS RM, W5.*          FROM ( ");
        } else if (JudgeDB.IEAI_DB_TYPE==2)
        {
            if (flag)
            {
                sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT a ,IEAI_GROUPMESSAGE B " + sqlWhere);
            } else
            {
                sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            }
            buff.append(" SELECT *    FROM (SELECT ROWNUMBER() OVER() AS RM , W5.*          FROM ( ");
        }else if (JudgeDB.IEAI_DB_TYPE==3)
        {
            if (flag)
            {
                sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT a ,IEAI_GROUPMESSAGE B " + sqlWhere);
            } else
            {
                sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            }
            buff.append(" SELECT *    FROM (SELECT  W5.*          FROM ( ");
        }

        if (flag)
        {
            buff.append(" SELECT IID ,INAME ,ISVALIDATE ,ISYSTEMCODE,IGROUPID,GROUPNAME  ");
            buff.append("   FROM IEAI_PROJECT  A,IEAI_GROUPMESSAGE B ");
        }else{
            buff.append(" SELECT IID ,INAME ,ISVALIDATE ,ISYSTEMCODE  ");
            buff.append("   FROM IEAI_PROJECT ");
        }
        
        buff.append(sqlWhere);
        buff.append(orderBy);
        if (DBManager.Orcl_Faimily())
        {
            buff.append("               )W5          )  WHERE RM >" + start + "  AND RM <= " + (start+limit));
        } else if (JudgeDB.IEAI_DB_TYPE==2)
        {
            buff.append("               )W5          )  WHERE RM >" + start + "  AND RM <= " + (start+limit));
        }else if (JudgeDB.IEAI_DB_TYPE==3)
        {
            buff.append("               )W5          )tt limit " + start + " ," + limit);
        }

        sql = buff.toString();

        for (int i = 0;i<10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getBusinesssys", _logger, Constants.IEAI_SUS);
                    instanceStat = conn.prepareStatement(sql);
                    int index = 0;
                    instanceStat.setInt(++index, 1);
                    if (flag)
                    {
                        // instanceStat.setInt(++index, 3);
                        // instanceStat.setString(++index, "所有变更管理业务系统");
                        // instanceStat.setString(++index, "EXCELACTEXECMODELSUS");
                    } else
                    {
                        instanceStat.setInt(++index, 3);
                        instanceStat.setString(++index, "所有变更管理业务系统");
                        instanceStat.setString(++index, "EXCELACTEXECMODELSUS");
                        instanceStat.setString(++index, "ExcelActExecModelSUSAS400");
                    }
                    if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
                    {
                        instanceStat.setString(++index, "%" + businesssys.getIid() + "%");
                    }
                    if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
                    {
                        instanceStat.setString(++index, "%" + businesssys.getIbusnes_sys_name() + "%");
                    }
                    if ((businesssys.getSys_name_or_short_name() != null)
                            && !"".equals(businesssys.getSys_name_or_short_name()))
                    {
                        if (businesssys.getSys_name_or_short_name().contains("%"))
                        {
                            String shortname = businesssys.getSys_name_or_short_name();
                            shortname = shortname.replaceAll("%", "/%");
                            shortname = "%" + shortname + "%";
                            instanceStat.setString(++index, shortname);
                            instanceStat.setString(++index, shortname);
                        } else
                        {
                            instanceStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                            instanceStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                        }
                    }
                    instanceRS = instanceStat.executeQuery();
                    while (instanceRS.next())
                    {
                        Businesssys bean = new Businesssys();
                        bean.setIid(instanceRS.getString("IID"));
                        bean.setIbusnes_sys_name(instanceRS.getString("INAME"));
                        bean.setIsvalidate(instanceRS.getString("ISVALIDATE"));
                        bean.setIbusnes_sys_short_name(instanceRS.getString("ISYSTEMCODE"));
                        if (flag)
                        {
                            bean.setIbusnes_sys_type(instanceRS.getString("GROUPNAME"));
                        } else
                        {
                            //
                        }
                        resultList.add(bean);
                    }

                    countStat = conn.prepareStatement(sqlCount);
                    index = 0;
                    countStat.setInt(++index, 1);
                    if(flag){
                        // countStat.setInt(++index, 3);
                        // countStat.setString(++index, "所有变更管理业务系统");
                        // countStat.setString(++index, "EXCELACTEXECMODELSUS");
                    }else{
                        countStat.setInt(++index, 3);
                        countStat.setString(++index, "所有变更管理业务系统");
                        countStat.setString(++index, "EXCELACTEXECMODELSUS");
                        countStat.setString(++index, "ExcelActExecModelSUSAS400");
                    }
                    if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
                    {
                        countStat.setString(++index, "%" + businesssys.getIid() + "%");
                    }
                    if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
                    {
                        countStat.setString(++index, "%" + businesssys.getIbusnes_sys_name() + "%");
                    }
                    if ((businesssys.getSys_name_or_short_name() != null)
                            && !"".equals(businesssys.getSys_name_or_short_name()))
                    {
                        if (businesssys.getSys_name_or_short_name().contains("%"))
                        {
                            String shortname = businesssys.getSys_name_or_short_name();
                            shortname = shortname.replaceAll("%", "/%");
                            shortname = "%" + shortname + "%";
                            countStat.setString(++index, shortname);
                            countStat.setString(++index, shortname);
                        } else
                        {
                            countStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                            countStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                        }
                    }
                    countRS = countStat.executeQuery();
                    while (countRS.next())
                    {
                        intCount = countRS.getInt("CNT");
                    }
                    map.put("total", intCount);
                    map.put("dataList", resultList);
                } catch (SQLException sqlexception)
                {
                    sqlexception.printStackTrace();
                } finally
                {
                    DBResource.closePSRS(instanceRS, instanceStat, "getBusinesssys", _logger);
                    DBResource.closeConn(conn, countRS, countStat, "getBusinesssys", _logger);
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                DBRetryUtil.waitForNextTry(i, repositoryexception);
            }
        }
        return map;
    }

    public Map queryBusinesssys ( Businesssys businesssys ) throws RepositoryException
    {
        Map map = new LinkedHashMap();
        List<Businesssys> resultList = new ArrayList<Businesssys>();
        Connection conn = null;
        PreparedStatement instanceStat = null;
        ResultSet instanceRS = null;
        PreparedStatement countStat = null;
        ResultSet countRS = null;

        String sql = null;
        String sqlCount = null;
        int intCount = 0;
        StringBuffer buff = new StringBuffer();

        String sqlWhere = "WHERE IID IS NOT NULL AND ISVALIDATE=1"; // 没有被逻辑删除的";
        if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
        {
            sqlWhere = (sqlWhere + " AND IID LIKE '%" + businesssys.getIid() + "%'");
        }
        if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
        {
            sqlWhere = (sqlWhere + " AND INAME LIKE '%" + businesssys.getIbusnes_sys_name() + "%'");
        }

        if (DBManager.Orcl_Faimily())
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        } else if (JudgeDB.IEAI_DB_TYPE==2)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        }else  if (JudgeDB.IEAI_DB_TYPE==3)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        }

        buff.append(" SELECT IID ,INAME ,ISVALIDATE ");
        buff.append("   FROM IEAI_PROJECT");
        sql = buff.toString();

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("queryBusinesssys", _logger, Constants.IEAI_SUS);
                    instanceStat = conn.prepareStatement(sql);
                    instanceRS = instanceStat.executeQuery();

                    while (instanceRS.next())
                    {
                        Businesssys bean = new Businesssys();
                        bean.setIid(instanceRS.getString("IID"));
                        bean.setIbusnes_sys_name(instanceRS.getString("INAME"));
                        bean.setIsvalidate(instanceRS.getString("ISVALIDATE"));
                        resultList.add(bean);
                    }

                    countStat = conn.prepareStatement(sqlCount);
                    countRS = countStat.executeQuery();
                    while (countRS.next())
                    {
                        intCount = countRS.getInt("CNT");
                    }
                    map.put("total", intCount);
                    map.put("dataList", resultList);
                } catch (SQLException sqlexception)
                {
                    sqlexception.printStackTrace();
                } finally
                {
                    try
                    {
                        if (null != instanceRS)
                        {
                            instanceRS.close();
                            instanceRS = null;
                        }
                        if (null != instanceStat)
                        {
                            instanceStat.close();
                            instanceStat = null;
                        }
                        if (null != countRS)
                        {
                            countRS.close();
                            countRS = null;
                        }
                        if (null != countStat)
                        {
                            countStat.close();
                            countStat = null;
                        }
                        if (null != conn)
                        {
                            conn.close();
                            conn = null;
                        }
                        conn = null;
                    } catch (SQLException sqlexception)
                    {
                        sqlexception.printStackTrace();
                    }
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                repositoryexception.printStackTrace();
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    public Map queryBusinesssys ( Businesssys businesssys, int start, int limit ) throws RepositoryException
    {
        Map map = new LinkedHashMap();
        List<Businesssys> resultList = new ArrayList<Businesssys>();
        Connection conn = null;
        PreparedStatement instanceStat = null;
        ResultSet instanceRS = null;
        PreparedStatement countStat = null;
        ResultSet countRS = null;

        String sql = null;
        String sqlCount = null;
        int intCount = 0;
        StringBuffer buff = new StringBuffer();

        String sqlWhere = "WHERE IID IS NOT NULL AND ISVALIDATE=1"; // 没有被逻辑删除的";
        if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
        {
            sqlWhere = (sqlWhere + " AND IID LIKE '%" + businesssys.getIid() + "%'");
        }
        if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
        {
            sqlWhere = (sqlWhere + " AND INAME LIKE '%" + businesssys.getIbusnes_sys_name() + "%'");
        }

        if (DBManager.Orcl_Faimily())
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        } else if (JudgeDB.IEAI_DB_TYPE==2)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        }else  if (JudgeDB.IEAI_DB_TYPE==3)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b where   b.IID IS NOT NULL " + sqlWhere);
        } 

        buff.append(" SELECT *    FROM (SELECT ROWNUM AS RM, W5.*          FROM ( ");
        buff.append(" SELECT IID ,INAME ,ISVALIDATE ");
        buff.append("   FROM IEAI_PROJECT");
        buff.append(sqlWhere);
        buff.append("               )W5          )  WHERE RM >" + start + "  AND RM <= " + limit);
        sql = buff.toString();

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("queryBusinesssys", _logger, Constants.IEAI_SUS);
                    instanceStat = conn.prepareStatement(sql);
                    instanceRS = instanceStat.executeQuery();

                    while (instanceRS.next())
                    {
                        Businesssys bean = new Businesssys();
                        bean.setIid(instanceRS.getString("IID"));
                        bean.setIbusnes_sys_name(instanceRS.getString("INAME"));
                        bean.setIsvalidate(instanceRS.getString("ISVALIDATE"));
                        resultList.add(bean);
                    }

                    countStat = conn.prepareStatement(sqlCount);
                    countRS = countStat.executeQuery();
                    while (countRS.next())
                    {
                        intCount = countRS.getInt("CNT");
                    }
                    map.put("total", intCount);
                    map.put("dataList", resultList);
                } catch (SQLException sqlexception)
                {
                    sqlexception.printStackTrace();
                } finally
                {
                    try
                    {
                        if (null != instanceRS)
                        {
                            instanceRS.close();
                            instanceRS = null;
                        }
                        if (null != instanceStat)
                        {
                            instanceStat.close();
                            instanceStat = null;
                        }
                        if (null != countRS)
                        {
                            countRS.close();
                            countRS = null;
                        }
                        if (null != countStat)
                        {
                            countStat.close();
                            countStat = null;
                        }
                        if (null != conn)
                        {
                            conn.close();
                            conn = null;
                        }
                        conn = null;
                    } catch (SQLException sqlexception)
                    {
                        sqlexception.printStackTrace();
                    }
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                repositoryexception.printStackTrace();
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    public boolean saveBusinesssys ( List<Businesssys> businesssysList ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement instanceStat = null;

        long iid = 1;
        boolean flag = false;
        Businesssys businesssys = null;
        String sql = null;

        StringBuffer insrtBuff = new StringBuffer();
        StringBuffer updateBuff = new StringBuffer();
        insrtBuff.append(" INSERT INTO  IEAI_PROJECT (IID ,INAME ,ISYSTEMCODE)");
        insrtBuff.append(" VALUES (?,?,?)");
        updateBuff.append(" UPDATE  IEAI_PROJECT SET INAME=? ,ISYSTEMCODE=?");
        updateBuff.append("  WHERE IID=?");

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("saveBusinesssys", _logger, Constants.IEAI_SUS);
                    conn.setAutoCommit(false);
                    for (int j = 0; j < businesssysList.size(); j++)
                    {
                        businesssys = businesssysList.get(j);
                        if (null == businesssys.getIid())
                        {
                            sql = insrtBuff.toString();
                            iid = IdGenerator.createId("IEAI_PROJECT", conn);
                            instanceStat = conn.prepareStatement(sql);
                            instanceStat.setLong(1, iid);
                            instanceStat.setString(2, businesssys.getIbusnes_sys_name());
                            instanceStat.setString(3, businesssys.getIbusnes_sys_short_name());
                            instanceStat.executeUpdate();
                        } else
                        {
                            sql = updateBuff.toString();
                            instanceStat = conn.prepareStatement(sql);
                            instanceStat.setString(1, businesssys.getIbusnes_sys_name());
                            instanceStat.setString(2, businesssys.getIbusnes_sys_short_name());
                            instanceStat.setLong(3, Long.valueOf(businesssys.getIid()));
                            instanceStat.executeUpdate();
                        }
                    }
                    conn.commit();
                    flag = true;
                } catch (SQLException sqlexception)
                {
                    sqlexception.printStackTrace();
                } finally
                {
                    try
                    {
                        if (null != instanceStat)
                        {
                            instanceStat.close();
                            instanceStat = null;
                        }
                        if (null != conn)
                        {
                            conn.close();
                            conn = null;
                        }
                        conn = null;
                    } catch (SQLException sqlexception)
                    {
                        sqlexception.printStackTrace();
                    }
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                repositoryexception.printStackTrace();
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return flag;
    }

    public boolean deleteBusinesssys ( String iids ) throws RepositoryException
    {
        final int INVALIDATE = 0; // 逻辑删除标示
        Connection conn = null;
        PreparedStatement instanceStat = null;
        StringBuffer deleteBuff = new StringBuffer();
        deleteBuff.append(" UPDATE  IEAI_PROJECT SET ISVALIDATE=" + INVALIDATE + " WHERE  IID IN(" + iids + ")");
        boolean flag = false;
        String sql = null;

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("deleteBusinesssys", _logger, Constants.IEAI_SUS);
                    if (null != iids)
                    {
                        sql = deleteBuff.toString();
                        instanceStat = conn.prepareStatement(sql);
                        instanceStat.executeUpdate();
                        conn.commit();
                        flag = true;
                    }
                } catch (SQLException sqlexception)
                {
                    sqlexception.printStackTrace();
                } finally
                {
                    try
                    {
                        if (null != instanceStat)
                        {
                            instanceStat.close();
                            instanceStat = null;
                        }
                        if (null != conn)
                        {
                            conn.close();
                            conn = null;
                        }
                        conn = null;
                    } catch (SQLException sqlexception)
                    {
                        sqlexception.printStackTrace();
                    }
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                repositoryexception.printStackTrace();
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return flag;
    }
    public Businesssys getBusinesssysByName(String sysName , int type) throws RepositoryException{
        Businesssys result = null;
        String sql = "SELECT * FROM IEAI_PROJECT T WHERE T.ISVALIDATE=1 AND T.INAME=?  AND T.PROTYPE=?";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getBusinesssysByName", _logger, Constants.IEAI_SUS);
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, sysName);
                    ps.setInt(2, type);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        result = new Businesssys();
                        result.setIid(rs.getString("IID"));
                        result.setIbusnes_sys_name(rs.getString("INAME"));
                        result.setIbusnes_sys_short_name(rs.getString("ISYSTEMCODE"));
                        result.setIsvalidate(rs.getString("ISVALIDATE"));
                    }

                } catch (SQLException sqlexception)
                {
                    sqlexception.printStackTrace();
                } finally
                {
                    try
                    {
                        if (null != rs)
                        {
                            rs.close();
                            rs = null;
                        }
                        if (null != ps)
                        {
                            ps.close();
                            ps = null;
                        }
                        if (null != conn)
                        {
                            conn.close();
                            conn = null;
                        }
                        conn = null;
                    } catch (SQLException sqlexception)
                    {
                        sqlexception.printStackTrace();
                    }
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                repositoryexception.printStackTrace();
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return result;
    }

    /**
     * 
     * @Title: getBusinesssysForAPM   
     * @Description: 查询应用维护业务系统 Manager   
     * @param businesssys
     * @param start
     * @param limit
     * @param returnMap
     * @param needFilter
     * @param userId
     * @return
     * @throws RepositoryException      
     * @author: ming_li 
     * @date:   2019年3月27日 上午11:10:39
     */
    public Map<String,Object> getBusinesssysForAPM ( Businesssys businesssys, int start, int limit, Map returnMap, Integer needFilter,
            long userId ) throws RepositoryException
    {

        Map<String,Object> map = new LinkedHashMap<String,Object>();
        List<Businesssys> resultList = new ArrayList<Businesssys>();
        Connection conn = null;
        PreparedStatement instanceStat = null;
        ResultSet instanceRS = null;
        PreparedStatement countStat = null;
        ResultSet countRS = null;

        String sql = null;
        String sqlCount = null;
        int intCount = 0;
        StringBuilder buff = new StringBuilder();

        String orderBy = " ORDER BY INAME ASC ";

        String sqlWhere = "WHERE IID in (SELECT MAX(IID) FROM   IEAI_PROJECT WHERE  IID IS NOT NULL AND ISVALIDATE=? AND    IGROUPID=? AND  INAME !=? AND    UPPER(INAME) !=? GROUP BY INAME) ";
        if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
        {

            sqlWhere += " AND IID LIKE ? ";
        }
        if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
        {

            sqlWhere += " AND INAME LIKE ? ";
        }
        if ((businesssys.getSys_name_or_short_name() != null) && !"".equals(businesssys.getSys_name_or_short_name()))
        {

            if (businesssys.getSys_name_or_short_name().contains("%"))
            {
                sqlWhere += " AND (INAME LIKE ?escape '/' OR  ISYSTEMCODE LIKE ?escape '/' )";
            } else
            {
                sqlWhere += " AND (INAME LIKE ? OR  ISYSTEMCODE LIKE ? )";
            }
        }
        // 是否需要权限过滤
        if (needFilter != null && needFilter == 1)
        {

            /**百信获取权限方式调整 **/
            List<String> sysNames = new ArrayList<String>();
            List<ProjectBean> listPb;
            boolean hasAllSkill = false;
            try
            {
                listPb = DataCenterOperationManage.getInstance().getProject(null, userId, Constants.IEAI_APM);
                for (ProjectBean projectBean : listPb)
                {
                    if (projectBean.getLastId() < 0)
                    {
                        hasAllSkill = true;
                        break;
                    }
                    sysNames.add("'" + projectBean.getiName() + "'");
                }
            } catch (RepositoryException e2)
            {
                _logger.error("method：" + Thread.currentThread().getStackTrace()[1].getMethodName(), e2);
            }
            /**百信获取权限方式调整 **/
            if (!hasAllSkill)
            {
                String instanceName = StringUtils.join(sysNames, ",");
                if ("".equals(instanceName))
                {
                    instanceName = "''";
                }
                sqlWhere += " AND INAME IN (" + instanceName + ")  ";
            }
        }

        if (DBManager.Orcl_Faimily())
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            buff.append(" SELECT *    FROM (SELECT ROWNUM AS RM, W5.*          FROM ( ");
        } else if (JudgeDB.IEAI_DB_TYPE == 2)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            buff.append(" SELECT *    FROM (SELECT ROWNUMBER() OVER() AS RM , W5.*          FROM ( ");
        } else if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            buff.append(" SELECT *    FROM (SELECT  W5.*          FROM ( ");
        }

        buff.append(" SELECT IID ,INAME ,ISVALIDATE ,ISYSTEMCODE  ");
        buff.append("   FROM IEAI_PROJECT ");
        buff.append(sqlWhere);
        buff.append(orderBy);
        if (DBManager.Orcl_Faimily())
        {
            buff.append("               )W5          )  WHERE RM >" + start + "  AND RM <= " + (start + limit));
        } else if (JudgeDB.IEAI_DB_TYPE == 2)
        {
            buff.append("               )W5          )  WHERE RM >" + start + "  AND RM <= " + (start + limit));
        } else if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            buff.append("               )W5          )tt limit " + start + " ," + limit);
        }

        sql = buff.toString();

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getBusinesssysForAPM", _logger, Constants.IEAI_APM);
                    instanceStat = conn.prepareStatement(sql);
                    int index = 0;
                    instanceStat.setInt(++index, 1);
                    instanceStat.setInt(++index, 16);
                    instanceStat.setString(++index, "所有应用维护业务系统");
                    instanceStat.setString(++index, "EXCELACTEXECMODELSUS");
                    if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
                    {
                        instanceStat.setString(++index, "%" + businesssys.getIid() + "%");
                    }
                    if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
                    {
                        instanceStat.setString(++index, "%" + businesssys.getIbusnes_sys_name() + "%");
                    }
                    if ((businesssys.getSys_name_or_short_name() != null)
                            && !"".equals(businesssys.getSys_name_or_short_name()))
                    {
                        if (businesssys.getSys_name_or_short_name().contains("%"))
                        {
                            String shortname = businesssys.getSys_name_or_short_name();
                            shortname = shortname.replaceAll("%", "/%");
                            shortname = "%" + shortname + "%";
                            instanceStat.setString(++index, shortname);
                            instanceStat.setString(++index, shortname);
                        } else
                        {
                            instanceStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                            instanceStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                        }
                    }
                    instanceRS = instanceStat.executeQuery();
                    while (instanceRS.next())
                    {
                        Businesssys bean = new Businesssys();
                        bean.setIid(instanceRS.getString("IID"));
                        bean.setIbusnes_sys_name(instanceRS.getString("INAME"));
                        bean.setIsvalidate(instanceRS.getString("ISVALIDATE"));
                        bean.setIbusnes_sys_short_name(instanceRS.getString("ISYSTEMCODE"));
                        resultList.add(bean);
                    }

                    countStat = conn.prepareStatement(sqlCount);
                    index = 0;
                    countStat.setInt(++index, 1);
                    countStat.setInt(++index, 16);
                    countStat.setString(++index, "所有应用维护业务系统");
                    countStat.setString(++index, "EXCELACTEXECMODELSUS");
                    if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
                    {
                        countStat.setString(++index, "%" + businesssys.getIid() + "%");
                    }
                    if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
                    {
                        countStat.setString(++index, "%" + businesssys.getIbusnes_sys_name() + "%");
                    }
                    if ((businesssys.getSys_name_or_short_name() != null)
                            && !"".equals(businesssys.getSys_name_or_short_name()))
                    {
                        if (businesssys.getSys_name_or_short_name().contains("%"))
                        {
                            String shortname = businesssys.getSys_name_or_short_name();
                            shortname = shortname.replaceAll("%", "/%");
                            shortname = "%" + shortname + "%";
                            countStat.setString(++index, shortname);
                            countStat.setString(++index, shortname);
                        } else
                        {
                            countStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                            countStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                        }
                    }
                    countRS = countStat.executeQuery();
                    while (countRS.next())
                    {
                        intCount = countRS.getInt("CNT");
                    }
                    map.put("total", intCount);
                    map.put("dataList", resultList);
                } catch (SQLException sqlexception)
                {
                    _logger.error("method：" + Thread.currentThread().getStackTrace()[1].getMethodName(), sqlexception);
                } finally
                {
                    DBResource.closePSRS(instanceRS, instanceStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _logger);
                    DBResource.closeConn(conn, countRS, countStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _logger);
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                DBRetryUtil.waitForNextTry(i, repositoryexception);
            }
        }
        return map;

    }
    
    /**
     * 
     * <li>Description:查询AZ切换业务系统 Manager</li> 
     * <AUTHOR>
     * 2020年8月6日 
     * @param businesssys
     * @param start
     * @param limit
     * @param returnMap
     * @param needFilter
     * @param userId
     * @return
     * @throws RepositoryException
     * return Map<String,Object>
     */
    public Map<String,Object> getBusinesssysForAZ ( Businesssys businesssys, int start, int limit, Map<String,Object> returnMap, Integer needFilter,
            long userId ) throws RepositoryException
    {

        Map<String,Object> map = new LinkedHashMap<String,Object>();
        List<Businesssys> resultList = new ArrayList<Businesssys>();
        Connection conn = null;
        PreparedStatement instanceStat = null;
        ResultSet instanceRS = null;
        PreparedStatement countStat = null;
        ResultSet countRS = null;

        String sql = null;
        String sqlCount = null;
        int intCount = 0;
        StringBuilder buff = new StringBuilder();

        String orderBy = " ORDER BY INAME ASC ";

        String sqlWhere = "WHERE IID in (SELECT MAX(IID) FROM   IEAI_PROJECT WHERE  IID IS NOT NULL AND ISVALIDATE=? AND    IGROUPID=? AND  INAME !=? AND    UPPER(INAME) !=? GROUP BY INAME) ";
        if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
        {

            sqlWhere += " AND IID LIKE ? ";
        }
        if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
        {

            sqlWhere += " AND INAME LIKE ? ";
        }
        if ((businesssys.getSys_name_or_short_name() != null) && !"".equals(businesssys.getSys_name_or_short_name()))
        {

            if (businesssys.getSys_name_or_short_name().contains("%"))
            {
                sqlWhere += " AND (INAME LIKE ?escape '/' OR  ISYSTEMCODE LIKE ?escape '/' )";
            } else
            {
                sqlWhere += " AND (INAME LIKE ? OR  ISYSTEMCODE LIKE ? )";
            }
        }
        // 是否需要权限过滤
        if (needFilter != null && needFilter == 1)
        {

            /**百信获取权限方式调整 **/
            List<String> sysNames = new ArrayList<String>();
            List<ProjectBean> listPb;
            boolean hasAllSkill = false;
            try
            {
                listPb = DataCenterOperationManage.getInstance().getProject(null, userId, Constants.IEAI_AZ);
                for (ProjectBean projectBean : listPb)
                {
                    if (projectBean.getLastId() < 0)
                    {
                        hasAllSkill = true;
                        break;
                    }
                    sysNames.add("'" + projectBean.getiName() + "'");
                }
            } catch (RepositoryException e2)
            {
                _logger.error("method：" + Thread.currentThread().getStackTrace()[1].getMethodName(), e2);
            }
            /**百信获取权限方式调整 **/
            if (!hasAllSkill)
            {
                String instanceName = StringUtils.join(sysNames, ",");
                if ("".equals(instanceName))
                {
                    instanceName = "''";
                }
                sqlWhere += " AND INAME IN (" + instanceName + ")  ";
            }
        }

        if (DBManager.Orcl_Faimily())
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            buff.append(" SELECT *    FROM (SELECT ROWNUM AS RM, W5.*          FROM ( ");
        } else if (JudgeDB.IEAI_DB_TYPE == 2)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            buff.append(" SELECT *    FROM (SELECT ROWNUMBER() OVER() AS RM , W5.*          FROM ( ");
        } else if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlCount = ("SELECT count(*) as CNT FROM IEAI_PROJECT b  " + sqlWhere);
            buff.append(" SELECT *    FROM (SELECT  W5.*          FROM ( ");
        }

        buff.append(" SELECT IID ,INAME ,ISVALIDATE ,ISYSTEMCODE  ");
        buff.append("   FROM IEAI_PROJECT ");
        buff.append(sqlWhere);
        buff.append(orderBy);
        if (DBManager.Orcl_Faimily())
        {
            buff.append("               )W5          )  WHERE RM >" + start + "  AND RM <= " + (start + limit));
        } else if (JudgeDB.IEAI_DB_TYPE == 2)
        {
            buff.append("               )W5          )  WHERE RM >" + start + "  AND RM <= " + (start + limit));
        } else if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            buff.append("               )W5          )tt limit " + start + " ," + limit);
        }

        sql = buff.toString();

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getBusinesssysForAZ", _logger, Constants.IEAI_AZ);
                    instanceStat = conn.prepareStatement(sql);
                    int index = 0;
                    instanceStat.setInt(++index, 1);
                    instanceStat.setInt(++index, 40);
                    instanceStat.setString(++index, "所有AZ切换业务系统");
                    instanceStat.setString(++index, "EXCELACTEXECMODELSUS");
                    if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
                    {
                        instanceStat.setString(++index, "%" + businesssys.getIid() + "%");
                    }
                    if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
                    {
                        instanceStat.setString(++index, "%" + businesssys.getIbusnes_sys_name() + "%");
                    }
                    if ((businesssys.getSys_name_or_short_name() != null)
                            && !"".equals(businesssys.getSys_name_or_short_name()))
                    {
                        if (businesssys.getSys_name_or_short_name().contains("%"))
                        {
                            String shortname = businesssys.getSys_name_or_short_name();
                            shortname = shortname.replaceAll("%", "/%");
                            shortname = "%" + shortname + "%";
                            instanceStat.setString(++index, shortname);
                            instanceStat.setString(++index, shortname);
                        } else
                        {
                            instanceStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                            instanceStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                        }
                    }
                    instanceRS = instanceStat.executeQuery();
                    while (instanceRS.next())
                    {
                        Businesssys bean = new Businesssys();
                        bean.setIid(instanceRS.getString("IID"));
                        bean.setIbusnes_sys_name(instanceRS.getString("INAME"));
                        bean.setIsvalidate(instanceRS.getString("ISVALIDATE"));
                        bean.setIbusnes_sys_short_name(instanceRS.getString("ISYSTEMCODE"));
                        resultList.add(bean);
                    }

                    countStat = conn.prepareStatement(sqlCount);
                    index = 0;
                    countStat.setInt(++index, 1);
                    countStat.setInt(++index, 40);
                    countStat.setString(++index, "所有AZ切换业务系统");
                    countStat.setString(++index, "EXCELACTEXECMODELSUS");
                    if ((businesssys.getIid() != null) && !"".equals(businesssys.getIid()))
                    {
                        countStat.setString(++index, "%" + businesssys.getIid() + "%");
                    }
                    if ((businesssys.getIbusnes_sys_name() != null) && !"".equals(businesssys.getIbusnes_sys_name()))
                    {
                        countStat.setString(++index, "%" + businesssys.getIbusnes_sys_name() + "%");
                    }
                    if ((businesssys.getSys_name_or_short_name() != null)
                            && !"".equals(businesssys.getSys_name_or_short_name()))
                    {
                        if (businesssys.getSys_name_or_short_name().contains("%"))
                        {
                            String shortname = businesssys.getSys_name_or_short_name();
                            shortname = shortname.replaceAll("%", "/%");
                            shortname = "%" + shortname + "%";
                            countStat.setString(++index, shortname);
                            countStat.setString(++index, shortname);
                        } else
                        {
                            countStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                            countStat.setString(++index, "%" + businesssys.getSys_name_or_short_name() + "%");
                        }
                    }
                    countRS = countStat.executeQuery();
                    while (countRS.next())
                    {
                        intCount = countRS.getInt("CNT");
                    }
                    map.put("total", intCount);
                    map.put("dataList", resultList);
                } catch (SQLException sqlexception)
                {
                    _logger.error("method：" + Thread.currentThread().getStackTrace()[1].getMethodName(), sqlexception);
                } finally
                {
                    DBResource.closePSRS(instanceRS, instanceStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _logger);
                    DBResource.closeConn(conn, countRS, countStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _logger);
                }
                break;
            } catch (RepositoryException repositoryexception)
            {
                DBRetryUtil.waitForNextTry(i, repositoryexception);
            }
        }
        return map;
    }
}