<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{YYYY-MM-dd HH:mm:ss} [%t] %-5p %c{1}:%L - %msg%n" />
        </Console>

        <!-- SQL Logger Console Appender -->
        <Console name="SqlConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{YYYY-MM-dd HH:mm:ss} [SQL] %msg%n" />
        </Console>

        <RollingFile name="RollingFile" filename="log/test.log"
            filepattern="${logPath}/%d{YYYYMMddHHmmss}-fargo.log">
            <PatternLayout pattern="%d{YYYY-MM-dd HH:mm:ss} [%t] %-5p %c{1}:%L - %msg%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB" />
            </Policies>
            <DefaultRolloverStrategy max="20" />
        </RollingFile>

        <!-- SQL Logger File Appender -->
        <RollingFile name="SqlRollingFile" filename="log/sql.log"
            filepattern="${logPath}/%d{YYYYMMddHHmmss}-sql.log">
            <PatternLayout pattern="%d{YYYY-MM-dd HH:mm:ss} [SQL] %msg%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB" />
            </Policies>
            <DefaultRolloverStrategy max="20" />
        </RollingFile>

    </Appenders>
    <Loggers>
        <!-- SQL Logger Configuration -->
        <Logger name="SQL_LOGGER" level="info" additivity="false">
            <AppenderRef ref="SqlConsole" />
            <AppenderRef ref="SqlRollingFile" />
        </Logger>

        <Root level="info">
            <AppenderRef ref="Console" />
            <AppenderRef ref="RollingFile" />
        </Root>
    </Loggers>
</Configuration>