<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{YYYY-MM-dd HH:mm:ss} [%t] %-5p %c{1}:%L - %msg%n" />
        </Console>
 
        <RollingFile name="RollingFile" filename="log/test.log"
            filepattern="${logPath}/%d{YYYYMMddHHmmss}-fargo.log">
            <PatternLayout pattern="%d{YYYY-MM-dd HH:mm:ss} [%t] %-5p %c{1}:%L - %msg%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB" />
            </Policies>
            <DefaultRolloverStrategy max="20" />
        </RollingFile>

        <!-- SQL日志单独文件 -->
        <RollingFile name="SqlFile" filename="log/sql.log"
            filepattern="log/sql-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="%d{YYYY-MM-dd HH:mm:ss} - %msg%n" />
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="50 MB" />
            </Policies>
            <DefaultRolloverStrategy max="30" />
        </RollingFile>
 
    </Appenders>
    <Loggers>
        <!-- SQL日志单独配置 -->
        <Logger name="SQL_LOGGER" level="info" additivity="false">
            <AppenderRef ref="SqlFile" />
        </Logger>

        <Root level="info">
            <AppenderRef ref="Console" />
            <AppenderRef ref="RollingFile" />
        </Root>
    </Loggers>
</Configuration>