package com.ideal.ieai.server.repository.db;

import java.sql.*;

/**
 * 测试 LoggableConnection 的 SQL 日志功能
 */
public class LoggableConnectionTest {
    
    private static final org.apache.log4j.Logger sqlLogger = org.apache.log4j.Logger.getLogger("SQL_LOGGER");
    
    public static void main(String[] args) {
        // 测试日志记录器是否正常工作
        System.out.println("开始测试 SQL 日志功能...");
        
        // 直接测试日志记录器
        sqlLogger.info("测试 SQL 日志记录器是否正常工作");
        sqlLogger.info("SQL executed by: TestClass.main() [Line: 15]\nMethod: test\nSQL: SELECT * FROM test_table\n--------------------------------------------------");
        
        // 模拟 LoggableConnection 的日志输出格式
        String callerInfo = "LoggableConnectionTest.main() [Line: 20]";
        String method = "prepareStatement";
        String sql = "SELECT id, name FROM users WHERE status = ?";
        
        String logMessage = String.format("SQL executed by: %s\nMethod: %s\nSQL: %s\n--------------------------------------------------",
                callerInfo, method, sql);
        sqlLogger.info(logMessage);
        
        // 测试不同类型的 SQL 语句
        testDifferentSqlTypes();
        
        System.out.println("SQL 日志测试完成。请检查控制台输出和 log/sql.log 文件。");
    }
    
    private static void testDifferentSqlTypes() {
        String[] testSqls = {
            "INSERT INTO users (name, email) VALUES (?, ?)",
            "UPDATE users SET last_login = ? WHERE id = ?",
            "DELETE FROM users WHERE status = 'inactive'",
            "SELECT COUNT(*) FROM orders WHERE created_date > ?",
            "CALL sp_update_user_stats(?)"
        };
        
        String[] methods = {
            "PreparedStatement.executeUpdate",
            "PreparedStatement.executeUpdate", 
            "Statement.executeUpdate",
            "PreparedStatement.executeQuery",
            "CallableStatement.execute"
        };
        
        for (int i = 0; i < testSqls.length; i++) {
            String callerInfo = "LoggableConnectionTest.testDifferentSqlTypes() [Line: " + (40 + i) + "]";
            String logMessage = String.format("SQL executed by: %s\nMethod: %s\nSQL: %s\n--------------------------------------------------",
                    callerInfo, methods[i], testSqls[i]);
            sqlLogger.info(logMessage);
        }
    }
}
